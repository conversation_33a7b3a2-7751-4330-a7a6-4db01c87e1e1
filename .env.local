# Smart Off Plan - Environment Variables
# Copy this file to .env.local and fill in your actual values

# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://search-listings-production.up.railway.app
NEXT_PUBLIC_API_KEY=reelly-680ffbdd-FEuCzeraBCN5dtByJeLb8AeCesrTvlFz
NEXT_PUBLIC_API_VERSION=v1

# Authentication (if using)
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=http://localhost:3000

# Database (if using)
DATABASE_URL=your-database-url-here

# External APIs
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your-ga-id

# Email Service (if using)
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-app-password

# File Upload (if using)
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-secret

# Development
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_CHAT=false
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=false
