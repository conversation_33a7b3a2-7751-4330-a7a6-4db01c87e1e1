/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22PageErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CErrorMonitoringProvider.tsx%22%2C%22ids%22%3A%5B%22ErrorMonitoringProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22PageErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CErrorMonitoringProvider.tsx%22%2C%22ids%22%3A%5B%22ErrorMonitoringProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(ssr)/./src/components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ErrorMonitoringProvider.tsx */ \"(ssr)/./src/components/providers/ErrorMonitoringProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ToastProvider.tsx */ \"(ssr)/./src/components/providers/ToastProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22PageErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CErrorMonitoringProvider.tsx%22%2C%22ids%22%3A%5B%22ErrorMonitoringProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ComponentErrorBoundary: () => (/* binding */ ComponentErrorBoundary),\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   PageErrorBoundary: () => (/* binding */ PageErrorBoundary),\n/* harmony export */   SectionErrorBoundary: () => (/* binding */ SectionErrorBoundary),\n/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _lib_error_handler__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/error-handler */ \"(ssr)/./src/lib/error-handler.ts\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,PageErrorBoundary,SectionErrorBoundary,ComponentErrorBoundary,withErrorBoundary auto */ \n\n\n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.handleRetry = ()=>{\n            this.setState({\n                hasError: false,\n                error: null,\n                errorInfo: null,\n                errorId: null\n            });\n        };\n        this.handleReload = ()=>{\n            window.location.reload();\n        };\n        this.handleGoHome = ()=>{\n            window.location.href = \"/\";\n        };\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null,\n            errorId: null\n        };\n    }\n    static getDerivedStateFromError(error) {\n        // Update state so the next render will show the fallback UI\n        return {\n            hasError: true,\n            error,\n            errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        // Log the error\n        (0,_lib_error_handler__WEBPACK_IMPORTED_MODULE_3__.logError)(error, errorInfo);\n        // Update state with error info\n        this.setState({\n            errorInfo\n        });\n        // Call custom error handler if provided\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        }\n        // Send to error monitoring service in production\n        if (false) {}\n    }\n    render() {\n        if (this.state.hasError) {\n            // Custom fallback UI\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            // Default fallback UI based on level\n            return this.renderErrorUI();\n        }\n        return this.props.children;\n    }\n    renderErrorUI() {\n        const { level = \"component\", showDetails = false } = this.props;\n        const { error, errorInfo, errorId } = this.state;\n        // Component-level error (minimal UI)\n        if (level === \"component\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-4 bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-8 h-8 text-red-500 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-700 mb-2\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: this.handleRetry,\n                            size: \"sm\",\n                            variant: \"outline\",\n                            className: \"text-red-700 border-red-300 hover:bg-red-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, this);\n        }\n        // Section-level error (medium UI)\n        if (level === \"section\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-[200px] bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-md px-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-12 h-12 text-red-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-red-800 mb-2\",\n                            children: \"Oops! Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600 mb-4\",\n                            children: \"We encountered an error while loading this section. Please try again.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: this.handleRetry,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    className: \"text-red-700 border-red-300 hover:bg-red-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Try Again\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: this.handleReload,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    className: \"text-red-700 border-red-300 hover:bg-red-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Reload Page\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this);\n        }\n        // Page-level error (full UI)\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-ivory flex items-center justify-center px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-20 h-20 text-red-500 mx-auto mb-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-soft-brown mb-4\",\n                                children: \"Something went wrong\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-warm-gray text-lg mb-6\",\n                                children: \"We're sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            showDetails && error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                className: \"text-left bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                        className: \"cursor-pointer text-red-700 font-medium mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Error Details\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-red-600 space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Error ID:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" \",\n                                                    errorId\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Message:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" \",\n                                                    error.message\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 19\n                                            }, this),\n                                             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Stack:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                className: \"mt-1 text-xs bg-red-100 p-2 rounded overflow-auto\",\n                                                                children: error.stack\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    errorInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Component Stack:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                className: \"mt-1 text-xs bg-red-100 p-2 rounded overflow-auto\",\n                                                                children: errorInfo.componentStack\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: this.handleRetry,\n                                className: \"bg-soft-brown hover:bg-deep-brown text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Try Again\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: this.handleReload,\n                                variant: \"outline\",\n                                className: \"border-soft-brown text-soft-brown hover:bg-soft-brown hover:text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Reload Page\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: this.handleGoHome,\n                                variant: \"outline\",\n                                className: \"border-soft-brown text-soft-brown hover:bg-soft-brown hover:text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Go Home\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 text-sm text-warm-gray\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"If this problem persists, please contact our support team at\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:<EMAIL>\",\n                                        className: \"text-soft-brown hover:underline\",\n                                        children: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this),\n                            errorId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2\",\n                                children: [\n                                    \"Reference ID:\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"bg-gray-100 px-1 rounded\",\n                                        children: errorId\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, this);\n    }\n}\n// Specialized Error Boundaries\nfunction PageErrorBoundary({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n        level: \"page\",\n        showDetails: \"development\" === \"development\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 260,\n        columnNumber: 5\n    }, this);\n}\nfunction SectionErrorBoundary({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n        level: \"section\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 270,\n        columnNumber: 10\n    }, this);\n}\nfunction ComponentErrorBoundary({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n        level: \"component\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 274,\n        columnNumber: 10\n    }, this);\n}\n// HOC for wrapping components with error boundaries\nfunction withErrorBoundary(Component, level = \"component\") {\n    const WrappedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n            level: level,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 283,\n            columnNumber: 5\n        }, this);\n    WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n    return WrappedComponent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/ErrorMonitoringProvider.tsx":
/*!**************************************************************!*\
  !*** ./src/components/providers/ErrorMonitoringProvider.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorMonitoringProvider: () => (/* binding */ ErrorMonitoringProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/error-monitoring */ \"(ssr)/./src/lib/error-monitoring.ts\");\n/* __next_internal_client_entry_do_not_use__ ErrorMonitoringProvider auto */ \n\n\nfunction ErrorMonitoringProvider({ children }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize error monitoring\n        if (false) {}\n    }, []);\n    // Set user when authentication state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This would typically come from your auth context/store\n        // For now, we'll check localStorage for user info\n        const checkUser = ()=>{\n            try {\n                const userStr = localStorage.getItem(\"user\");\n                if (userStr) {\n                    const user = JSON.parse(userStr);\n                    if (user.id) {\n                        (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.setUser)(user.id);\n                        (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.addContext)(\"user\", {\n                            id: user.id,\n                            email: user.email,\n                            role: user.role\n                        });\n                    }\n                }\n            } catch (e) {\n            // Ignore parsing errors\n            }\n        };\n        checkUser();\n        // Listen for storage changes (user login/logout)\n        window.addEventListener(\"storage\", checkUser);\n        return ()=>{\n            window.removeEventListener(\"storage\", checkUser);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/ErrorMonitoringProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/ToastProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/ToastProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider auto */ \n\nfunction ToastProvider() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n        position: \"top-right\",\n        reverseOrder: false,\n        gutter: 8,\n        containerClassName: \"\",\n        containerStyle: {},\n        toastOptions: {\n            // Default options for all toasts\n            duration: 5000,\n            style: {\n                background: \"#fff\",\n                color: \"#333\",\n                border: \"1px solid #e5e7eb\",\n                borderRadius: \"8px\",\n                boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\",\n                fontSize: \"14px\",\n                fontFamily: \"var(--font-inter), Inter, sans-serif\",\n                maxWidth: \"400px\"\n            },\n            // Success toasts\n            success: {\n                style: {\n                    background: \"#f0fdf4\",\n                    color: \"#166534\",\n                    border: \"1px solid #bbf7d0\"\n                },\n                iconTheme: {\n                    primary: \"#22c55e\",\n                    secondary: \"#f0fdf4\"\n                }\n            },\n            // Error toasts\n            error: {\n                style: {\n                    background: \"#fef2f2\",\n                    color: \"#dc2626\",\n                    border: \"1px solid #fecaca\"\n                },\n                iconTheme: {\n                    primary: \"#ef4444\",\n                    secondary: \"#fef2f2\"\n                },\n                duration: 6000\n            },\n            // Loading toasts\n            loading: {\n                style: {\n                    background: \"#fefce8\",\n                    color: \"#a16207\",\n                    border: \"1px solid #fef3c7\"\n                },\n                iconTheme: {\n                    primary: \"#eab308\",\n                    secondary: \"#fefce8\"\n                }\n            }\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\providers\\\\ToastProvider.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/ToastProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9 rounded-md\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/error-handler.ts":
/*!**********************************!*\
  !*** ./src/lib/error-handler.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorType: () => (/* binding */ ErrorType),\n/* harmony export */   classifyError: () => (/* binding */ classifyError),\n/* harmony export */   dismissAllToasts: () => (/* binding */ dismissAllToasts),\n/* harmony export */   dismissToast: () => (/* binding */ dismissToast),\n/* harmony export */   formatValidationErrors: () => (/* binding */ formatValidationErrors),\n/* harmony export */   logError: () => (/* binding */ logError),\n/* harmony export */   showError: () => (/* binding */ showError),\n/* harmony export */   showInfo: () => (/* binding */ showInfo),\n/* harmony export */   showLoading: () => (/* binding */ showLoading),\n/* harmony export */   showSuccess: () => (/* binding */ showSuccess),\n/* harmony export */   withRetry: () => (/* binding */ withRetry)\n/* harmony export */ });\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _error_monitoring__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error-monitoring */ \"(ssr)/./src/lib/error-monitoring.ts\");\n\n\nvar ErrorType;\n(function(ErrorType) {\n    ErrorType[\"NETWORK\"] = \"NETWORK\";\n    ErrorType[\"VALIDATION\"] = \"VALIDATION\";\n    ErrorType[\"AUTHENTICATION\"] = \"AUTHENTICATION\";\n    ErrorType[\"AUTHORIZATION\"] = \"AUTHORIZATION\";\n    ErrorType[\"NOT_FOUND\"] = \"NOT_FOUND\";\n    ErrorType[\"SERVER\"] = \"SERVER\";\n    ErrorType[\"UNKNOWN\"] = \"UNKNOWN\";\n})(ErrorType || (ErrorType = {}));\n// Error classification\nconst classifyError = (error)=>{\n    const timestamp = new Date();\n    // Handle API errors\n    if (error && typeof error === \"object\" && \"status\" in error) {\n        const apiError = error;\n        switch(apiError.status){\n            case 400:\n                return {\n                    type: \"VALIDATION\",\n                    message: apiError.message || \"Invalid request data\",\n                    code: apiError.code,\n                    details: apiError.details,\n                    timestamp\n                };\n            case 401:\n                return {\n                    type: \"AUTHENTICATION\",\n                    message: \"Please log in to continue\",\n                    code: apiError.code,\n                    timestamp\n                };\n            case 403:\n                return {\n                    type: \"AUTHORIZATION\",\n                    message: \"You do not have permission to perform this action\",\n                    code: apiError.code,\n                    timestamp\n                };\n            case 404:\n                return {\n                    type: \"NOT_FOUND\",\n                    message: \"The requested resource was not found\",\n                    code: apiError.code,\n                    timestamp\n                };\n            case 422:\n                return {\n                    type: \"VALIDATION\",\n                    message: apiError.message || \"Validation failed\",\n                    code: apiError.code,\n                    details: apiError.details,\n                    timestamp\n                };\n            case 500:\n            case 502:\n            case 503:\n            case 504:\n                return {\n                    type: \"SERVER\",\n                    message: \"Server error. Please try again later.\",\n                    code: apiError.code,\n                    timestamp\n                };\n            default:\n                return {\n                    type: \"UNKNOWN\",\n                    message: apiError.message || \"An unexpected error occurred\",\n                    code: apiError.code,\n                    timestamp\n                };\n        }\n    }\n    // Handle network errors\n    if (error && error.code === \"NETWORK_ERROR\") {\n        return {\n            type: \"NETWORK\",\n            message: \"Network error. Please check your connection.\",\n            code: \"NETWORK_ERROR\",\n            timestamp\n        };\n    }\n    // Handle generic errors\n    if (error instanceof Error) {\n        return {\n            type: \"UNKNOWN\",\n            message: error.message || \"An unexpected error occurred\",\n            timestamp\n        };\n    }\n    // Fallback\n    return {\n        type: \"UNKNOWN\",\n        message: \"An unexpected error occurred\",\n        timestamp\n    };\n};\n// Error display functions\nconst showError = (error, customMessage)=>{\n    const appError = classifyError(error);\n    const message = customMessage || appError.message;\n    // Log error in development\n    if (true) {\n        console.error(\"\\uD83D\\uDEA8 Error:\", appError);\n    }\n    // Send to error monitoring system\n    (0,_error_monitoring__WEBPACK_IMPORTED_MODULE_1__.captureError)(error, {\n        customMessage,\n        appError\n    }, \"error\");\n    // Show toast notification\n    switch(appError.type){\n        case \"VALIDATION\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 6000,\n                icon: \"⚠️\"\n            });\n            break;\n        case \"AUTHENTICATION\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 8000,\n                icon: \"\\uD83D\\uDD10\"\n            });\n            break;\n        case \"AUTHORIZATION\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 6000,\n                icon: \"\\uD83D\\uDEAB\"\n            });\n            break;\n        case \"NOT_FOUND\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 5000,\n                icon: \"\\uD83D\\uDD0D\"\n            });\n            break;\n        case \"NETWORK\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 8000,\n                icon: \"\\uD83D\\uDCE1\"\n            });\n            break;\n        case \"SERVER\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 8000,\n                icon: \"\\uD83D\\uDD27\"\n            });\n            break;\n        default:\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 5000,\n                icon: \"❌\"\n            });\n    }\n    return appError;\n};\n// Success notifications\nconst showSuccess = (message, duration = 4000)=>{\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].success(message, {\n        duration,\n        icon: \"✅\"\n    });\n};\n// Info notifications\nconst showInfo = (message, duration = 4000)=>{\n    (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(message, {\n        duration,\n        icon: \"ℹ️\"\n    });\n};\n// Loading notifications\nconst showLoading = (message = \"Loading...\")=>{\n    return react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].loading(message, {\n        icon: \"⏳\"\n    });\n};\n// Dismiss specific toast\nconst dismissToast = (toastId)=>{\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].dismiss(toastId);\n};\n// Dismiss all toasts\nconst dismissAllToasts = ()=>{\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].dismiss();\n};\n// Error boundary helper\nconst logError = (error, errorInfo)=>{\n    console.error(\"\\uD83D\\uDEA8 Error Boundary:\", error, errorInfo);\n    // Send to error monitoring system\n    (0,_error_monitoring__WEBPACK_IMPORTED_MODULE_1__.captureError)(error, errorInfo, \"error\");\n};\n// Validation error helpers\nconst formatValidationErrors = (errors)=>{\n    const messages = Object.entries(errors).map(([field, fieldErrors])=>{\n        const fieldName = field.charAt(0).toUpperCase() + field.slice(1);\n        return `${fieldName}: ${fieldErrors.join(\", \")}`;\n    });\n    return messages.join(\"\\n\");\n};\n// Retry helper\nconst withRetry = async (fn, maxRetries = 3, delay = 1000)=>{\n    let lastError;\n    for(let i = 0; i <= maxRetries; i++){\n        try {\n            return await fn();\n        } catch (error) {\n            lastError = error;\n            if (i === maxRetries) {\n                throw error;\n            }\n            // Wait before retrying\n            await new Promise((resolve)=>setTimeout(resolve, delay * Math.pow(2, i)));\n        }\n    }\n    throw lastError;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/error-handler.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/error-monitoring.ts":
/*!*************************************!*\
  !*** ./src/lib/error-monitoring.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addContext: () => (/* binding */ addContext),\n/* harmony export */   captureError: () => (/* binding */ captureError),\n/* harmony export */   capturePerformanceMetric: () => (/* binding */ capturePerformanceMetric),\n/* harmony export */   captureUserAction: () => (/* binding */ captureUserAction),\n/* harmony export */   errorMonitor: () => (/* binding */ errorMonitor),\n/* harmony export */   setUser: () => (/* binding */ setUser)\n/* harmony export */ });\n// Error monitoring and tracking system\nclass ErrorMonitor {\n    constructor(){\n        this.maxRetries = 3;\n        this.retryDelay = 1000;\n        this.queue = [];\n        this.isOnline = true;\n        this.sessionId = this.generateSessionId();\n        this.isEnabled =  false || process.env.NEXT_PUBLIC_ENABLE_ERROR_MONITORING === \"true\";\n        this.apiEndpoint = process.env.NEXT_PUBLIC_ERROR_MONITORING_ENDPOINT || \"/api/errors\";\n        if (false) {}\n    }\n    generateSessionId() {\n        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    setupGlobalErrorHandlers() {\n        // Handle JavaScript errors\n        window.addEventListener(\"error\", (event)=>{\n            this.captureError(event.error || new Error(event.message), {\n                filename: event.filename,\n                lineno: event.lineno,\n                colno: event.colno,\n                type: \"javascript\"\n            });\n        });\n        // Handle unhandled promise rejections\n        window.addEventListener(\"unhandledrejection\", (event)=>{\n            this.captureError(new Error(event.reason), {\n                type: \"unhandled_promise_rejection\",\n                reason: event.reason\n            });\n        });\n        // Handle React errors (if using React Error Boundary)\n        const originalConsoleError = console.error;\n        console.error = (...args)=>{\n            if (args[0] && typeof args[0] === \"string\" && args[0].includes(\"React\")) {\n                this.captureError(new Error(args.join(\" \")), {\n                    type: \"react_error\",\n                    args\n                });\n            }\n            originalConsoleError.apply(console, args);\n        };\n    }\n    setupNetworkMonitoring() {\n        // Monitor network status\n        window.addEventListener(\"online\", ()=>{\n            this.isOnline = true;\n            this.processQueue();\n        });\n        window.addEventListener(\"offline\", ()=>{\n            this.isOnline = false;\n        });\n        // Monitor fetch requests\n        const originalFetch = window.fetch;\n        window.fetch = async (...args)=>{\n            const startTime = performance.now();\n            try {\n                const response = await originalFetch(...args);\n                const endTime = performance.now();\n                const duration = endTime - startTime;\n                // Log slow requests\n                if (duration > 5000) {\n                    this.capturePerformanceMetric(\"slow_request\", duration, \"ms\", {\n                        url: args[0],\n                        status: response.status\n                    });\n                }\n                // Log failed requests\n                if (!response.ok) {\n                    this.captureError(new Error(`HTTP ${response.status}: ${response.statusText}`), {\n                        type: \"http_error\",\n                        url: args[0],\n                        status: response.status,\n                        statusText: response.statusText\n                    });\n                }\n                return response;\n            } catch (error) {\n                this.captureError(error, {\n                    type: \"network_error\",\n                    url: args[0]\n                });\n                throw error;\n            }\n        };\n    }\n    setupPerformanceMonitoring() {\n        // Monitor page load performance\n        window.addEventListener(\"load\", ()=>{\n            setTimeout(()=>{\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    this.capturePerformanceMetric(\"page_load_time\", navigation.loadEventEnd - navigation.fetchStart, \"ms\");\n                    this.capturePerformanceMetric(\"dom_content_loaded\", navigation.domContentLoadedEventEnd - navigation.fetchStart, \"ms\");\n                    this.capturePerformanceMetric(\"first_byte\", navigation.responseStart - navigation.fetchStart, \"ms\");\n                }\n                // Monitor Core Web Vitals\n                this.monitorCoreWebVitals();\n            }, 0);\n        });\n    }\n    monitorCoreWebVitals() {\n        // This would typically use the web-vitals library\n        // For now, we'll implement basic monitoring\n        // Monitor Largest Contentful Paint (LCP)\n        const observer = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            const lastEntry = entries[entries.length - 1];\n            if (lastEntry) {\n                this.capturePerformanceMetric(\"lcp\", lastEntry.startTime, \"ms\");\n            }\n        });\n        try {\n            observer.observe({\n                entryTypes: [\n                    \"largest-contentful-paint\"\n                ]\n            });\n        } catch (e) {\n        // LCP not supported\n        }\n        // Monitor Cumulative Layout Shift (CLS)\n        let clsValue = 0;\n        const clsObserver = new PerformanceObserver((list)=>{\n            for (const entry of list.getEntries()){\n                if (!entry.hadRecentInput) {\n                    clsValue += entry.value;\n                }\n            }\n        });\n        try {\n            clsObserver.observe({\n                entryTypes: [\n                    \"layout-shift\"\n                ]\n            });\n            // Report CLS on page unload\n            window.addEventListener(\"beforeunload\", ()=>{\n                this.capturePerformanceMetric(\"cls\", clsValue, \"score\");\n            });\n        } catch (e) {\n        // CLS not supported\n        }\n    }\n    startQueueProcessor() {\n        setInterval(()=>{\n            if (this.isOnline && this.queue.length > 0) {\n                this.processQueue();\n            }\n        }, 5000) // Process queue every 5 seconds\n        ;\n    }\n    async processQueue() {\n        if (this.queue.length === 0) return;\n        const batch = this.queue.splice(0, 10) // Process up to 10 errors at a time\n        ;\n        try {\n            await this.sendErrorBatch(batch);\n        } catch (error) {\n            // Put errors back in queue for retry\n            this.queue.unshift(...batch);\n            console.warn(\"Failed to send error batch:\", error);\n        }\n    }\n    async sendErrorBatch(errors) {\n        if (!this.isEnabled) return;\n        const payload = {\n            errors,\n            sessionId: this.sessionId,\n            userId: this.userId,\n            timestamp: new Date().toISOString()\n        };\n        const response = await fetch(this.apiEndpoint, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            throw new Error(`Failed to send errors: ${response.status}`);\n        }\n    }\n    captureError(error, context, level = \"error\") {\n        if (!this.isEnabled) {\n            console.error(\"Error captured:\", error, context);\n            return;\n        }\n        const errorEvent = {\n            id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            timestamp: new Date().toISOString(),\n            message: error.message,\n            stack: error.stack,\n            url: window.location.href,\n            userAgent: navigator.userAgent,\n            userId: this.userId,\n            sessionId: this.sessionId,\n            level,\n            context,\n            fingerprint: this.generateFingerprint(error)\n        };\n        this.queue.push(errorEvent);\n        // Send immediately for critical errors\n        if (level === \"error\" && this.isOnline) {\n            this.sendErrorBatch([\n                errorEvent\n            ]).catch(()=>{\n            // Error will remain in queue for retry\n            });\n        }\n    }\n    capturePerformanceMetric(name, value, unit, context) {\n        if (!this.isEnabled) return;\n        const metric = {\n            id: `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            timestamp: new Date().toISOString(),\n            name,\n            value,\n            unit,\n            context\n        };\n        // Send performance metrics to a separate endpoint\n        fetch(\"/api/metrics\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(metric)\n        }).catch(()=>{\n        // Ignore metric sending failures\n        });\n    }\n    captureUserAction(action, element, context) {\n        if (!this.isEnabled) return;\n        const userAction = {\n            id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            timestamp: new Date().toISOString(),\n            action,\n            element,\n            page: window.location.pathname,\n            userId: this.userId,\n            sessionId: this.sessionId,\n            context\n        };\n        // Send user actions to analytics endpoint\n        fetch(\"/api/analytics\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(userAction)\n        }).catch(()=>{\n        // Ignore analytics sending failures\n        });\n    }\n    setUser(userId) {\n        this.userId = userId;\n    }\n    addContext(key, value) {\n        // Add global context that will be included with all errors\n        if (false) {}\n    }\n    generateFingerprint(error) {\n        // Generate a fingerprint for grouping similar errors\n        const message = error.message || \"Unknown error\";\n        const stack = error.stack || \"\";\n        const firstStackLine = stack.split(\"\\n\")[1] || \"\";\n        return btoa(`${message}:${firstStackLine}`).substr(0, 16);\n    }\n    getSessionId() {\n        return this.sessionId;\n    }\n    isMonitoringEnabled() {\n        return this.isEnabled;\n    }\n}\n// Create global instance\nconst errorMonitor = new ErrorMonitor();\n// Convenience functions\nconst captureError = (error, context, level)=>{\n    errorMonitor.captureError(error, context, level);\n};\nconst capturePerformanceMetric = (name, value, unit, context)=>{\n    errorMonitor.capturePerformanceMetric(name, value, unit, context);\n};\nconst captureUserAction = (action, element, context)=>{\n    errorMonitor.captureUserAction(action, element, context);\n};\nconst setUser = (userId)=>{\n    errorMonitor.setUser(userId);\n};\nconst addContext = (key, value)=>{\n    errorMonitor.addContext(key, value);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/error-monitoring.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3NtYXJ0LW9mZi1wbGFuLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0d7026c43fe2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21hcnQtb2ZmLXBsYW4vLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzPzY2MzciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwZDcwMjZjNDNmZTJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_variableName_playfair___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-playfair\"}],\"variableName\":\"playfair\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-playfair\\\"}],\\\"variableName\\\":\\\"playfair\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_variableName_playfair___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_variableName_playfair___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n/* harmony import */ var _components_providers_ToastProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/ToastProvider */ \"(rsc)/./src/components/providers/ToastProvider.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(rsc)/./src/components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_providers_ErrorMonitoringProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/ErrorMonitoringProvider */ \"(rsc)/./src/components/providers/ErrorMonitoringProvider.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Smart Off Plan - Premium Dubai Real Estate\",\n    description: \"Your trusted partner for Dubai developments. Connecting international investors with premium off-plan opportunities.\",\n    keywords: \"Dubai real estate, off-plan properties, luxury developments, property investment, Dubai properties\",\n    authors: [\n        {\n            name: \"Smart Off Plan\"\n        }\n    ],\n    creator: \"Smart Off Plan\",\n    publisher: \"Smart Off Plan\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://smartoffplan.ae\"),\n    openGraph: {\n        title: \"Smart Off Plan - Premium Dubai Real Estate\",\n        description: \"Your trusted partner for Dubai developments. Connecting international investors with premium off-plan opportunities.\",\n        url: \"https://smartoffplan.ae\",\n        siteName: \"Smart Off Plan\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Smart Off Plan - Premium Dubai Real Estate\"\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Smart Off Plan - Premium Dubai Real Estate\",\n        description: \"Your trusted partner for Dubai developments. Connecting international investors with premium off-plan opportunities.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_variableName_playfair___WEBPACK_IMPORTED_MODULE_6___default().variable)}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#d4af37\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, maximum-scale=5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)} antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ErrorMonitoringProvider__WEBPACK_IMPORTED_MODULE_4__.ErrorMonitoringProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__.PageErrorBoundary, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ToastProvider__WEBPACK_IMPORTED_MODULE_2__.ToastProvider, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ComponentErrorBoundary: () => (/* binding */ e3),
/* harmony export */   ErrorBoundary: () => (/* binding */ e0),
/* harmony export */   PageErrorBoundary: () => (/* binding */ e1),
/* harmony export */   SectionErrorBoundary: () => (/* binding */ e2),
/* harmony export */   withErrorBoundary: () => (/* binding */ e4)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\smart-off-plan\src\components\ErrorBoundary.tsx#ErrorBoundary`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\smart-off-plan\src\components\ErrorBoundary.tsx#PageErrorBoundary`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\smart-off-plan\src\components\ErrorBoundary.tsx#SectionErrorBoundary`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\smart-off-plan\src\components\ErrorBoundary.tsx#ComponentErrorBoundary`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\smart-off-plan\src\components\ErrorBoundary.tsx#withErrorBoundary`);


/***/ }),

/***/ "(rsc)/./src/components/providers/ErrorMonitoringProvider.tsx":
/*!**************************************************************!*\
  !*** ./src/components/providers/ErrorMonitoringProvider.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ErrorMonitoringProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\smart-off-plan\src\components\providers\ErrorMonitoringProvider.tsx#ErrorMonitoringProvider`);


/***/ }),

/***/ "(rsc)/./src/components/providers/ToastProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/ToastProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\smart-off-plan\src\components\providers\ToastProvider.tsx#ToastProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/lucide-react","vendor-chunks/tailwind-merge","vendor-chunks/react-hot-toast","vendor-chunks/class-variance-authority","vendor-chunks/goober","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();