"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/properties/route";
exports.ids = ["app/api/properties/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Black_Desktop_smart_off_plan_src_app_api_properties_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/properties/route.ts */ \"(rsc)/./src/app/api/properties/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/properties/route\",\n        pathname: \"/api/properties\",\n        filename: \"route\",\n        bundlePath: \"app/api/properties/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\api\\\\properties\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Black_Desktop_smart_off_plan_src_app_api_properties_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/properties/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/properties/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/properties/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request) {\n    try {\n        console.log(\"\\uD83D\\uDE80 Properties API called\");\n        const { searchParams } = new URL(request.url);\n        // Extract query parameters\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"12\");\n        // Get API configuration\n        const apiBaseUrl = \"https://search-listings-production.up.railway.app\";\n        const apiKey = \"reelly-680ffbdd-FEuCzeraBCN5dtByJeLb8AeCesrTvlFz\";\n        console.log(\"\\uD83D\\uDD27 API Config:\", {\n            apiBaseUrl,\n            hasApiKey: !!apiKey,\n            apiKeyLength: apiKey?.length\n        });\n        if (!apiBaseUrl || !apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"API configuration missing\",\n                message: \"API base URL or API key not configured\"\n            }, {\n                status: 500\n            });\n        }\n        // Build external API URL\n        const externalApiUrl = `${apiBaseUrl}/v1/properties`;\n        console.log(\"\\uD83D\\uDCE1 Calling external API:\", externalApiUrl);\n        // Try different header formats - let's try the most common ones\n        const headerOptions = [\n            // Option 1: Bearer token\n            {\n                Authorization: `Bearer ${apiKey}`,\n                \"Content-Type\": \"application/json\",\n                Accept: \"application/json\"\n            },\n            // Option 2: X-API-Key\n            {\n                \"X-API-Key\": apiKey,\n                \"Content-Type\": \"application/json\",\n                Accept: \"application/json\"\n            },\n            // Option 3: RapidAPI format\n            {\n                \"X-RapidAPI-Key\": apiKey,\n                \"X-RapidAPI-Host\": \"realty-in-us.p.rapidapi.com\",\n                \"Content-Type\": \"application/json\"\n            },\n            // Option 4: Simple API key\n            {\n                apikey: apiKey,\n                \"Content-Type\": \"application/json\"\n            }\n        ];\n        let response;\n        let lastError;\n        // Try each header format\n        for(let i = 0; i < headerOptions.length; i++){\n            const headers = headerOptions[i];\n            console.log(`📤 Trying header option ${i + 1}:`, headers);\n            try {\n                response = await fetch(externalApiUrl, {\n                    method: \"GET\",\n                    headers\n                });\n                console.log(`📥 Response status for option ${i + 1}:`, response.status);\n                if (response.ok) {\n                    console.log(`✅ Success with header option ${i + 1}`);\n                    break;\n                } else {\n                    const errorText = await response.text();\n                    console.log(`❌ Failed with option ${i + 1}:`, errorText);\n                    lastError = errorText;\n                }\n            } catch (error) {\n                console.log(`❌ Error with option ${i + 1}:`, error);\n                lastError = error;\n            }\n        }\n        if (!response || !response.ok) {\n            console.error(\"❌ All header options failed\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"External API error\",\n                message: \"Failed to authenticate with external API using any header format\",\n                details: lastError,\n                apiUrl: externalApiUrl,\n                triedOptions: headerOptions.length\n            }, {\n                status: 401\n            });\n        }\n        console.log(\"\\uD83D\\uDCE5 External API response status:\", response.status);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"❌ External API error:\", response.status, errorText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"External API error\",\n                message: `Failed to fetch properties: ${response.status} ${response.statusText}`,\n                details: errorText,\n                apiUrl: externalApiUrl\n            }, {\n                status: response.status === 404 ? 404 : 502\n            });\n        }\n        const externalData = await response.json();\n        console.log(\"✅ External API response received\");\n        console.log(\"\\uD83D\\uDCCA Response type:\", typeof externalData);\n        // Return the raw response for now so we can see the structure\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: externalData,\n            message: \"Properties fetched successfully\",\n            apiUrl: externalApiUrl,\n            requestParams: {\n                page,\n                limit\n            }\n        });\n    } catch (error) {\n        console.error(\"❌ Properties API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\",\n            message: \"An unexpected error occurred while fetching properties.\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9wcm9wZXJ0aWVzL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXdEO0FBRWpELGVBQWVDLElBQUlDLE9BQW9CO0lBQzVDLElBQUk7UUFDRkMsUUFBUUMsR0FBRyxDQUFDO1FBRVosTUFBTSxFQUFFQyxZQUFZLEVBQUUsR0FBRyxJQUFJQyxJQUFJSixRQUFRSyxHQUFHO1FBRTVDLDJCQUEyQjtRQUMzQixNQUFNQyxPQUFPQyxTQUFTSixhQUFhSyxHQUFHLENBQUMsV0FBVztRQUNsRCxNQUFNQyxRQUFRRixTQUFTSixhQUFhSyxHQUFHLENBQUMsWUFBWTtRQUVwRCx3QkFBd0I7UUFDeEIsTUFBTUUsYUFBYUMsbURBQW9DO1FBQ3ZELE1BQU1HLFNBQVNILGtEQUErQjtRQUU5Q1YsUUFBUUMsR0FBRyxDQUFDLDRCQUFrQjtZQUM1QlE7WUFDQU0sV0FBVyxDQUFDLENBQUNGO1lBQ2JHLGNBQWNILFFBQVFJO1FBQ3hCO1FBRUEsSUFBSSxDQUFDUixjQUFjLENBQUNJLFFBQVE7WUFDMUIsT0FBT2hCLHFEQUFZQSxDQUFDcUIsSUFBSSxDQUN0QjtnQkFDRUMsU0FBUztnQkFDVEMsT0FBTztnQkFDUEMsU0FBUztZQUNYLEdBQ0E7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLHlCQUF5QjtRQUN6QixNQUFNQyxpQkFBaUIsQ0FBQyxFQUFFZCxXQUFXLGNBQWMsQ0FBQztRQUNwRFQsUUFBUUMsR0FBRyxDQUFDLHNDQUE0QnNCO1FBRXhDLGdFQUFnRTtRQUNoRSxNQUFNQyxnQkFBMEM7WUFDOUMseUJBQXlCO1lBQ3pCO2dCQUNFQyxlQUFlLENBQUMsT0FBTyxFQUFFWixPQUFPLENBQUM7Z0JBQ2pDLGdCQUFnQjtnQkFDaEJhLFFBQVE7WUFDVjtZQUNBLHNCQUFzQjtZQUN0QjtnQkFDRSxhQUFhYjtnQkFDYixnQkFBZ0I7Z0JBQ2hCYSxRQUFRO1lBQ1Y7WUFDQSw0QkFBNEI7WUFDNUI7Z0JBQ0Usa0JBQWtCYjtnQkFDbEIsbUJBQW1CO2dCQUNuQixnQkFBZ0I7WUFDbEI7WUFDQSwyQkFBMkI7WUFDM0I7Z0JBQ0VjLFFBQVFkO2dCQUNSLGdCQUFnQjtZQUNsQjtTQUNEO1FBRUQsSUFBSWU7UUFDSixJQUFJQztRQUVKLHlCQUF5QjtRQUN6QixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSU4sY0FBY1AsTUFBTSxFQUFFYSxJQUFLO1lBQzdDLE1BQU1DLFVBQVVQLGFBQWEsQ0FBQ00sRUFBRTtZQUNoQzlCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHdCQUF3QixFQUFFNkIsSUFBSSxFQUFFLENBQUMsQ0FBQyxFQUFFQztZQUVqRCxJQUFJO2dCQUNGSCxXQUFXLE1BQU1JLE1BQU1ULGdCQUFnQjtvQkFDckNVLFFBQVE7b0JBQ1JGO2dCQUNGO2dCQUVBL0IsUUFBUUMsR0FBRyxDQUFDLENBQUMsOEJBQThCLEVBQUU2QixJQUFJLEVBQUUsQ0FBQyxDQUFDLEVBQUVGLFNBQVNOLE1BQU07Z0JBRXRFLElBQUlNLFNBQVNNLEVBQUUsRUFBRTtvQkFDZmxDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDZCQUE2QixFQUFFNkIsSUFBSSxFQUFFLENBQUM7b0JBQ25EO2dCQUNGLE9BQU87b0JBQ0wsTUFBTUssWUFBWSxNQUFNUCxTQUFTUSxJQUFJO29CQUNyQ3BDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHFCQUFxQixFQUFFNkIsSUFBSSxFQUFFLENBQUMsQ0FBQyxFQUFFSztvQkFDOUNOLFlBQVlNO2dCQUNkO1lBQ0YsRUFBRSxPQUFPZixPQUFPO2dCQUNkcEIsUUFBUUMsR0FBRyxDQUFDLENBQUMsb0JBQW9CLEVBQUU2QixJQUFJLEVBQUUsQ0FBQyxDQUFDLEVBQUVWO2dCQUM3Q1MsWUFBWVQ7WUFDZDtRQUNGO1FBRUEsSUFBSSxDQUFDUSxZQUFZLENBQUNBLFNBQVNNLEVBQUUsRUFBRTtZQUM3QmxDLFFBQVFvQixLQUFLLENBQUM7WUFDZCxPQUFPdkIscURBQVlBLENBQUNxQixJQUFJLENBQ3RCO2dCQUNFQyxTQUFTO2dCQUNUQyxPQUFPO2dCQUNQQyxTQUNFO2dCQUNGZ0IsU0FBU1I7Z0JBQ1RTLFFBQVFmO2dCQUNSZ0IsY0FBY2YsY0FBY1AsTUFBTTtZQUNwQyxHQUNBO2dCQUFFSyxRQUFRO1lBQUk7UUFFbEI7UUFFQXRCLFFBQVFDLEdBQUcsQ0FBQyw4Q0FBb0MyQixTQUFTTixNQUFNO1FBRS9ELElBQUksQ0FBQ00sU0FBU00sRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTVAsU0FBU1EsSUFBSTtZQUNyQ3BDLFFBQVFvQixLQUFLLENBQUMseUJBQXlCUSxTQUFTTixNQUFNLEVBQUVhO1lBRXhELE9BQU90QyxxREFBWUEsQ0FBQ3FCLElBQUksQ0FDdEI7Z0JBQ0VDLFNBQVM7Z0JBQ1RDLE9BQU87Z0JBQ1BDLFNBQVMsQ0FBQyw0QkFBNEIsRUFBRU8sU0FBU04sTUFBTSxDQUFDLENBQUMsRUFBRU0sU0FBU1ksVUFBVSxDQUFDLENBQUM7Z0JBQ2hGSCxTQUFTRjtnQkFDVEcsUUFBUWY7WUFDVixHQUNBO2dCQUFFRCxRQUFRTSxTQUFTTixNQUFNLEtBQUssTUFBTSxNQUFNO1lBQUk7UUFFbEQ7UUFFQSxNQUFNbUIsZUFBZSxNQUFNYixTQUFTVixJQUFJO1FBQ3hDbEIsUUFBUUMsR0FBRyxDQUFDO1FBQ1pELFFBQVFDLEdBQUcsQ0FBQywrQkFBcUIsT0FBT3dDO1FBRXhDLDhEQUE4RDtRQUM5RCxPQUFPNUMscURBQVlBLENBQUNxQixJQUFJLENBQUM7WUFDdkJDLFNBQVM7WUFDVHVCLE1BQU1EO1lBQ05wQixTQUFTO1lBQ1RpQixRQUFRZjtZQUNSb0IsZUFBZTtnQkFBRXRDO2dCQUFNRztZQUFNO1FBQy9CO0lBQ0YsRUFBRSxPQUFPWSxPQUFPO1FBQ2RwQixRQUFRb0IsS0FBSyxDQUFDLDJCQUEyQkE7UUFFekMsT0FBT3ZCLHFEQUFZQSxDQUFDcUIsSUFBSSxDQUN0QjtZQUNFQyxTQUFTO1lBQ1RDLE9BQU87WUFDUEMsU0FBUztZQUNUZ0IsU0FBU2pCLGlCQUFpQndCLFFBQVF4QixNQUFNQyxPQUFPLEdBQUc7UUFDcEQsR0FDQTtZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3NtYXJ0LW9mZi1wbGFuLy4vc3JjL2FwcC9hcGkvcHJvcGVydGllcy9yb3V0ZS50cz8xZjFhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tIFwibmV4dC9zZXJ2ZXJcIjtcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnNvbGUubG9nKFwi8J+agCBQcm9wZXJ0aWVzIEFQSSBjYWxsZWRcIik7XG5cbiAgICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybCk7XG5cbiAgICAvLyBFeHRyYWN0IHF1ZXJ5IHBhcmFtZXRlcnNcbiAgICBjb25zdCBwYWdlID0gcGFyc2VJbnQoc2VhcmNoUGFyYW1zLmdldChcInBhZ2VcIikgfHwgXCIxXCIpO1xuICAgIGNvbnN0IGxpbWl0ID0gcGFyc2VJbnQoc2VhcmNoUGFyYW1zLmdldChcImxpbWl0XCIpIHx8IFwiMTJcIik7XG5cbiAgICAvLyBHZXQgQVBJIGNvbmZpZ3VyYXRpb25cbiAgICBjb25zdCBhcGlCYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX0JBU0VfVVJMO1xuICAgIGNvbnN0IGFwaUtleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9LRVk7XG5cbiAgICBjb25zb2xlLmxvZyhcIvCflKcgQVBJIENvbmZpZzpcIiwge1xuICAgICAgYXBpQmFzZVVybCxcbiAgICAgIGhhc0FwaUtleTogISFhcGlLZXksXG4gICAgICBhcGlLZXlMZW5ndGg6IGFwaUtleT8ubGVuZ3RoLFxuICAgIH0pO1xuXG4gICAgaWYgKCFhcGlCYXNlVXJsIHx8ICFhcGlLZXkpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiBcIkFQSSBjb25maWd1cmF0aW9uIG1pc3NpbmdcIixcbiAgICAgICAgICBtZXNzYWdlOiBcIkFQSSBiYXNlIFVSTCBvciBBUEkga2V5IG5vdCBjb25maWd1cmVkXCIsXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBCdWlsZCBleHRlcm5hbCBBUEkgVVJMXG4gICAgY29uc3QgZXh0ZXJuYWxBcGlVcmwgPSBgJHthcGlCYXNlVXJsfS92MS9wcm9wZXJ0aWVzYDtcbiAgICBjb25zb2xlLmxvZyhcIvCfk6EgQ2FsbGluZyBleHRlcm5hbCBBUEk6XCIsIGV4dGVybmFsQXBpVXJsKTtcblxuICAgIC8vIFRyeSBkaWZmZXJlbnQgaGVhZGVyIGZvcm1hdHMgLSBsZXQncyB0cnkgdGhlIG1vc3QgY29tbW9uIG9uZXNcbiAgICBjb25zdCBoZWFkZXJPcHRpb25zOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+W10gPSBbXG4gICAgICAvLyBPcHRpb24gMTogQmVhcmVyIHRva2VuXG4gICAgICB7XG4gICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHthcGlLZXl9YCxcbiAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXG4gICAgICAgIEFjY2VwdDogXCJhcHBsaWNhdGlvbi9qc29uXCIsXG4gICAgICB9LFxuICAgICAgLy8gT3B0aW9uIDI6IFgtQVBJLUtleVxuICAgICAge1xuICAgICAgICBcIlgtQVBJLUtleVwiOiBhcGlLZXksXG4gICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgICBBY2NlcHQ6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgfSxcbiAgICAgIC8vIE9wdGlvbiAzOiBSYXBpZEFQSSBmb3JtYXRcbiAgICAgIHtcbiAgICAgICAgXCJYLVJhcGlkQVBJLUtleVwiOiBhcGlLZXksXG4gICAgICAgIFwiWC1SYXBpZEFQSS1Ib3N0XCI6IFwicmVhbHR5LWluLXVzLnAucmFwaWRhcGkuY29tXCIsXG4gICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgfSxcbiAgICAgIC8vIE9wdGlvbiA0OiBTaW1wbGUgQVBJIGtleVxuICAgICAge1xuICAgICAgICBhcGlrZXk6IGFwaUtleSxcbiAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXG4gICAgICB9LFxuICAgIF07XG5cbiAgICBsZXQgcmVzcG9uc2U7XG4gICAgbGV0IGxhc3RFcnJvcjtcblxuICAgIC8vIFRyeSBlYWNoIGhlYWRlciBmb3JtYXRcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGhlYWRlck9wdGlvbnMubGVuZ3RoOyBpKyspIHtcbiAgICAgIGNvbnN0IGhlYWRlcnMgPSBoZWFkZXJPcHRpb25zW2ldO1xuICAgICAgY29uc29sZS5sb2coYPCfk6QgVHJ5aW5nIGhlYWRlciBvcHRpb24gJHtpICsgMX06YCwgaGVhZGVycyk7XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goZXh0ZXJuYWxBcGlVcmwsIHtcbiAgICAgICAgICBtZXRob2Q6IFwiR0VUXCIsXG4gICAgICAgICAgaGVhZGVycyxcbiAgICAgICAgfSk7XG5cbiAgICAgICAgY29uc29sZS5sb2coYPCfk6UgUmVzcG9uc2Ugc3RhdHVzIGZvciBvcHRpb24gJHtpICsgMX06YCwgcmVzcG9uc2Uuc3RhdHVzKTtcblxuICAgICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIFN1Y2Nlc3Mgd2l0aCBoZWFkZXIgb3B0aW9uICR7aSArIDF9YCk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc3QgZXJyb3JUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDinYwgRmFpbGVkIHdpdGggb3B0aW9uICR7aSArIDF9OmAsIGVycm9yVGV4dCk7XG4gICAgICAgICAgbGFzdEVycm9yID0gZXJyb3JUZXh0O1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmxvZyhg4p2MIEVycm9yIHdpdGggb3B0aW9uICR7aSArIDF9OmAsIGVycm9yKTtcbiAgICAgICAgbGFzdEVycm9yID0gZXJyb3I7XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKCFyZXNwb25zZSB8fCAhcmVzcG9uc2Uub2spIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCLinYwgQWxsIGhlYWRlciBvcHRpb25zIGZhaWxlZFwiKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiBcIkV4dGVybmFsIEFQSSBlcnJvclwiLFxuICAgICAgICAgIG1lc3NhZ2U6XG4gICAgICAgICAgICBcIkZhaWxlZCB0byBhdXRoZW50aWNhdGUgd2l0aCBleHRlcm5hbCBBUEkgdXNpbmcgYW55IGhlYWRlciBmb3JtYXRcIixcbiAgICAgICAgICBkZXRhaWxzOiBsYXN0RXJyb3IsXG4gICAgICAgICAgYXBpVXJsOiBleHRlcm5hbEFwaVVybCxcbiAgICAgICAgICB0cmllZE9wdGlvbnM6IGhlYWRlck9wdGlvbnMubGVuZ3RoLFxuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogNDAxIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coXCLwn5OlIEV4dGVybmFsIEFQSSByZXNwb25zZSBzdGF0dXM6XCIsIHJlc3BvbnNlLnN0YXR1cyk7XG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zdCBlcnJvclRleHQgPSBhd2FpdCByZXNwb25zZS50ZXh0KCk7XG4gICAgICBjb25zb2xlLmVycm9yKFwi4p2MIEV4dGVybmFsIEFQSSBlcnJvcjpcIiwgcmVzcG9uc2Uuc3RhdHVzLCBlcnJvclRleHQpO1xuXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHtcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICBlcnJvcjogXCJFeHRlcm5hbCBBUEkgZXJyb3JcIixcbiAgICAgICAgICBtZXNzYWdlOiBgRmFpbGVkIHRvIGZldGNoIHByb3BlcnRpZXM6ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCxcbiAgICAgICAgICBkZXRhaWxzOiBlcnJvclRleHQsXG4gICAgICAgICAgYXBpVXJsOiBleHRlcm5hbEFwaVVybCxcbiAgICAgICAgfSxcbiAgICAgICAgeyBzdGF0dXM6IHJlc3BvbnNlLnN0YXR1cyA9PT0gNDA0ID8gNDA0IDogNTAyIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgY29uc3QgZXh0ZXJuYWxEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgIGNvbnNvbGUubG9nKFwi4pyFIEV4dGVybmFsIEFQSSByZXNwb25zZSByZWNlaXZlZFwiKTtcbiAgICBjb25zb2xlLmxvZyhcIvCfk4ogUmVzcG9uc2UgdHlwZTpcIiwgdHlwZW9mIGV4dGVybmFsRGF0YSk7XG5cbiAgICAvLyBSZXR1cm4gdGhlIHJhdyByZXNwb25zZSBmb3Igbm93IHNvIHdlIGNhbiBzZWUgdGhlIHN0cnVjdHVyZVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgZGF0YTogZXh0ZXJuYWxEYXRhLFxuICAgICAgbWVzc2FnZTogXCJQcm9wZXJ0aWVzIGZldGNoZWQgc3VjY2Vzc2Z1bGx5XCIsXG4gICAgICBhcGlVcmw6IGV4dGVybmFsQXBpVXJsLFxuICAgICAgcmVxdWVzdFBhcmFtczogeyBwYWdlLCBsaW1pdCB9LFxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCLinYwgUHJvcGVydGllcyBBUEkgZXJyb3I6XCIsIGVycm9yKTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIGVycm9yOiBcIkludGVybmFsIHNlcnZlciBlcnJvclwiLFxuICAgICAgICBtZXNzYWdlOiBcIkFuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQgd2hpbGUgZmV0Y2hpbmcgcHJvcGVydGllcy5cIixcbiAgICAgICAgZGV0YWlsczogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBcIlVua25vd24gZXJyb3JcIixcbiAgICAgIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiR0VUIiwicmVxdWVzdCIsImNvbnNvbGUiLCJsb2ciLCJzZWFyY2hQYXJhbXMiLCJVUkwiLCJ1cmwiLCJwYWdlIiwicGFyc2VJbnQiLCJnZXQiLCJsaW1pdCIsImFwaUJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX0JBU0VfVVJMIiwiYXBpS2V5IiwiTkVYVF9QVUJMSUNfQVBJX0tFWSIsImhhc0FwaUtleSIsImFwaUtleUxlbmd0aCIsImxlbmd0aCIsImpzb24iLCJzdWNjZXNzIiwiZXJyb3IiLCJtZXNzYWdlIiwic3RhdHVzIiwiZXh0ZXJuYWxBcGlVcmwiLCJoZWFkZXJPcHRpb25zIiwiQXV0aG9yaXphdGlvbiIsIkFjY2VwdCIsImFwaWtleSIsInJlc3BvbnNlIiwibGFzdEVycm9yIiwiaSIsImhlYWRlcnMiLCJmZXRjaCIsIm1ldGhvZCIsIm9rIiwiZXJyb3JUZXh0IiwidGV4dCIsImRldGFpbHMiLCJhcGlVcmwiLCJ0cmllZE9wdGlvbnMiLCJzdGF0dXNUZXh0IiwiZXh0ZXJuYWxEYXRhIiwiZGF0YSIsInJlcXVlc3RQYXJhbXMiLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/properties/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();