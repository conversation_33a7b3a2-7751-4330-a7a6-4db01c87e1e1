"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/properties/route";
exports.ids = ["app/api/properties/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Black_Desktop_smart_off_plan_src_app_api_properties_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/properties/route.ts */ \"(rsc)/./src/app/api/properties/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/properties/route\",\n        pathname: \"/api/properties\",\n        filename: \"route\",\n        bundlePath: \"app/api/properties/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\api\\\\properties\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Black_Desktop_smart_off_plan_src_app_api_properties_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/properties/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/properties/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/properties/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Transform external API data to our internal format\nfunction transformProperty(externalProperty) {\n    return {\n        id: externalProperty.id || externalProperty._id || `prop_${Date.now()}`,\n        title: externalProperty.title || externalProperty.name || \"Untitled Property\",\n        description: externalProperty.description || externalProperty.summary || \"\",\n        price: parseFloat(externalProperty.price) || 0,\n        currency: externalProperty.currency || \"AED\",\n        propertyType: externalProperty.propertyType || externalProperty.type || \"apartment\",\n        status: externalProperty.status || \"available\",\n        bedrooms: parseInt(externalProperty.bedrooms) || parseInt(externalProperty.beds) || 0,\n        bathrooms: parseInt(externalProperty.bathrooms) || parseInt(externalProperty.baths) || 0,\n        area: parseFloat(externalProperty.area) || parseFloat(externalProperty.size) || 0,\n        areaUnit: externalProperty.areaUnit || externalProperty.sizeUnit || \"sqft\",\n        location: {\n            name: externalProperty.location?.name || externalProperty.area || \"Unknown Location\",\n            city: externalProperty.location?.city || externalProperty.city || \"Dubai\",\n            country: externalProperty.location?.country || externalProperty.country || \"UAE\",\n            coordinates: externalProperty.location?.coordinates || externalProperty.coordinates || undefined\n        },\n        developer: externalProperty.developer ? {\n            name: externalProperty.developer.name || externalProperty.developerName || \"Unknown Developer\",\n            logo: externalProperty.developer.logo || externalProperty.developerLogo || undefined\n        } : undefined,\n        images: Array.isArray(externalProperty.images) ? externalProperty.images.map((img)=>({\n                url: typeof img === \"string\" ? img : img.url || img.src,\n                alt: typeof img === \"object\" ? img.alt || img.caption : undefined,\n                type: typeof img === \"object\" ? img.type : \"gallery\"\n            })) : externalProperty.image ? [\n            {\n                url: externalProperty.image,\n                type: \"main\"\n            }\n        ] : [],\n        amenities: Array.isArray(externalProperty.amenities) ? externalProperty.amenities : externalProperty.amenities ? externalProperty.amenities.split(\",\").map((a)=>a.trim()) : [],\n        features: Array.isArray(externalProperty.features) ? externalProperty.features : externalProperty.features ? externalProperty.features.split(\",\").map((f)=>f.trim()) : [],\n        completionDate: externalProperty.completionDate || externalProperty.handoverDate || undefined,\n        isFeatured: Boolean(externalProperty.isFeatured || externalProperty.featured),\n        isActive: Boolean(externalProperty.isActive !== false),\n        createdAt: externalProperty.createdAt || externalProperty.created_at || new Date().toISOString(),\n        updatedAt: externalProperty.updatedAt || externalProperty.updated_at || new Date().toISOString()\n    };\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        // Extract query parameters\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"12\");\n        const search = searchParams.get(\"search\") || \"\";\n        const propertyType = searchParams.get(\"propertyType\") || \"\";\n        const minPrice = searchParams.get(\"minPrice\") || \"\";\n        const maxPrice = searchParams.get(\"maxPrice\") || \"\";\n        const bedrooms = searchParams.get(\"bedrooms\") || \"\";\n        const bathrooms = searchParams.get(\"bathrooms\") || \"\";\n        const location = searchParams.get(\"location\") || \"\";\n        const developer = searchParams.get(\"developer\") || \"\";\n        const featured = searchParams.get(\"featured\") || \"\";\n        const sort = searchParams.get(\"sort\") || \"createdAt:desc\";\n        // Get API configuration\n        const apiBaseUrl = \"https://search-listings-production.up.railway.app\";\n        const apiKey = \"reelly-680ffbdd-FEuCzeraBCN5dtByJeLb8AeCesrTvlFz\";\n        if (!apiBaseUrl || !apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"API configuration missing\",\n                message: \"API base URL or API key not configured\"\n            }, {\n                status: 500\n            });\n        }\n        // Build external API URL with parameters\n        const externalApiUrl = new URL(`${apiBaseUrl}/api/properties`);\n        // Add query parameters to external API call\n        externalApiUrl.searchParams.set(\"page\", page.toString());\n        externalApiUrl.searchParams.set(\"limit\", limit.toString());\n        if (search) externalApiUrl.searchParams.set(\"search\", search);\n        if (propertyType) externalApiUrl.searchParams.set(\"propertyType\", propertyType);\n        if (minPrice) externalApiUrl.searchParams.set(\"minPrice\", minPrice);\n        if (maxPrice) externalApiUrl.searchParams.set(\"maxPrice\", maxPrice);\n        if (bedrooms) externalApiUrl.searchParams.set(\"bedrooms\", bedrooms);\n        if (bathrooms) externalApiUrl.searchParams.set(\"bathrooms\", bathrooms);\n        if (location) externalApiUrl.searchParams.set(\"location\", location);\n        if (developer) externalApiUrl.searchParams.set(\"developer\", developer);\n        if (featured) externalApiUrl.searchParams.set(\"featured\", featured);\n        if (sort) externalApiUrl.searchParams.set(\"sort\", sort);\n        // Make request to external API\n        const response = await fetch(externalApiUrl.toString(), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": `Bearer ${apiKey}`,\n                \"Content-Type\": \"application/json\",\n                \"User-Agent\": \"SmartOffPlan/1.0\"\n            },\n            // Add timeout\n            signal: AbortSignal.timeout(10000)\n        });\n        if (!response.ok) {\n            console.error(`External API error: ${response.status} ${response.statusText}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"External API error\",\n                message: `Failed to fetch properties: ${response.status} ${response.statusText}`,\n                statusCode: response.status\n            }, {\n                status: response.status === 404 ? 404 : 502\n            });\n        }\n        const externalData = await response.json();\n        // Handle different response formats from external API\n        let properties = [];\n        let pagination = undefined;\n        if (Array.isArray(externalData)) {\n            // Direct array response\n            properties = externalData;\n        } else if (externalData.data && Array.isArray(externalData.data)) {\n            // Wrapped response with data property\n            properties = externalData.data;\n            pagination = externalData.pagination || externalData.meta;\n        } else if (externalData.properties && Array.isArray(externalData.properties)) {\n            // Alternative property name\n            properties = externalData.properties;\n            pagination = externalData.pagination || externalData.meta;\n        } else {\n            console.error(\"Unexpected API response format:\", externalData);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid API response format\",\n                message: \"External API returned unexpected data format\"\n            }, {\n                status: 502\n            });\n        }\n        // Transform properties to our internal format\n        const transformedProperties = properties.map(transformProperty);\n        // Build response\n        const responseData = {\n            data: transformedProperties,\n            success: true,\n            pagination: pagination || {\n                page,\n                limit,\n                total: transformedProperties.length,\n                totalPages: Math.ceil(transformedProperties.length / limit),\n                hasNext: false,\n                hasPrev: page > 1\n            }\n        };\n        // Add cache headers for better performance\n        const response_headers = new Headers();\n        response_headers.set(\"Cache-Control\", \"public, s-maxage=300, stale-while-revalidate=600\") // 5 min cache\n        ;\n        response_headers.set(\"X-Total-Count\", responseData.pagination?.total.toString() || \"0\");\n        response_headers.set(\"X-Page\", page.toString());\n        response_headers.set(\"X-Per-Page\", limit.toString());\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(responseData, {\n            status: 200,\n            headers: response_headers\n        });\n    } catch (error) {\n        console.error(\"Properties API error:\", error);\n        // Handle specific error types\n        if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Network error\",\n                message: \"Failed to connect to external API. Please check your internet connection.\"\n            }, {\n                status: 503\n            });\n        }\n        if (error instanceof Error && error.name === \"AbortError\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Timeout error\",\n                message: \"Request to external API timed out. Please try again.\"\n            }, {\n                status: 504\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\",\n            message: \"An unexpected error occurred while fetching properties.\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/properties/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();