"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/properties/route";
exports.ids = ["app/api/properties/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Black_Desktop_smart_off_plan_src_app_api_properties_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/properties/route.ts */ \"(rsc)/./src/app/api/properties/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/properties/route\",\n        pathname: \"/api/properties\",\n        filename: \"route\",\n        bundlePath: \"app/api/properties/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\api\\\\properties\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Black_Desktop_smart_off_plan_src_app_api_properties_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/properties/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZwcm9wZXJ0aWVzJTJGcm91dGUmcGFnZT0lMkZhcGklMkZwcm9wZXJ0aWVzJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGcHJvcGVydGllcyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNCbGFjayU1Q0Rlc2t0b3AlNUNzbWFydC1vZmYtcGxhbiU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDQmxhY2slNUNEZXNrdG9wJTVDc21hcnQtb2ZmLXBsYW4maXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNjO0FBQzhCO0FBQzNHO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnSEFBbUI7QUFDM0M7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsaUVBQWlFO0FBQ3pFO0FBQ0E7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDdUg7O0FBRXZIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21hcnQtb2ZmLXBsYW4vPzY3Y2UiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcQmxhY2tcXFxcRGVza3RvcFxcXFxzbWFydC1vZmYtcGxhblxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxwcm9wZXJ0aWVzXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9wcm9wZXJ0aWVzL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvcHJvcGVydGllc1wiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvcHJvcGVydGllcy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXEJsYWNrXFxcXERlc2t0b3BcXFxcc21hcnQtb2ZmLXBsYW5cXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxccHJvcGVydGllc1xcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9hcGkvcHJvcGVydGllcy9yb3V0ZVwiO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICBzZXJ2ZXJIb29rcyxcbiAgICAgICAgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBvcmlnaW5hbFBhdGhuYW1lLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/properties/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/properties/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        // Extract query parameters\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"12\");\n        // Get API configuration\n        const apiBaseUrl = \"https://search-listings-production.up.railway.app\";\n        const apiKey = \"reelly-680ffbdd-FEuCzeraBCN5dtByJeLb8AeCesrTvlFz\";\n        console.log(\"\\uD83D\\uDD27 API Config:\", {\n            apiBaseUrl,\n            hasApiKey: !!apiKey,\n            apiKeyLength: apiKey?.length\n        });\n        if (!apiBaseUrl || !apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"API configuration missing\",\n                message: \"API base URL or API key not configured\"\n            }, {\n                status: 500\n            });\n        }\n        // Build external API URL\n        const externalApiUrl = `${apiBaseUrl}/v1/properties`;\n        console.log(\"\\uD83D\\uDCE1 Calling external API:\", externalApiUrl);\n        // Try different header formats - let's try the most common ones\n        const headerOptions = [\n            // Option 1: Bearer token\n            {\n                Authorization: `Bearer ${apiKey}`,\n                \"Content-Type\": \"application/json\",\n                Accept: \"application/json\"\n            },\n            // Option 2: X-API-Key\n            {\n                \"X-API-Key\": apiKey,\n                \"Content-Type\": \"application/json\",\n                Accept: \"application/json\"\n            },\n            // Option 3: RapidAPI format\n            {\n                \"X-RapidAPI-Key\": apiKey,\n                \"X-RapidAPI-Host\": \"realty-in-us.p.rapidapi.com\",\n                \"Content-Type\": \"application/json\"\n            },\n            // Option 4: Simple API key\n            {\n                apikey: apiKey,\n                \"Content-Type\": \"application/json\"\n            }\n        ];\n        let response;\n        let lastError;\n        // Try each header format\n        for(let i = 0; i < headerOptions.length; i++){\n            const headers = headerOptions[i];\n            console.log(`📤 Trying header option ${i + 1}:`, headers);\n            try {\n                response = await fetch(externalApiUrl, {\n                    method: \"GET\",\n                    headers\n                });\n                console.log(`📥 Response status for option ${i + 1}:`, response.status);\n                if (response.ok) {\n                    console.log(`✅ Success with header option ${i + 1}`);\n                    break;\n                } else {\n                    const errorText = await response.text();\n                    console.log(`❌ Failed with option ${i + 1}:`, errorText);\n                    lastError = errorText;\n                }\n            } catch (error) {\n                console.log(`❌ Error with option ${i + 1}:`, error);\n                lastError = error;\n            }\n        }\n        if (!response || !response.ok) {\n            console.error(\"❌ All header options failed\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"External API error\",\n                message: \"Failed to authenticate with external API using any header format\",\n                details: lastError,\n                apiUrl: externalApiUrl,\n                triedOptions: headerOptions.length\n            }, {\n                status: 401\n            });\n        }\n        console.log(\"\\uD83D\\uDCE5 External API response status:\", response.status);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"❌ External API error:\", response.status, errorText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"External API error\",\n                message: `Failed to fetch properties: ${response.status} ${response.statusText}`,\n                details: errorText,\n                apiUrl: externalApiUrl\n            }, {\n                status: response.status === 404 ? 404 : 502\n            });\n        }\n        const externalData = await response.json();\n        console.log(\"✅ External API response received\");\n        console.log(\"\\uD83D\\uDCCA Response type:\", typeof externalData);\n        // Return the raw response for now so we can see the structure\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: externalData,\n            message: \"Properties fetched successfully\",\n            apiUrl: externalApiUrl,\n            requestParams: {\n                page,\n                limit\n            }\n        });\n    } catch (error) {\n        console.error(\"❌ Properties API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\",\n            message: \"An unexpected error occurred while fetching properties.\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/properties/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();