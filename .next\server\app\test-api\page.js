/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/test-api/page";
exports.ids = ["app/test-api/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-api%2Fpage&page=%2Ftest-api%2Fpage&appPaths=%2Ftest-api%2Fpage&pagePath=private-next-app-dir%2Ftest-api%2Fpage.tsx&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-api%2Fpage&page=%2Ftest-api%2Fpage&appPaths=%2Ftest-api%2Fpage&pagePath=private-next-app-dir%2Ftest-api%2Fpage.tsx&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'test-api',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/test-api/page.tsx */ \"(rsc)/./src/app/test-api/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/test-api/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/test-api/page\",\n        pathname: \"/test-api\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-api%2Fpage&page=%2Ftest-api%2Fpage&appPaths=%2Ftest-api%2Fpage&pagePath=private-next-app-dir%2Ftest-api%2Fpage.tsx&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22PageErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CErrorMonitoringProvider.tsx%22%2C%22ids%22%3A%5B%22ErrorMonitoringProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22PageErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CErrorMonitoringProvider.tsx%22%2C%22ids%22%3A%5B%22ErrorMonitoringProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(ssr)/./src/components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ErrorMonitoringProvider.tsx */ \"(ssr)/./src/components/providers/ErrorMonitoringProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ToastProvider.tsx */ \"(ssr)/./src/components/providers/ToastProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22PageErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CErrorMonitoringProvider.tsx%22%2C%22ids%22%3A%5B%22ErrorMonitoringProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Capp%5C%5Ctest-api%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Capp%5C%5Ctest-api%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/test-api/page.tsx */ \"(ssr)/./src/app/test-api/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0JsYWNrJTVDJTVDRGVza3RvcCU1QyU1Q3NtYXJ0LW9mZi1wbGFuJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDdGVzdC1hcGklNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQTRHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21hcnQtb2ZmLXBsYW4vPzU1NzYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxCbGFja1xcXFxEZXNrdG9wXFxcXHNtYXJ0LW9mZi1wbGFuXFxcXHNyY1xcXFxhcHBcXFxcdGVzdC1hcGlcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Capp%5C%5Ctest-api%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/test-api/page.tsx":
/*!***********************************!*\
  !*** ./src/app/test-api/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestApiPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_loading_spinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/loading-spinner */ \"(ssr)/./src/components/ui/loading-spinner.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api-client */ \"(ssr)/./src/lib/api-client.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction TestApiPage() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedProperty, setSelectedProperty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fetchProperties = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            console.log(\"Fetching properties from:\", _lib_config__WEBPACK_IMPORTED_MODULE_5__.endpoints.properties.list);\n            const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.api.get(_lib_config__WEBPACK_IMPORTED_MODULE_5__.endpoints.properties.list, {\n                params: {\n                    page: 1,\n                    limit: 2\n                }\n            });\n            console.log(\"API Response:\", response);\n            setProperties(response.data || []);\n        } catch (err) {\n            console.error(\"API Error:\", err);\n            setError(err.message || \"Failed to fetch properties\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchPropertyDetail = async (propertyId)=>{\n        setLoading(true);\n        setError(null);\n        try {\n            console.log(\"Fetching property detail for ID:\", propertyId);\n            const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.api.get(_lib_config__WEBPACK_IMPORTED_MODULE_5__.endpoints.properties.detail(propertyId));\n            console.log(\"Property Detail Response:\", response);\n            setSelectedProperty(response.data);\n        } catch (err) {\n            console.error(\"API Error:\", err);\n            setError(err.message || \"Failed to fetch property detail\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold mb-8\",\n                children: \"API Test Page\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 p-4 rounded-lg mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-2\",\n                        children: \"API Configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"External API Base URL:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            \"https://search-listings-production.up.railway.app\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"External API Key:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                             true ? \"✅ Set\" : 0\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Our API Endpoint:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            _lib_config__WEBPACK_IMPORTED_MODULE_5__.endpoints.properties.list\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Full External URL:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            \"https://search-listings-production.up.railway.app\",\n                            \"/v1/properties\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"Test Properties List\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: fetchProperties,\n                                disabled: loading,\n                                className: \"bg-soft-brown hover:bg-deep-brown\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loading_spinner__WEBPACK_IMPORTED_MODULE_3__.LoadingSpinner, {\n                                            size: \"sm\",\n                                            color: \"white\",\n                                            className: \"mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Loading...\"\n                                    ]\n                                }, void 0, true) : \"Fetch Properties\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Error:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            \" \",\n                            error\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this),\n                    properties.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border rounded-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 bg-gray-50 border-b\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold\",\n                                    children: [\n                                        \"Properties Found: \",\n                                        properties.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"divide-y\",\n                                children: properties.slice(0, 5).map((property, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 hover:bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-lg\",\n                                                            children: property.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                property.location?.name,\n                                                                \", \",\n                                                                property.location?.city\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-soft-brown font-semibold\",\n                                                            children: [\n                                                                property.currency,\n                                                                \" \",\n                                                                property.price?.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                property.bedrooms,\n                                                                \" bed • \",\n                                                                property.bathrooms,\n                                                                \" bath •\",\n                                                                \" \",\n                                                                property.area,\n                                                                \" \",\n                                                                property.areaUnit\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: ()=>fetchPropertyDetail(property.id),\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    disabled: loading,\n                                                    children: \"View Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, property.id || index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            selectedProperty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Property Detail\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                children: selectedProperty.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Basic Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Price:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.currency,\n                                                            \" \",\n                                                            selectedProperty.price?.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Type:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.propertyType\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Status:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.status\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Bedrooms:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.bedrooms\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Bathrooms:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.bathrooms\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Area:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.area,\n                                                            \" \",\n                                                            selectedProperty.areaUnit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Location\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Area:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.location?.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"City:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.location?.city\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Country:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.location?.country\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            selectedProperty.developer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold mb-2\",\n                                                        children: \"Developer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: selectedProperty.developer.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            selectedProperty.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"Description\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: selectedProperty.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this),\n                            selectedProperty.amenities && selectedProperty.amenities.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"Amenities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: selectedProperty.amenities.map((amenity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs\",\n                                                children: amenity\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 25\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 17\n                            }, this),\n                            selectedProperty.images && selectedProperty.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: [\n                                            \"Images (\",\n                                            selectedProperty.images.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-2\",\n                                        children: selectedProperty.images.slice(0, 4).map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square bg-gray-200 rounded overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: image.url,\n                                                    alt: image.alt || `Property image ${index + 1}`,\n                                                    className: \"w-full h-full object-cover\",\n                                                    onError: (e)=>{\n                                                        e.target.src = \"/placeholder-image.jpg\";\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, this),\n            (properties.length > 0 || selectedProperty) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Raw API Response\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto max-h-96\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"text-xs\",\n                            children: JSON.stringify(selectedProperty || properties, null, 2)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/test-api/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ComponentErrorBoundary: () => (/* binding */ ComponentErrorBoundary),\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   PageErrorBoundary: () => (/* binding */ PageErrorBoundary),\n/* harmony export */   SectionErrorBoundary: () => (/* binding */ SectionErrorBoundary),\n/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _lib_error_handler__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/error-handler */ \"(ssr)/./src/lib/error-handler.ts\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,PageErrorBoundary,SectionErrorBoundary,ComponentErrorBoundary,withErrorBoundary auto */ \n\n\n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.handleRetry = ()=>{\n            this.setState({\n                hasError: false,\n                error: null,\n                errorInfo: null,\n                errorId: null\n            });\n        };\n        this.handleReload = ()=>{\n            window.location.reload();\n        };\n        this.handleGoHome = ()=>{\n            window.location.href = \"/\";\n        };\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null,\n            errorId: null\n        };\n    }\n    static getDerivedStateFromError(error) {\n        // Update state so the next render will show the fallback UI\n        return {\n            hasError: true,\n            error,\n            errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        // Log the error\n        (0,_lib_error_handler__WEBPACK_IMPORTED_MODULE_3__.logError)(error, errorInfo);\n        // Update state with error info\n        this.setState({\n            errorInfo\n        });\n        // Call custom error handler if provided\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        }\n        // Send to error monitoring service in production\n        if (false) {}\n    }\n    render() {\n        if (this.state.hasError) {\n            // Custom fallback UI\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            // Default fallback UI based on level\n            return this.renderErrorUI();\n        }\n        return this.props.children;\n    }\n    renderErrorUI() {\n        const { level = \"component\", showDetails = false } = this.props;\n        const { error, errorInfo, errorId } = this.state;\n        // Component-level error (minimal UI)\n        if (level === \"component\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-4 bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-8 h-8 text-red-500 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-700 mb-2\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: this.handleRetry,\n                            size: \"sm\",\n                            variant: \"outline\",\n                            className: \"text-red-700 border-red-300 hover:bg-red-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, this);\n        }\n        // Section-level error (medium UI)\n        if (level === \"section\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-[200px] bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-md px-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-12 h-12 text-red-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-red-800 mb-2\",\n                            children: \"Oops! Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600 mb-4\",\n                            children: \"We encountered an error while loading this section. Please try again.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: this.handleRetry,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    className: \"text-red-700 border-red-300 hover:bg-red-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Try Again\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: this.handleReload,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    className: \"text-red-700 border-red-300 hover:bg-red-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Reload Page\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this);\n        }\n        // Page-level error (full UI)\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-ivory flex items-center justify-center px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-20 h-20 text-red-500 mx-auto mb-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-soft-brown mb-4\",\n                                children: \"Something went wrong\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-warm-gray text-lg mb-6\",\n                                children: \"We're sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            showDetails && error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                className: \"text-left bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                        className: \"cursor-pointer text-red-700 font-medium mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Error Details\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-red-600 space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Error ID:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" \",\n                                                    errorId\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Message:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" \",\n                                                    error.message\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 19\n                                            }, this),\n                                             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Stack:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                className: \"mt-1 text-xs bg-red-100 p-2 rounded overflow-auto\",\n                                                                children: error.stack\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    errorInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Component Stack:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                className: \"mt-1 text-xs bg-red-100 p-2 rounded overflow-auto\",\n                                                                children: errorInfo.componentStack\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: this.handleRetry,\n                                className: \"bg-soft-brown hover:bg-deep-brown text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Try Again\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: this.handleReload,\n                                variant: \"outline\",\n                                className: \"border-soft-brown text-soft-brown hover:bg-soft-brown hover:text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Reload Page\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: this.handleGoHome,\n                                variant: \"outline\",\n                                className: \"border-soft-brown text-soft-brown hover:bg-soft-brown hover:text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Go Home\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 text-sm text-warm-gray\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"If this problem persists, please contact our support team at\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:<EMAIL>\",\n                                        className: \"text-soft-brown hover:underline\",\n                                        children: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this),\n                            errorId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2\",\n                                children: [\n                                    \"Reference ID: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"bg-gray-100 px-1 rounded\",\n                                        children: errorId\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 31\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 160,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 159,\n            columnNumber: 7\n        }, this);\n    }\n}\n// Specialized Error Boundaries\nfunction PageErrorBoundary({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n        level: \"page\",\n        showDetails: \"development\" === \"development\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 257,\n        columnNumber: 5\n    }, this);\n}\nfunction SectionErrorBoundary({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n        level: \"section\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, this);\n}\nfunction ComponentErrorBoundary({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n        level: \"component\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, this);\n}\n// HOC for wrapping components with error boundaries\nfunction withErrorBoundary(Component, level = \"component\") {\n    const WrappedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n            level: level,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 285,\n            columnNumber: 5\n        }, this);\n    WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n    return WrappedComponent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/ErrorMonitoringProvider.tsx":
/*!**************************************************************!*\
  !*** ./src/components/providers/ErrorMonitoringProvider.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorMonitoringProvider: () => (/* binding */ ErrorMonitoringProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/error-monitoring */ \"(ssr)/./src/lib/error-monitoring.ts\");\n/* __next_internal_client_entry_do_not_use__ ErrorMonitoringProvider auto */ \n\n\nfunction ErrorMonitoringProvider({ children }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize error monitoring\n        if (false) {}\n    }, []);\n    // Set user when authentication state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This would typically come from your auth context/store\n        // For now, we'll check localStorage for user info\n        const checkUser = ()=>{\n            try {\n                const userStr = localStorage.getItem(\"user\");\n                if (userStr) {\n                    const user = JSON.parse(userStr);\n                    if (user.id) {\n                        (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.setUser)(user.id);\n                        (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.addContext)(\"user\", {\n                            id: user.id,\n                            email: user.email,\n                            role: user.role\n                        });\n                    }\n                }\n            } catch (e) {\n            // Ignore parsing errors\n            }\n        };\n        checkUser();\n        // Listen for storage changes (user login/logout)\n        window.addEventListener(\"storage\", checkUser);\n        return ()=>{\n            window.removeEventListener(\"storage\", checkUser);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/ErrorMonitoringProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/ToastProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/ToastProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider auto */ \n\nfunction ToastProvider() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n        position: \"top-right\",\n        reverseOrder: false,\n        gutter: 8,\n        containerClassName: \"\",\n        containerStyle: {},\n        toastOptions: {\n            // Default options for all toasts\n            duration: 5000,\n            style: {\n                background: \"#fff\",\n                color: \"#333\",\n                border: \"1px solid #e5e7eb\",\n                borderRadius: \"8px\",\n                boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\",\n                fontSize: \"14px\",\n                fontFamily: \"var(--font-inter), Inter, sans-serif\",\n                maxWidth: \"400px\"\n            },\n            // Success toasts\n            success: {\n                style: {\n                    background: \"#f0fdf4\",\n                    color: \"#166534\",\n                    border: \"1px solid #bbf7d0\"\n                },\n                iconTheme: {\n                    primary: \"#22c55e\",\n                    secondary: \"#f0fdf4\"\n                }\n            },\n            // Error toasts\n            error: {\n                style: {\n                    background: \"#fef2f2\",\n                    color: \"#dc2626\",\n                    border: \"1px solid #fecaca\"\n                },\n                iconTheme: {\n                    primary: \"#ef4444\",\n                    secondary: \"#fef2f2\"\n                },\n                duration: 6000\n            },\n            // Loading toasts\n            loading: {\n                style: {\n                    background: \"#fefce8\",\n                    color: \"#a16207\",\n                    border: \"1px solid #fef3c7\"\n                },\n                iconTheme: {\n                    primary: \"#eab308\",\n                    secondary: \"#fefce8\"\n                }\n            }\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\providers\\\\ToastProvider.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/ToastProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9 rounded-md\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/loading-spinner.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/loading-spinner.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingDots: () => (/* binding */ LoadingDots),\n/* harmony export */   LoadingPulse: () => (/* binding */ LoadingPulse),\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\nconst sizeClasses = {\n    sm: \"w-4 h-4\",\n    md: \"w-6 h-6\",\n    lg: \"w-8 h-8\",\n    xl: \"w-12 h-12\"\n};\nconst colorClasses = {\n    primary: \"text-soft-brown\",\n    secondary: \"text-gold\",\n    white: \"text-white\"\n};\nfunction LoadingSpinner({ size = \"md\", className, color = \"primary\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-spin rounded-full border-2 border-current border-t-transparent\", sizeClasses[size], colorClasses[color], className),\n        role: \"status\",\n        \"aria-label\": \"Loading\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"sr-only\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ui\\\\loading-spinner.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ui\\\\loading-spinner.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingDots({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex space-x-1\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.3s]\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ui\\\\loading-spinner.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.15s]\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ui\\\\loading-spinner.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-current rounded-full animate-bounce\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ui\\\\loading-spinner.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ui\\\\loading-spinner.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingPulse({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex space-x-2\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-3 h-3 bg-current rounded-full animate-pulse\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ui\\\\loading-spinner.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-3 h-3 bg-current rounded-full animate-pulse [animation-delay:0.2s]\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ui\\\\loading-spinner.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-3 h-3 bg-current rounded-full animate-pulse [animation-delay:0.4s]\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ui\\\\loading-spinner.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ui\\\\loading-spinner.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/loading-spinner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api-client.ts":
/*!*******************************!*\
  !*** ./src/lib/api-client.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   apiUtils: () => (/* binding */ apiUtils),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(ssr)/./src/lib/config.ts\");\n\n\n// Create axios instance\nconst createApiClient = ()=>{\n    const client = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n        baseURL: _config__WEBPACK_IMPORTED_MODULE_0__.config.app.url,\n        timeout: _config__WEBPACK_IMPORTED_MODULE_0__.config.api.timeout,\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Accept: \"application/json\"\n        }\n    });\n    // Request interceptor\n    client.interceptors.request.use((config)=>{\n        // Add auth token if available\n        const token =  false ? 0 : null;\n        if (token) {\n            config.headers.Authorization = `Bearer ${token}`;\n        }\n        // Add request timestamp for debugging\n        if (true) {\n            console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {\n                params: config.params,\n                data: config.data\n            });\n        }\n        return config;\n    }, (error)=>{\n        console.error(\"❌ Request Error:\", error);\n        return Promise.reject(error);\n    });\n    // Response interceptor\n    client.interceptors.response.use((response)=>{\n        // Log successful responses in development\n        if (true) {\n            console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {\n                status: response.status,\n                data: response.data\n            });\n        }\n        return response;\n    }, (error)=>{\n        // Handle different error types\n        if (error.response) {\n            // Server responded with error status\n            const { status, data } = error.response;\n            console.error(`❌ API Error ${status}:`, data?.message || error.message);\n            // Handle specific status codes\n            switch(status){\n                case 401:\n                    // Unauthorized - clear auth and redirect to login\n                    if (false) {}\n                    break;\n                case 403:\n                    // Forbidden\n                    console.error(\"Access forbidden\");\n                    break;\n                case 404:\n                    // Not found\n                    console.error(\"Resource not found\");\n                    break;\n                case 422:\n                    // Validation error\n                    console.error(\"Validation error:\", data?.details);\n                    break;\n                case 500:\n                    // Server error\n                    console.error(\"Internal server error\");\n                    break;\n            }\n            return Promise.reject({\n                message: data?.message || \"An error occurred\",\n                code: data?.code,\n                status,\n                details: data?.details\n            });\n        } else if (error.request) {\n            // Network error\n            console.error(\"❌ Network Error:\", error.message);\n            return Promise.reject({\n                message: \"Network error. Please check your connection.\",\n                code: \"NETWORK_ERROR\"\n            });\n        } else {\n            // Other error\n            console.error(\"❌ Error:\", error.message);\n            return Promise.reject({\n                message: error.message || \"An unexpected error occurred\",\n                code: \"UNKNOWN_ERROR\"\n            });\n        }\n    });\n    return client;\n};\n// Create the API client instance\nconst apiClient = createApiClient();\n// Helper functions for common HTTP methods\nconst api = {\n    // GET request\n    get: async (url, config)=>{\n        const response = await apiClient.get(url, config);\n        return response.data;\n    },\n    // POST request\n    post: async (url, data, config)=>{\n        const response = await apiClient.post(url, data, config);\n        return response.data;\n    },\n    // PUT request\n    put: async (url, data, config)=>{\n        const response = await apiClient.put(url, data, config);\n        return response.data;\n    },\n    // PATCH request\n    patch: async (url, data, config)=>{\n        const response = await apiClient.patch(url, data, config);\n        return response.data;\n    },\n    // DELETE request\n    delete: async (url, config)=>{\n        const response = await apiClient.delete(url, config);\n        return response.data;\n    }\n};\n// Utility functions\nconst apiUtils = {\n    // Build query string from object\n    buildQueryString: (params)=>{\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value])=>{\n            if (value !== undefined && value !== null && value !== \"\") {\n                searchParams.append(key, String(value));\n            }\n        });\n        return searchParams.toString();\n    },\n    // Handle file upload\n    uploadFile: async (file, endpoint)=>{\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        return api.post(endpoint, formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n    },\n    // Download file\n    downloadFile: async (url, filename)=>{\n        const response = await apiClient.get(url, {\n            responseType: \"blob\"\n        });\n        const blob = new Blob([\n            response.data\n        ]);\n        const downloadUrl = window.URL.createObjectURL(blob);\n        const link = document.createElement(\"a\");\n        link.href = downloadUrl;\n        link.download = filename || \"download\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(downloadUrl);\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api-client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   constants: () => (/* binding */ constants),\n/* harmony export */   endpoints: () => (/* binding */ endpoints),\n/* harmony export */   validation: () => (/* binding */ validation)\n/* harmony export */ });\n// Environment configuration and constants\nconst config = {\n    // API Configuration\n    api: {\n        baseUrl: \"https://search-listings-production.up.railway.app\" || 0,\n        version: \"v1\" || 0,\n        timeout: 30000\n    },\n    // App Configuration\n    app: {\n        name: \"Smart Off Plan\",\n        url: process.env.NEXTAUTH_URL || \"http://localhost:3000\",\n        environment: \"development\" || 0\n    },\n    // External Services\n    services: {\n        googleMaps: {\n            apiKey: \"your-google-maps-api-key\" || 0\n        },\n        analytics: {\n            googleAnalyticsId: \"your-ga-id\" || 0\n        },\n        cloudinary: {\n            cloudName: \"your-cloudinary-name\" || 0\n        }\n    },\n    // Feature Flags\n    features: {\n        analytics: \"false\" === \"true\",\n        chat: \"false\" === \"true\",\n        notifications: \"false\" === \"true\"\n    },\n    // Development\n    isDevelopment: \"development\" === \"development\",\n    isProduction: \"development\" === \"production\",\n    isTest: \"development\" === \"test\"\n};\n// API Endpoints\nconst endpoints = {\n    // Properties\n    properties: {\n        list: \"/api/properties\",\n        detail: (id)=>`/api/properties/${id}`,\n        search: \"/api/properties\",\n        featured: \"/api/properties?featured=true\"\n    },\n    // Developers\n    developers: {\n        list: \"/developers\",\n        detail: (id)=>`/developers/${id}`,\n        properties: (id)=>`/developers/${id}/properties`\n    },\n    // Areas\n    areas: {\n        list: \"/areas\",\n        detail: (id)=>`/areas/${id}`,\n        properties: (id)=>`/areas/${id}/properties`\n    },\n    // Contact\n    contact: {\n        submit: \"/contact\",\n        newsletter: \"/newsletter/subscribe\"\n    },\n    // User\n    user: {\n        profile: \"/user/profile\",\n        favorites: \"/user/favorites\",\n        inquiries: \"/user/inquiries\"\n    },\n    // Authentication\n    auth: {\n        login: \"/auth/login\",\n        register: \"/auth/register\",\n        logout: \"/auth/logout\",\n        refresh: \"/auth/refresh\"\n    }\n};\n// Validation\nconst validation = {\n    email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n    phone: /^[\\+]?[1-9][\\d]{0,15}$/,\n    password: {\n        minLength: 8,\n        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/\n    }\n};\n// Constants\nconst constants = {\n    // Pagination\n    defaultPageSize: 12,\n    maxPageSize: 50,\n    // File Upload\n    maxFileSize: 5 * 1024 * 1024,\n    allowedImageTypes: [\n        \"image/jpeg\",\n        \"image/png\",\n        \"image/webp\"\n    ],\n    allowedDocumentTypes: [\n        \"application/pdf\",\n        \"application/msword\"\n    ],\n    // Cache\n    cacheKeys: {\n        properties: \"properties\",\n        developers: \"developers\",\n        areas: \"areas\",\n        user: \"user\"\n    },\n    // Timeouts\n    debounceDelay: 300,\n    toastDuration: 5000\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/config.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/error-handler.ts":
/*!**********************************!*\
  !*** ./src/lib/error-handler.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorType: () => (/* binding */ ErrorType),\n/* harmony export */   classifyError: () => (/* binding */ classifyError),\n/* harmony export */   dismissAllToasts: () => (/* binding */ dismissAllToasts),\n/* harmony export */   dismissToast: () => (/* binding */ dismissToast),\n/* harmony export */   formatValidationErrors: () => (/* binding */ formatValidationErrors),\n/* harmony export */   logError: () => (/* binding */ logError),\n/* harmony export */   showError: () => (/* binding */ showError),\n/* harmony export */   showInfo: () => (/* binding */ showInfo),\n/* harmony export */   showLoading: () => (/* binding */ showLoading),\n/* harmony export */   showSuccess: () => (/* binding */ showSuccess),\n/* harmony export */   withRetry: () => (/* binding */ withRetry)\n/* harmony export */ });\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _error_monitoring__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error-monitoring */ \"(ssr)/./src/lib/error-monitoring.ts\");\n\n\nvar ErrorType;\n(function(ErrorType) {\n    ErrorType[\"NETWORK\"] = \"NETWORK\";\n    ErrorType[\"VALIDATION\"] = \"VALIDATION\";\n    ErrorType[\"AUTHENTICATION\"] = \"AUTHENTICATION\";\n    ErrorType[\"AUTHORIZATION\"] = \"AUTHORIZATION\";\n    ErrorType[\"NOT_FOUND\"] = \"NOT_FOUND\";\n    ErrorType[\"SERVER\"] = \"SERVER\";\n    ErrorType[\"UNKNOWN\"] = \"UNKNOWN\";\n})(ErrorType || (ErrorType = {}));\n// Error classification\nconst classifyError = (error)=>{\n    const timestamp = new Date();\n    // Handle API errors\n    if (error && typeof error === \"object\" && \"status\" in error) {\n        const apiError = error;\n        switch(apiError.status){\n            case 400:\n                return {\n                    type: \"VALIDATION\",\n                    message: apiError.message || \"Invalid request data\",\n                    code: apiError.code,\n                    details: apiError.details,\n                    timestamp\n                };\n            case 401:\n                return {\n                    type: \"AUTHENTICATION\",\n                    message: \"Please log in to continue\",\n                    code: apiError.code,\n                    timestamp\n                };\n            case 403:\n                return {\n                    type: \"AUTHORIZATION\",\n                    message: \"You do not have permission to perform this action\",\n                    code: apiError.code,\n                    timestamp\n                };\n            case 404:\n                return {\n                    type: \"NOT_FOUND\",\n                    message: \"The requested resource was not found\",\n                    code: apiError.code,\n                    timestamp\n                };\n            case 422:\n                return {\n                    type: \"VALIDATION\",\n                    message: apiError.message || \"Validation failed\",\n                    code: apiError.code,\n                    details: apiError.details,\n                    timestamp\n                };\n            case 500:\n            case 502:\n            case 503:\n            case 504:\n                return {\n                    type: \"SERVER\",\n                    message: \"Server error. Please try again later.\",\n                    code: apiError.code,\n                    timestamp\n                };\n            default:\n                return {\n                    type: \"UNKNOWN\",\n                    message: apiError.message || \"An unexpected error occurred\",\n                    code: apiError.code,\n                    timestamp\n                };\n        }\n    }\n    // Handle network errors\n    if (error && error.code === \"NETWORK_ERROR\") {\n        return {\n            type: \"NETWORK\",\n            message: \"Network error. Please check your connection.\",\n            code: \"NETWORK_ERROR\",\n            timestamp\n        };\n    }\n    // Handle generic errors\n    if (error instanceof Error) {\n        return {\n            type: \"UNKNOWN\",\n            message: error.message || \"An unexpected error occurred\",\n            timestamp\n        };\n    }\n    // Fallback\n    return {\n        type: \"UNKNOWN\",\n        message: \"An unexpected error occurred\",\n        timestamp\n    };\n};\n// Error display functions\nconst showError = (error, customMessage)=>{\n    const appError = classifyError(error);\n    const message = customMessage || appError.message;\n    // Log error in development\n    if (true) {\n        console.error(\"\\uD83D\\uDEA8 Error:\", appError);\n    }\n    // Send to error monitoring system\n    (0,_error_monitoring__WEBPACK_IMPORTED_MODULE_1__.captureError)(error, {\n        customMessage,\n        appError\n    }, \"error\");\n    // Show toast notification\n    switch(appError.type){\n        case \"VALIDATION\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 6000,\n                icon: \"⚠️\"\n            });\n            break;\n        case \"AUTHENTICATION\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 8000,\n                icon: \"\\uD83D\\uDD10\"\n            });\n            break;\n        case \"AUTHORIZATION\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 6000,\n                icon: \"\\uD83D\\uDEAB\"\n            });\n            break;\n        case \"NOT_FOUND\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 5000,\n                icon: \"\\uD83D\\uDD0D\"\n            });\n            break;\n        case \"NETWORK\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 8000,\n                icon: \"\\uD83D\\uDCE1\"\n            });\n            break;\n        case \"SERVER\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 8000,\n                icon: \"\\uD83D\\uDD27\"\n            });\n            break;\n        default:\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 5000,\n                icon: \"❌\"\n            });\n    }\n    return appError;\n};\n// Success notifications\nconst showSuccess = (message, duration = 4000)=>{\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].success(message, {\n        duration,\n        icon: \"✅\"\n    });\n};\n// Info notifications\nconst showInfo = (message, duration = 4000)=>{\n    (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(message, {\n        duration,\n        icon: \"ℹ️\"\n    });\n};\n// Loading notifications\nconst showLoading = (message = \"Loading...\")=>{\n    return react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].loading(message, {\n        icon: \"⏳\"\n    });\n};\n// Dismiss specific toast\nconst dismissToast = (toastId)=>{\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].dismiss(toastId);\n};\n// Dismiss all toasts\nconst dismissAllToasts = ()=>{\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].dismiss();\n};\n// Error boundary helper\nconst logError = (error, errorInfo)=>{\n    console.error(\"\\uD83D\\uDEA8 Error Boundary:\", error, errorInfo);\n    // Send to error monitoring system\n    (0,_error_monitoring__WEBPACK_IMPORTED_MODULE_1__.captureError)(error, errorInfo, \"error\");\n};\n// Validation error helpers\nconst formatValidationErrors = (errors)=>{\n    const messages = Object.entries(errors).map(([field, fieldErrors])=>{\n        const fieldName = field.charAt(0).toUpperCase() + field.slice(1);\n        return `${fieldName}: ${fieldErrors.join(\", \")}`;\n    });\n    return messages.join(\"\\n\");\n};\n// Retry helper\nconst withRetry = async (fn, maxRetries = 3, delay = 1000)=>{\n    let lastError;\n    for(let i = 0; i <= maxRetries; i++){\n        try {\n            return await fn();\n        } catch (error) {\n            lastError = error;\n            if (i === maxRetries) {\n                throw error;\n            }\n            // Wait before retrying\n            await new Promise((resolve)=>setTimeout(resolve, delay * Math.pow(2, i)));\n        }\n    }\n    throw lastError;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/error-handler.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/error-monitoring.ts":
/*!*************************************!*\
  !*** ./src/lib/error-monitoring.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addContext: () => (/* binding */ addContext),\n/* harmony export */   captureError: () => (/* binding */ captureError),\n/* harmony export */   capturePerformanceMetric: () => (/* binding */ capturePerformanceMetric),\n/* harmony export */   captureUserAction: () => (/* binding */ captureUserAction),\n/* harmony export */   errorMonitor: () => (/* binding */ errorMonitor),\n/* harmony export */   setUser: () => (/* binding */ setUser)\n/* harmony export */ });\n// Error monitoring and tracking system\nclass ErrorMonitor {\n    constructor(){\n        this.maxRetries = 3;\n        this.retryDelay = 1000;\n        this.queue = [];\n        this.isOnline = true;\n        this.sessionId = this.generateSessionId();\n        this.isEnabled =  false || process.env.NEXT_PUBLIC_ENABLE_ERROR_MONITORING === \"true\";\n        this.apiEndpoint = process.env.NEXT_PUBLIC_ERROR_MONITORING_ENDPOINT || \"/api/errors\";\n        if (false) {}\n    }\n    generateSessionId() {\n        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    setupGlobalErrorHandlers() {\n        // Handle JavaScript errors\n        window.addEventListener(\"error\", (event)=>{\n            this.captureError(event.error || new Error(event.message), {\n                filename: event.filename,\n                lineno: event.lineno,\n                colno: event.colno,\n                type: \"javascript\"\n            });\n        });\n        // Handle unhandled promise rejections\n        window.addEventListener(\"unhandledrejection\", (event)=>{\n            this.captureError(new Error(event.reason), {\n                type: \"unhandled_promise_rejection\",\n                reason: event.reason\n            });\n        });\n        // Handle React errors (if using React Error Boundary)\n        const originalConsoleError = console.error;\n        console.error = (...args)=>{\n            if (args[0] && typeof args[0] === \"string\" && args[0].includes(\"React\")) {\n                this.captureError(new Error(args.join(\" \")), {\n                    type: \"react_error\",\n                    args\n                });\n            }\n            originalConsoleError.apply(console, args);\n        };\n    }\n    setupNetworkMonitoring() {\n        // Monitor network status\n        window.addEventListener(\"online\", ()=>{\n            this.isOnline = true;\n            this.processQueue();\n        });\n        window.addEventListener(\"offline\", ()=>{\n            this.isOnline = false;\n        });\n        // Monitor fetch requests\n        const originalFetch = window.fetch;\n        window.fetch = async (...args)=>{\n            const startTime = performance.now();\n            try {\n                const response = await originalFetch(...args);\n                const endTime = performance.now();\n                const duration = endTime - startTime;\n                // Log slow requests\n                if (duration > 5000) {\n                    this.capturePerformanceMetric(\"slow_request\", duration, \"ms\", {\n                        url: args[0],\n                        status: response.status\n                    });\n                }\n                // Log failed requests\n                if (!response.ok) {\n                    this.captureError(new Error(`HTTP ${response.status}: ${response.statusText}`), {\n                        type: \"http_error\",\n                        url: args[0],\n                        status: response.status,\n                        statusText: response.statusText\n                    });\n                }\n                return response;\n            } catch (error) {\n                this.captureError(error, {\n                    type: \"network_error\",\n                    url: args[0]\n                });\n                throw error;\n            }\n        };\n    }\n    setupPerformanceMonitoring() {\n        // Monitor page load performance\n        window.addEventListener(\"load\", ()=>{\n            setTimeout(()=>{\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    this.capturePerformanceMetric(\"page_load_time\", navigation.loadEventEnd - navigation.fetchStart, \"ms\");\n                    this.capturePerformanceMetric(\"dom_content_loaded\", navigation.domContentLoadedEventEnd - navigation.fetchStart, \"ms\");\n                    this.capturePerformanceMetric(\"first_byte\", navigation.responseStart - navigation.fetchStart, \"ms\");\n                }\n                // Monitor Core Web Vitals\n                this.monitorCoreWebVitals();\n            }, 0);\n        });\n    }\n    monitorCoreWebVitals() {\n        // This would typically use the web-vitals library\n        // For now, we'll implement basic monitoring\n        // Monitor Largest Contentful Paint (LCP)\n        const observer = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            const lastEntry = entries[entries.length - 1];\n            if (lastEntry) {\n                this.capturePerformanceMetric(\"lcp\", lastEntry.startTime, \"ms\");\n            }\n        });\n        try {\n            observer.observe({\n                entryTypes: [\n                    \"largest-contentful-paint\"\n                ]\n            });\n        } catch (e) {\n        // LCP not supported\n        }\n        // Monitor Cumulative Layout Shift (CLS)\n        let clsValue = 0;\n        const clsObserver = new PerformanceObserver((list)=>{\n            for (const entry of list.getEntries()){\n                if (!entry.hadRecentInput) {\n                    clsValue += entry.value;\n                }\n            }\n        });\n        try {\n            clsObserver.observe({\n                entryTypes: [\n                    \"layout-shift\"\n                ]\n            });\n            // Report CLS on page unload\n            window.addEventListener(\"beforeunload\", ()=>{\n                this.capturePerformanceMetric(\"cls\", clsValue, \"score\");\n            });\n        } catch (e) {\n        // CLS not supported\n        }\n    }\n    startQueueProcessor() {\n        setInterval(()=>{\n            if (this.isOnline && this.queue.length > 0) {\n                this.processQueue();\n            }\n        }, 5000) // Process queue every 5 seconds\n        ;\n    }\n    async processQueue() {\n        if (this.queue.length === 0) return;\n        const batch = this.queue.splice(0, 10) // Process up to 10 errors at a time\n        ;\n        try {\n            await this.sendErrorBatch(batch);\n        } catch (error) {\n            // Put errors back in queue for retry\n            this.queue.unshift(...batch);\n            console.warn(\"Failed to send error batch:\", error);\n        }\n    }\n    async sendErrorBatch(errors) {\n        if (!this.isEnabled) return;\n        const payload = {\n            errors,\n            sessionId: this.sessionId,\n            userId: this.userId,\n            timestamp: new Date().toISOString()\n        };\n        const response = await fetch(this.apiEndpoint, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            throw new Error(`Failed to send errors: ${response.status}`);\n        }\n    }\n    captureError(error, context, level = \"error\") {\n        if (!this.isEnabled) {\n            console.error(\"Error captured:\", error, context);\n            return;\n        }\n        const errorEvent = {\n            id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            timestamp: new Date().toISOString(),\n            message: error.message,\n            stack: error.stack,\n            url: window.location.href,\n            userAgent: navigator.userAgent,\n            userId: this.userId,\n            sessionId: this.sessionId,\n            level,\n            context,\n            fingerprint: this.generateFingerprint(error)\n        };\n        this.queue.push(errorEvent);\n        // Send immediately for critical errors\n        if (level === \"error\" && this.isOnline) {\n            this.sendErrorBatch([\n                errorEvent\n            ]).catch(()=>{\n            // Error will remain in queue for retry\n            });\n        }\n    }\n    capturePerformanceMetric(name, value, unit, context) {\n        if (!this.isEnabled) return;\n        const metric = {\n            id: `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            timestamp: new Date().toISOString(),\n            name,\n            value,\n            unit,\n            context\n        };\n        // Send performance metrics to a separate endpoint\n        fetch(\"/api/metrics\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(metric)\n        }).catch(()=>{\n        // Ignore metric sending failures\n        });\n    }\n    captureUserAction(action, element, context) {\n        if (!this.isEnabled) return;\n        const userAction = {\n            id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            timestamp: new Date().toISOString(),\n            action,\n            element,\n            page: window.location.pathname,\n            userId: this.userId,\n            sessionId: this.sessionId,\n            context\n        };\n        // Send user actions to analytics endpoint\n        fetch(\"/api/analytics\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(userAction)\n        }).catch(()=>{\n        // Ignore analytics sending failures\n        });\n    }\n    setUser(userId) {\n        this.userId = userId;\n    }\n    addContext(key, value) {\n        // Add global context that will be included with all errors\n        if (false) {}\n    }\n    generateFingerprint(error) {\n        // Generate a fingerprint for grouping similar errors\n        const message = error.message || \"Unknown error\";\n        const stack = error.stack || \"\";\n        const firstStackLine = stack.split(\"\\n\")[1] || \"\";\n        return btoa(`${message}:${firstStackLine}`).substr(0, 16);\n    }\n    getSessionId() {\n        return this.sessionId;\n    }\n    isMonitoringEnabled() {\n        return this.isEnabled;\n    }\n}\n// Create global instance\nconst errorMonitor = new ErrorMonitor();\n// Convenience functions\nconst captureError = (error, context, level)=>{\n    errorMonitor.captureError(error, context, level);\n};\nconst capturePerformanceMetric = (name, value, unit, context)=>{\n    errorMonitor.capturePerformanceMetric(name, value, unit, context);\n};\nconst captureUserAction = (action, element, context)=>{\n    errorMonitor.captureUserAction(action, element, context);\n};\nconst setUser = (userId)=>{\n    errorMonitor.setUser(userId);\n};\nconst addContext = (key, value)=>{\n    errorMonitor.addContext(key, value);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/error-monitoring.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3NtYXJ0LW9mZi1wbGFuLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0d7026c43fe2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21hcnQtb2ZmLXBsYW4vLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzPzY2MzciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwZDcwMjZjNDNmZTJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_variableName_playfair___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-playfair\"}],\"variableName\":\"playfair\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-playfair\\\"}],\\\"variableName\\\":\\\"playfair\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_variableName_playfair___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_variableName_playfair___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n/* harmony import */ var _components_providers_ToastProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/ToastProvider */ \"(rsc)/./src/components/providers/ToastProvider.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(rsc)/./src/components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_providers_ErrorMonitoringProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/ErrorMonitoringProvider */ \"(rsc)/./src/components/providers/ErrorMonitoringProvider.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Smart Off Plan - Premium Dubai Real Estate\",\n    description: \"Your trusted partner for Dubai developments. Connecting international investors with premium off-plan opportunities.\",\n    keywords: \"Dubai real estate, off-plan properties, luxury developments, property investment, Dubai properties\",\n    authors: [\n        {\n            name: \"Smart Off Plan\"\n        }\n    ],\n    creator: \"Smart Off Plan\",\n    publisher: \"Smart Off Plan\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://smartoffplan.ae\"),\n    openGraph: {\n        title: \"Smart Off Plan - Premium Dubai Real Estate\",\n        description: \"Your trusted partner for Dubai developments. Connecting international investors with premium off-plan opportunities.\",\n        url: \"https://smartoffplan.ae\",\n        siteName: \"Smart Off Plan\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Smart Off Plan - Premium Dubai Real Estate\"\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Smart Off Plan - Premium Dubai Real Estate\",\n        description: \"Your trusted partner for Dubai developments. Connecting international investors with premium off-plan opportunities.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_variableName_playfair___WEBPACK_IMPORTED_MODULE_6___default().variable)}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#d4af37\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, maximum-scale=5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)} antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ErrorMonitoringProvider__WEBPACK_IMPORTED_MODULE_4__.ErrorMonitoringProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__.PageErrorBoundary, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ToastProvider__WEBPACK_IMPORTED_MODULE_2__.ToastProvider, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/test-api/page.tsx":
/*!***********************************!*\
  !*** ./src/app/test-api/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\smart-off-plan\src\app\test-api\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ComponentErrorBoundary: () => (/* binding */ e3),
/* harmony export */   ErrorBoundary: () => (/* binding */ e0),
/* harmony export */   PageErrorBoundary: () => (/* binding */ e1),
/* harmony export */   SectionErrorBoundary: () => (/* binding */ e2),
/* harmony export */   withErrorBoundary: () => (/* binding */ e4)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\smart-off-plan\src\components\ErrorBoundary.tsx#ErrorBoundary`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\smart-off-plan\src\components\ErrorBoundary.tsx#PageErrorBoundary`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\smart-off-plan\src\components\ErrorBoundary.tsx#SectionErrorBoundary`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\smart-off-plan\src\components\ErrorBoundary.tsx#ComponentErrorBoundary`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\smart-off-plan\src\components\ErrorBoundary.tsx#withErrorBoundary`);


/***/ }),

/***/ "(rsc)/./src/components/providers/ErrorMonitoringProvider.tsx":
/*!**************************************************************!*\
  !*** ./src/components/providers/ErrorMonitoringProvider.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ErrorMonitoringProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\smart-off-plan\src\components\providers\ErrorMonitoringProvider.tsx#ErrorMonitoringProvider`);


/***/ }),

/***/ "(rsc)/./src/components/providers/ToastProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/ToastProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\smart-off-plan\src\components\providers\ToastProvider.tsx#ToastProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/lucide-react","vendor-chunks/tailwind-merge","vendor-chunks/react-hot-toast","vendor-chunks/class-variance-authority","vendor-chunks/goober","vendor-chunks/clsx","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-api%2Fpage&page=%2Ftest-api%2Fpage&appPaths=%2Ftest-api%2Fpage&pagePath=private-next-app-dir%2Ftest-api%2Fpage.tsx&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();