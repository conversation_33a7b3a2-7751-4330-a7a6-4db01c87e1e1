/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"],"variable":"--font-inter"}],"variableName":"inter"} ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Inter_Fallback_e8ce0c';src: local("Arial");ascent-override: 90.49%;descent-override: 22.56%;line-gap-override: 0.00%;size-adjust: 107.06%
}.__className_e8ce0c {font-family: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c';font-style: normal
}.__variable_e8ce0c {--font-inter: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Playfair_Display","arguments":[{"subsets":["latin"],"variable":"--font-playfair"}],"variableName":"playfair"} ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: '__Playfair_Display_65f816';
  font-style: normal;
  font-weight: 400 900;
  font-display: swap;
  src: url(/_next/static/media/47f136985ef5b5cb-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: '__Playfair_Display_65f816';
  font-style: normal;
  font-weight: 400 900;
  font-display: swap;
  src: url(/_next/static/media/4ead58c4dcc3f285-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Playfair_Display_65f816';
  font-style: normal;
  font-weight: 400 900;
  font-display: swap;
  src: url(/_next/static/media/f7c8bed65df13031-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Playfair_Display_65f816';
  font-style: normal;
  font-weight: 400 900;
  font-display: swap;
  src: url(/_next/static/media/6af6b543dd3be231-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Playfair_Display_Fallback_65f816';src: local("Times New Roman");ascent-override: 97.25%;descent-override: 22.56%;line-gap-override: 0.00%;size-adjust: 111.26%
}.__className_65f816 {font-family: '__Playfair_Display_65f816', '__Playfair_Display_Fallback_65f816';font-style: normal
}.__variable_65f816 {--font-playfair: '__Playfair_Display_65f816', '__Playfair_Display_Fallback_65f816'
}

/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/styles/globals.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: var(--font-inter), Inter, sans-serif; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
  * {
  border-color: hsl(var(--border));
  outline-color: hsl(var(--ring) / 0.5);
}

  body {
  background-color: hsl(var(--background));
  font-family: var(--font-inter), Inter, sans-serif;
  color: hsl(var(--foreground));
    font-size: var(--font-size);
    line-height: 1.6;
    color: var(--foreground);
    background-color: var(--background);
}
  /* Luxury Typography - Serif for headings */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
  font-family: var(--font-playfair), Playfair Display, serif;
    color: var(--soft-brown);
    letter-spacing: -0.02em;
}

  h1 {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
  }

  h2 {
    font-size: 2.75rem;
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1.25rem;
  }

  h3 {
    font-size: 2rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
  }

  h4 {
    font-size: 1.5rem;
    font-weight: 500;
    line-height: 1.4;
    margin-bottom: 0.75rem;
  }

  h5 {
    font-size: 1.25rem;
    font-weight: 500;
    line-height: 1.4;
    margin-bottom: 0.5rem;
  }

  h6 {
    font-size: 1.125rem;
    font-weight: 500;
    line-height: 1.4;
    margin-bottom: 0.5rem;
  }

  /* Sans-serif for body and UI text */
  p,
  span,
  div,
  label,
  button,
  input,
  textarea,
  select {
  font-family: var(--font-inter), Inter, sans-serif;
}

  p {
    font-size: 1.125rem;
    font-weight: 400;
    line-height: 1.7;
    color: var(--warm-gray);
    margin-bottom: 1rem;
  }

  label {
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.5;
    color: var(--soft-brown);
  }

  button {
    font-size: 1rem;
    font-weight: 500;
    line-height: 1.5;
    letter-spacing: 0.01em;
  }

  input,
  textarea,
  select {
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
  }
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 2rem;
  padding-left: 2rem;
}
@media (min-width: 1400px) {

  .container {
    max-width: 1400px;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.visible {
  visibility: visible;
}
.invisible {
  visibility: hidden;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.inset-0 {
  inset: 0px;
}
.inset-x-0 {
  left: 0px;
  right: 0px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.-bottom-12 {
  bottom: -3rem;
}
.-bottom-4 {
  bottom: -1rem;
}
.-bottom-6 {
  bottom: -1.5rem;
}
.-left-12 {
  left: -3rem;
}
.-left-4 {
  left: -1rem;
}
.-right-12 {
  right: -3rem;
}
.-right-4 {
  right: -1rem;
}
.-right-6 {
  right: -1.5rem;
}
.-top-12 {
  top: -3rem;
}
.-top-2 {
  top: -0.5rem;
}
.-top-4 {
  top: -1rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-14 {
  bottom: 3.5rem;
}
.bottom-20 {
  bottom: 5rem;
}
.bottom-3 {
  bottom: 0.75rem;
}
.bottom-4 {
  bottom: 1rem;
}
.bottom-8 {
  bottom: 2rem;
}
.left-0 {
  left: 0px;
}
.left-1 {
  left: 0.25rem;
}
.left-1\/2 {
  left: 50%;
}
.left-2 {
  left: 0.5rem;
}
.left-3 {
  left: 0.75rem;
}
.left-4 {
  left: 1rem;
}
.left-\[50\%\] {
  left: 50%;
}
.left-full {
  left: 100%;
}
.right-0 {
  right: 0px;
}
.right-1 {
  right: 0.25rem;
}
.right-2 {
  right: 0.5rem;
}
.right-20 {
  right: 5rem;
}
.right-3 {
  right: 0.75rem;
}
.right-4 {
  right: 1rem;
}
.right-40 {
  right: 10rem;
}
.right-60 {
  right: 15rem;
}
.top-0 {
  top: 0px;
}
.top-1\.5 {
  top: 0.375rem;
}
.top-1\/2 {
  top: 50%;
}
.top-12 {
  top: 3rem;
}
.top-16 {
  top: 4rem;
}
.top-20 {
  top: 5rem;
}
.top-3 {
  top: 0.75rem;
}
.top-3\.5 {
  top: 0.875rem;
}
.top-4 {
  top: 1rem;
}
.top-40 {
  top: 10rem;
}
.top-6 {
  top: 1.5rem;
}
.top-8 {
  top: 2rem;
}
.top-\[1px\] {
  top: 1px;
}
.top-\[50\%\] {
  top: 50%;
}
.top-\[60\%\] {
  top: 60%;
}
.top-full {
  top: 100%;
}
.isolate {
  isolation: isolate;
}
.-z-10 {
  z-index: -10;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-\[1\] {
  z-index: 1;
}
.order-1 {
  order: 1;
}
.order-2 {
  order: 2;
}
.col-start-2 {
  grid-column-start: 2;
}
.row-span-2 {
  grid-row: span 2 / span 2;
}
.row-start-1 {
  grid-row-start: 1;
}
.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-3\.5 {
  margin-left: 0.875rem;
  margin-right: 0.875rem;
}
.mx-\[10px\] {
  margin-left: 10px;
  margin-right: 10px;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-0\.5 {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-\[0px\] {
  margin-top: 0px;
  margin-bottom: 0px;
}
.-ml-4 {
  margin-left: -1rem;
}
.-mt-4 {
  margin-top: -1rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-16 {
  margin-bottom: 4rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.mb-\[24px\] {
  margin-bottom: 24px;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-\[0px\] {
  margin-left: 0px;
}
.ml-auto {
  margin-left: auto;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mr-6 {
  margin-right: 1.5rem;
}
.mr-\[0px\] {
  margin-right: 0px;
}
.mt-0 {
  margin-top: 0px;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-1\.5 {
  margin-top: 0.375rem;
}
.mt-12 {
  margin-top: 3rem;
}
.mt-16 {
  margin-top: 4rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-\[0px\] {
  margin-top: 0px;
}
.mt-auto {
  margin-top: auto;
}
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.table-caption {
  display: table-caption;
}
.table-cell {
  display: table-cell;
}
.table-row {
  display: table-row;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.aspect-\[16\/9\] {
  aspect-ratio: 16/9;
}
.aspect-\[4\/3\] {
  aspect-ratio: 4/3;
}
.aspect-square {
  aspect-ratio: 1 / 1;
}
.aspect-video {
  aspect-ratio: 16 / 9;
}
.size-10 {
  width: 2.5rem;
  height: 2.5rem;
}
.size-2 {
  width: 0.5rem;
  height: 0.5rem;
}
.size-2\.5 {
  width: 0.625rem;
  height: 0.625rem;
}
.size-3 {
  width: 0.75rem;
  height: 0.75rem;
}
.size-3\.5 {
  width: 0.875rem;
  height: 0.875rem;
}
.size-4 {
  width: 1rem;
  height: 1rem;
}
.size-7 {
  width: 1.75rem;
  height: 1.75rem;
}
.size-8 {
  width: 2rem;
  height: 2rem;
}
.size-9 {
  width: 2.25rem;
  height: 2.25rem;
}
.size-full {
  width: 100%;
  height: 100%;
}
.h-0 {
  height: 0px;
}
.h-0\.5 {
  height: 0.125rem;
}
.h-1 {
  height: 0.25rem;
}
.h-1\.5 {
  height: 0.375rem;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\.5 {
  height: 0.625rem;
}
.h-20 {
  height: 5rem;
}
.h-24 {
  height: 6rem;
}
.h-3 {
  height: 0.75rem;
}
.h-32 {
  height: 8rem;
}
.h-4 {
  height: 1rem;
}
.h-40 {
  height: 10rem;
}
.h-48 {
  height: 12rem;
}
.h-5 {
  height: 1.25rem;
}
.h-56 {
  height: 14rem;
}
.h-6 {
  height: 1.5rem;
}
.h-7 {
  height: 1.75rem;
}
.h-8 {
  height: 2rem;
}
.h-80 {
  height: 20rem;
}
.h-9 {
  height: 2.25rem;
}
.h-96 {
  height: 24rem;
}
.h-\[1\.15rem\] {
  height: 1.15rem;
}
.h-\[600px\] {
  height: 600px;
}
.h-\[calc\(100\%-1px\)\] {
  height: calc(100% - 1px);
}
.h-\[var\(--radix-navigation-menu-viewport-height\)\] {
  height: var(--radix-navigation-menu-viewport-height);
}
.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.h-screen {
  height: 100vh;
}
.h-svh {
  height: 100svh;
}
.max-h-96 {
  max-height: 24rem;
}
.max-h-\[300px\] {
  max-height: 300px;
}
.max-h-\[600px\] {
  max-height: 600px;
}
.max-h-\[90vh\] {
  max-height: 90vh;
}
.min-h-0 {
  min-height: 0px;
}
.min-h-16 {
  min-height: 4rem;
}
.min-h-4 {
  min-height: 1rem;
}
.min-h-\[120px\] {
  min-height: 120px;
}
.min-h-\[200px\] {
  min-height: 200px;
}
.min-h-screen {
  min-height: 100vh;
}
.min-h-svh {
  min-height: 100svh;
}
.w-0 {
  width: 0px;
}
.w-1 {
  width: 0.25rem;
}
.w-1\/2 {
  width: 50%;
}
.w-10 {
  width: 2.5rem;
}
.w-12 {
  width: 3rem;
}
.w-14 {
  width: 3.5rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\.5 {
  width: 0.625rem;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\/4 {
  width: 75%;
}
.w-32 {
  width: 8rem;
}
.w-4 {
  width: 1rem;
}
.w-40 {
  width: 10rem;
}
.w-5 {
  width: 1.25rem;
}
.w-52 {
  width: 13rem;
}
.w-56 {
  width: 14rem;
}
.w-6 {
  width: 1.5rem;
}
.w-64 {
  width: 16rem;
}
.w-7 {
  width: 1.75rem;
}
.w-72 {
  width: 18rem;
}
.w-8 {
  width: 2rem;
}
.w-80 {
  width: 20rem;
}
.w-9 {
  width: 2.25rem;
}
.w-\[100px\] {
  width: 100px;
}
.w-\[180px\] {
  width: 180px;
}
.w-auto {
  width: auto;
}
.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}
.w-full {
  width: 100%;
}
.w-max {
  width: -moz-max-content;
  width: max-content;
}
.w-px {
  width: 1px;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-10 {
  min-width: 2.5rem;
}
.min-w-5 {
  min-width: 1.25rem;
}
.min-w-8 {
  min-width: 2rem;
}
.min-w-9 {
  min-width: 2.25rem;
}
.min-w-\[100px\] {
  min-width: 100px;
}
.min-w-\[120px\] {
  min-width: 120px;
}
.min-w-\[12rem\] {
  min-width: 12rem;
}
.min-w-\[20px\] {
  min-width: 20px;
}
.min-w-\[220px\] {
  min-width: 220px;
}
.min-w-\[8rem\] {
  min-width: 8rem;
}
.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}
.min-w-max {
  min-width: -moz-max-content;
  min-width: max-content;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-5xl {
  max-width: 64rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.max-w-\[calc\(100\%-2rem\)\] {
  max-width: calc(100% - 2rem);
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-max {
  max-width: -moz-max-content;
  max-width: max-content;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-xl {
  max-width: 36rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.grow {
  flex-grow: 1;
}
.grow-0 {
  flex-grow: 0;
}
.basis-full {
  flex-basis: 100%;
}
.caption-bottom {
  caption-side: bottom;
}
.border-collapse {
  border-collapse: collapse;
}
.origin-center {
  transform-origin: center;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-px {
  --tw-translate-x: -1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-px {
  --tw-translate-x: 1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0\.5 {
  --tw-translate-y: 0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-2 {
  --tw-translate-y: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[calc\(-50\%_-_2px\)\] {
  --tw-translate-y: calc(-50% - 2px);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-12 {
  --tw-rotate: -12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-12 {
  --tw-rotate: 12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-0 {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-150 {
  --tw-scale-x: 1.5;
  --tw-scale-y: 1.5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-\[1\.02\] {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes bounce {

  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}
.animate-bounce {
  animation: bounce 1s infinite;
}
@keyframes ping {

  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-default {
  cursor: default;
}
.cursor-pointer {
  cursor: pointer;
}
.touch-none {
  touch-action: none;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize-none {
  resize: none;
}
.resize {
  resize: both;
}
.scroll-my-1 {
  scroll-margin-top: 0.25rem;
  scroll-margin-bottom: 0.25rem;
}
.scroll-py-1 {
  scroll-padding-top: 0.25rem;
  scroll-padding-bottom: 0.25rem;
}
.list-none {
  list-style-type: none;
}
.auto-rows-min {
  grid-auto-rows: min-content;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-\[0_1fr\] {
  grid-template-columns: 0 1fr;
}
.grid-rows-\[auto_auto\] {
  grid-template-rows: auto auto;
}
.flex-row {
  flex-direction: row;
}
.flex-col {
  flex-direction: column;
}
.flex-col-reverse {
  flex-direction: column-reverse;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.items-stretch {
  align-items: stretch;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-items-start {
  justify-items: start;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-12 {
  gap: 3rem;
}
.gap-16 {
  gap: 4rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-y-0\.5 {
  row-gap: 0.125rem;
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-20 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.self-start {
  align-self: flex-start;
}
.justify-self-end {
  justify-self: end;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.text-balance {
  text-wrap: balance;
}
.break-words {
  overflow-wrap: break-word;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-3xl {
  border-radius: 1.5rem;
}
.rounded-\[2px\] {
  border-radius: 2px;
}
.rounded-\[4px\] {
  border-radius: 4px;
}
.rounded-\[inherit\] {
  border-radius: inherit;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: var(--radius);
}
.rounded-md {
  border-radius: calc(var(--radius) - 2px);
}
.rounded-none {
  border-radius: 0px;
}
.rounded-sm {
  border-radius: calc(var(--radius) - 4px);
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-tl-sm {
  border-top-left-radius: calc(var(--radius) - 4px);
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-2 {
  border-width: 2px;
}
.border-\[1\.5px\] {
  border-width: 1.5px;
}
.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-l {
  border-left-width: 1px;
}
.border-l-4 {
  border-left-width: 4px;
}
.border-r {
  border-right-width: 1px;
}
.border-r-4 {
  border-right-width: 4px;
}
.border-t {
  border-top-width: 1px;
}
.border-t-4 {
  border-top-width: 4px;
}
.border-dashed {
  border-style: dashed;
}
.border-border {
  border-color: hsl(var(--border));
}
.border-border\/30 {
  border-color: hsl(var(--border) / 0.3);
}
.border-border\/50 {
  border-color: hsl(var(--border) / 0.5);
}
.border-current {
  border-color: currentColor;
}
.border-gold {
  --tw-border-opacity: 1;
  border-color: rgb(212 175 55 / var(--tw-border-opacity, 1));
}
.border-gold\/10 {
  border-color: rgb(212 175 55 / 0.1);
}
.border-gold\/20 {
  border-color: rgb(212 175 55 / 0.2);
}
.border-gold\/30 {
  border-color: rgb(212 175 55 / 0.3);
}
.border-gold\/70 {
  border-color: rgb(212 175 55 / 0.7);
}
.border-green-100\/50 {
  border-color: rgb(220 252 231 / 0.5);
}
.border-green-500\/30 {
  border-color: rgb(34 197 94 / 0.3);
}
.border-input {
  border-color: hsl(var(--input));
}
.border-primary {
  border-color: hsl(var(--primary));
}
.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}
.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}
.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-soft-brown {
  --tw-border-opacity: 1;
  border-color: rgb(139 69 19 / var(--tw-border-opacity, 1));
}
.border-soft-brown\/10 {
  border-color: rgb(139 69 19 / 0.1);
}
.border-soft-brown\/20 {
  border-color: rgb(139 69 19 / 0.2);
}
.border-soft-brown\/30 {
  border-color: rgb(139 69 19 / 0.3);
}
.border-transparent {
  border-color: transparent;
}
.border-warm-gray\/30 {
  border-color: rgb(139 134 128 / 0.3);
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}
.border-white\/30 {
  border-color: rgb(255 255 255 / 0.3);
}
.border-l-transparent {
  border-left-color: transparent;
}
.border-t-soft-brown {
  --tw-border-opacity: 1;
  border-top-color: rgb(139 69 19 / var(--tw-border-opacity, 1));
}
.border-t-transparent {
  border-top-color: transparent;
}
.border-t-white {
  --tw-border-opacity: 1;
  border-top-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.bg-\[rgba\(0\2c 0\2c 0\2c 0\)\] {
  background-color: rgba(0,0,0,0);
}
.bg-\[rgba\(139\2c 115\2c 85\2c 1\)\] {
  background-color: rgba(139,115,85,1);
}
.bg-\[rgba\(255\2c 255\2c 255\2c 1\)\] {
  background-color: rgba(255,255,255,1);
}
.bg-\[rgba\(30\2c 26\2c 26\2c 0\)\] {
  background-color: rgba(30,26,26,0);
}
.bg-accent {
  background-color: hsl(var(--accent));
}
.bg-background {
  background-color: hsl(var(--background));
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/40 {
  background-color: rgb(0 0 0 / 0.4);
}
.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}
.bg-black\/70 {
  background-color: rgb(0 0 0 / 0.7);
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-border {
  background-color: hsl(var(--border));
}
.bg-card {
  background-color: hsl(var(--card));
}
.bg-current {
  background-color: currentColor;
}
.bg-destructive {
  background-color: hsl(var(--destructive));
}
.bg-emerald-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));
}
.bg-foreground {
  background-color: hsl(var(--foreground));
}
.bg-gold {
  --tw-bg-opacity: 1;
  background-color: rgb(212 175 55 / var(--tw-bg-opacity, 1));
}
.bg-gold\/10 {
  background-color: rgb(212 175 55 / 0.1);
}
.bg-gold\/15 {
  background-color: rgb(212 175 55 / 0.15);
}
.bg-gold\/20 {
  background-color: rgb(212 175 55 / 0.2);
}
.bg-gold\/30 {
  background-color: rgb(212 175 55 / 0.3);
}
.bg-gold\/5 {
  background-color: rgb(212 175 55 / 0.05);
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-ivory {
  --tw-bg-opacity: 1;
  background-color: rgb(248 246 240 / var(--tw-bg-opacity, 1));
}
.bg-muted {
  background-color: hsl(var(--muted));
}
.bg-muted\/50 {
  background-color: hsl(var(--muted) / 0.5);
}
.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}
.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-popover {
  background-color: hsl(var(--popover));
}
.bg-primary {
  background-color: hsl(var(--primary));
}
.bg-primary\/20 {
  background-color: hsl(var(--primary) / 0.2);
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-secondary {
  background-color: hsl(var(--secondary));
}
.bg-soft-brown {
  --tw-bg-opacity: 1;
  background-color: rgb(139 69 19 / var(--tw-bg-opacity, 1));
}
.bg-soft-brown\/10 {
  background-color: rgb(139 69 19 / 0.1);
}
.bg-soft-brown\/15 {
  background-color: rgb(139 69 19 / 0.15);
}
.bg-soft-brown\/20 {
  background-color: rgb(139 69 19 / 0.2);
}
.bg-transparent {
  background-color: transparent;
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}
.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}
.bg-white\/60 {
  background-color: rgb(255 255 255 / 0.6);
}
.bg-white\/70 {
  background-color: rgb(255 255 255 / 0.7);
}
.bg-white\/80 {
  background-color: rgb(255 255 255 / 0.8);
}
.bg-white\/90 {
  background-color: rgb(255 255 255 / 0.9);
}
.bg-white\/95 {
  background-color: rgb(255 255 255 / 0.95);
}
.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-bl {
  background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
}
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.from-amber-600\/70 {
  --tw-gradient-from: rgb(217 119 6 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(217 119 6 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/20 {
  --tw-gradient-from: rgb(0 0 0 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-100 {
  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-600\/70 {
  --tw-gradient-from: rgb(37 99 235 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-emerald-600\/70 {
  --tw-gradient-from: rgb(5 150 105 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(5 150 105 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gold {
  --tw-gradient-from: #D4AF37 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(212 175 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gold\/10 {
  --tw-gradient-from: rgb(212 175 55 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(212 175 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gold\/15 {
  --tw-gradient-from: rgb(212 175 55 / 0.15) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(212 175 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gold\/20 {
  --tw-gradient-from: rgb(212 175 55 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(212 175 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gold\/5 {
  --tw-gradient-from: rgb(212 175 55 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(212 175 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gold\/90 {
  --tw-gradient-from: rgb(212 175 55 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(212 175 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-100\/50 {
  --tw-gradient-from: rgb(220 252 231 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(220 252 231 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-50 {
  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 253 244 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-ivory {
  --tw-gradient-from: #F8F6F0 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 246 240 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-100 {
  --tw-gradient-from: #ffedd5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 237 213 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-100 {
  --tw-gradient-from: #f3e8ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(243 232 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-600\/70 {
  --tw-gradient-from: rgb(147 51 234 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-100 {
  --tw-gradient-from: #fee2e2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(254 226 226 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-rose-600\/70 {
  --tw-gradient-from: rgb(225 29 72 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(225 29 72 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-soft-brown {
  --tw-gradient-from: #8B4513 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 69 19 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-soft-brown\/10 {
  --tw-gradient-from: rgb(139 69 19 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 69 19 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-soft-brown\/80 {
  --tw-gradient-from: rgb(139 69 19 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 69 19 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white {
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-blue-800\/60 {
  --tw-gradient-to: rgb(30 64 175 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(30 64 175 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-gold {
  --tw-gradient-to: rgb(212 175 55 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #D4AF37 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-gold\/5 {
  --tw-gradient-to: rgb(212 175 55 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(212 175 55 / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-gold\/90 {
  --tw-gradient-to: rgb(212 175 55 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(212 175 55 / 0.9) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-indigo-700\/60 {
  --tw-gradient-to: rgb(67 56 202 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(67 56 202 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-ivory {
  --tw-gradient-to: rgb(248 246 240 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #F8F6F0 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-orange-700\/60 {
  --tw-gradient-to: rgb(194 65 12 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(194 65 12 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-pink-700\/60 {
  --tw-gradient-to: rgb(190 24 93 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(190 24 93 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-soft-brown {
  --tw-gradient-to: rgb(139 69 19 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #8B4513 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-teal-700\/60 {
  --tw-gradient-to: rgb(15 118 110 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(15 118 110 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-transparent {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white\/95 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.95) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-black\/10 {
  --tw-gradient-to: rgb(0 0 0 / 0.1) var(--tw-gradient-to-position);
}
.to-black\/80 {
  --tw-gradient-to: rgb(0 0 0 / 0.8) var(--tw-gradient-to-position);
}
.to-blue-50 {
  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);
}
.to-emerald-50 {
  --tw-gradient-to: #ecfdf5 var(--tw-gradient-to-position);
}
.to-gold {
  --tw-gradient-to: #D4AF37 var(--tw-gradient-to-position);
}
.to-gold\/10 {
  --tw-gradient-to: rgb(212 175 55 / 0.1) var(--tw-gradient-to-position);
}
.to-gold\/30 {
  --tw-gradient-to: rgb(212 175 55 / 0.3) var(--tw-gradient-to-position);
}
.to-gold\/5 {
  --tw-gradient-to: rgb(212 175 55 / 0.05) var(--tw-gradient-to-position);
}
.to-gold\/80 {
  --tw-gradient-to: rgb(212 175 55 / 0.8) var(--tw-gradient-to-position);
}
.to-green-50 {
  --tw-gradient-to: #f0fdf4 var(--tw-gradient-to-position);
}
.to-ivory {
  --tw-gradient-to: #F8F6F0 var(--tw-gradient-to-position);
}
.to-ivory\/50 {
  --tw-gradient-to: rgb(248 246 240 / 0.5) var(--tw-gradient-to-position);
}
.to-orange-50 {
  --tw-gradient-to: #fff7ed var(--tw-gradient-to-position);
}
.to-purple-50 {
  --tw-gradient-to: #faf5ff var(--tw-gradient-to-position);
}
.to-red-50 {
  --tw-gradient-to: #fef2f2 var(--tw-gradient-to-position);
}
.to-soft-brown {
  --tw-gradient-to: #8B4513 var(--tw-gradient-to-position);
}
.to-soft-brown\/5 {
  --tw-gradient-to: rgb(139 69 19 / 0.05) var(--tw-gradient-to-position);
}
.to-soft-brown\/80 {
  --tw-gradient-to: rgb(139 69 19 / 0.8) var(--tw-gradient-to-position);
}
.to-soft-brown\/90 {
  --tw-gradient-to: rgb(139 69 19 / 0.9) var(--tw-gradient-to-position);
}
.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.to-white\/90 {
  --tw-gradient-to: rgb(255 255 255 / 0.9) var(--tw-gradient-to-position);
}
.fill-current {
  fill: currentColor;
}
.fill-gold {
  fill: #D4AF37;
}
.fill-primary {
  fill: hsl(var(--primary));
}
.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.p-0 {
  padding: 0px;
}
.p-1 {
  padding: 0.25rem;
}
.p-1\.5 {
  padding: 0.375rem;
}
.p-12 {
  padding: 3rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.p-\[3px\] {
  padding: 3px;
}
.p-px {
  padding: 1px;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-12 {
  padding-left: 3rem;
  padding-right: 3rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.px-\[82px\] {
  padding-left: 82px;
  padding-right: 82px;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.py-\[0px\] {
  padding-top: 0px;
  padding-bottom: 0px;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pb-6 {
  padding-bottom: 1.5rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-12 {
  padding-left: 3rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pl-4 {
  padding-left: 1rem;
}
.pl-8 {
  padding-left: 2rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pr-12 {
  padding-right: 3rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-2\.5 {
  padding-right: 0.625rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-8 {
  padding-right: 2rem;
}
.pt-0 {
  padding-top: 0px;
}
.pt-1 {
  padding-top: 0.25rem;
}
.pt-12 {
  padding-top: 3rem;
}
.pt-20 {
  padding-top: 5rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.pt-8 {
  padding-top: 2rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.align-middle {
  vertical-align: middle;
}
.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl {
  font-size: 3rem;
  line-height: 1;
}
.text-\[0\.8rem\] {
  font-size: 0.8rem;
}
.text-\[14px\] {
  font-size: 14px;
}
.text-\[15px\] {
  font-size: 15px;
}
.text-\[16px\] {
  font-size: 16px;
}
.text-\[20px\] {
  font-size: 20px;
}
.text-\[24px\] {
  font-size: 24px;
}
.text-\[36px\] {
  font-size: 36px;
}
.text-\[40px\] {
  font-size: 40px;
}
.text-\[48px\] {
  font-size: 48px;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.lowercase {
  text-transform: lowercase;
}
.capitalize {
  text-transform: capitalize;
}
.italic {
  font-style: italic;
}
.tabular-nums {
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}
.leading-\[1\.2\] {
  line-height: 1.2;
}
.leading-none {
  line-height: 1;
}
.leading-relaxed {
  line-height: 1.625;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-wide {
  letter-spacing: 0.025em;
}
.tracking-widest {
  letter-spacing: 0.1em;
}
.text-\[rgba\(0\2c 0\2c 0\2c 0\.49\)\] {
  color: rgba(0,0,0,0.49);
}
.text-\[rgba\(0\2c 0\2c 0\2c 1\)\] {
  color: rgba(0,0,0,1);
}
.text-\[rgba\(138\2c 121\2c 104\2c 0\.7\)\] {
  color: rgba(138,121,104,0.7);
}
.text-\[rgba\(139\2c 115\2c 85\2c 1\)\] {
  color: rgba(139,115,85,1);
}
.text-\[rgba\(255\2c 255\2c 255\2c 1\)\] {
  color: rgba(255,255,255,1);
}
.text-\[rgba\(30\2c 26\2c 26\2c 0\.5\)\] {
  color: rgba(30,26,26,0.5);
}
.text-\[rgba\(30\2c 26\2c 26\2c 0\.6\)\] {
  color: rgba(30,26,26,0.6);
}
.text-\[rgba\(30\2c 26\2c 26\2c 0\.7\)\] {
  color: rgba(30,26,26,0.7);
}
.text-\[rgba\(30\2c 26\2c 26\2c 0\.8\)\] {
  color: rgba(30,26,26,0.8);
}
.text-\[rgba\(30\2c 26\2c 26\2c 1\)\] {
  color: rgba(30,26,26,1);
}
.text-accent-foreground {
  color: hsl(var(--accent-foreground));
}
.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-card-foreground {
  color: hsl(var(--card-foreground));
}
.text-current {
  color: currentColor;
}
.text-destructive {
  color: hsl(var(--destructive));
}
.text-emerald-500 {
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity, 1));
}
.text-foreground {
  color: hsl(var(--foreground));
}
.text-gold {
  --tw-text-opacity: 1;
  color: rgb(212 175 55 / var(--tw-text-opacity, 1));
}
.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}
.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}
.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.text-popover-foreground {
  color: hsl(var(--popover-foreground));
}
.text-primary {
  color: hsl(var(--primary));
}
.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}
.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-secondary-foreground {
  color: hsl(var(--secondary-foreground));
}
.text-soft-brown {
  --tw-text-opacity: 1;
  color: rgb(139 69 19 / var(--tw-text-opacity, 1));
}
.text-tan {
  --tw-text-opacity: 1;
  color: rgb(210 180 140 / var(--tw-text-opacity, 1));
}
.text-warm-gray {
  --tw-text-opacity: 1;
  color: rgb(139 134 128 / var(--tw-text-opacity, 1));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}
.text-white\/90 {
  color: rgb(255 255 255 / 0.9);
}
.underline-offset-4 {
  text-underline-offset: 4px;
}
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.opacity-30 {
  opacity: 0.3;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-70 {
  opacity: 0.7;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-border));
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_2px_12px_-2px_rgba\(139\2c 115\2c 85\2c 0\.08\)\] {
  --tw-shadow: 0 2px 12px -2px rgba(139,115,85,0.08);
  --tw-shadow-colored: 0 2px 12px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_4px_16px_-2px_rgba\(212\2c 175\2c 55\2c 0\.3\)\] {
  --tw-shadow: 0 4px 16px -2px rgba(212,175,55,0.3);
  --tw-shadow-colored: 0 4px 16px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_4px_20px_-2px_rgba\(139\2c 115\2c 85\2c 0\.08\)\2c 0_2px_8px_-2px_rgba\(139\2c 115\2c 85\2c 0\.04\)\] {
  --tw-shadow: 0 4px 20px -2px rgba(139,115,85,0.08),0 2px 8px -2px rgba(139,115,85,0.04);
  --tw-shadow-colored: 0 4px 20px -2px var(--tw-shadow-color), 0 2px 8px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_4px_20px_-2px_rgba\(139\2c 115\2c 85\2c 0\.08\)\] {
  --tw-shadow: 0 4px 20px -2px rgba(139,115,85,0.08);
  --tw-shadow-colored: 0 4px 20px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_4px_20px_-2px_rgba\(212\2c 175\2c 55\2c 0\.3\)\] {
  --tw-shadow: 0 4px 20px -2px rgba(212,175,55,0.3);
  --tw-shadow-colored: 0 4px 20px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_8px_32px_-4px_rgba\(139\2c 115\2c 85\2c 0\.08\)\] {
  --tw-shadow: 0 8px 32px -4px rgba(139,115,85,0.08);
  --tw-shadow-colored: 0 8px 32px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_8px_32px_-4px_rgba\(139\2c 115\2c 85\2c 0\.12\)\2c 0_4px_16px_-4px_rgba\(139\2c 115\2c 85\2c 0\.08\)\] {
  --tw-shadow: 0 8px 32px -4px rgba(139,115,85,0.12),0 4px 16px -4px rgba(139,115,85,0.08);
  --tw-shadow-colored: 0 8px 32px -4px var(--tw-shadow-color), 0 4px 16px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_8px_32px_-4px_rgba\(212\2c 175\2c 55\2c 0\.25\)\2c 0_4px_16px_-4px_rgba\(212\2c 175\2c 55\2c 0\.15\)\] {
  --tw-shadow: 0 8px 32px -4px rgba(212,175,55,0.25),0 4px 16px -4px rgba(212,175,55,0.15);
  --tw-shadow-colored: 0 8px 32px -4px var(--tw-shadow-color), 0 4px 16px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_8px_32px_-4px_rgba\(212\2c 175\2c 55\2c 0\.3\)\2c 0_4px_16px_-4px_rgba\(212\2c 175\2c 55\2c 0\.2\)\] {
  --tw-shadow: 0 8px 32px -4px rgba(212,175,55,0.3),0 4px 16px -4px rgba(212,175,55,0.2);
  --tw-shadow-colored: 0 8px 32px -4px var(--tw-shadow-color), 0 4px 16px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-inner {
  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-gold\/50 {
  --tw-shadow-color: rgb(212 175 55 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-gold {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(212 175 55 / var(--tw-ring-opacity, 1));
}
.ring-ring\/50 {
  --tw-ring-color: hsl(var(--ring) / 0.5);
}
.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[color\2c box-shadow\] {
  transition-property: color,box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[left\2c right\2c width\] {
  transition-property: left,right,width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[margin\2c opacity\] {
  transition-property: margin,opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[width\2c height\2c padding\] {
  transition-property: width,height,padding;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[width\] {
  transition-property: width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-none {
  transition-property: none;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-1000 {
  transition-duration: 1000ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.duration-700 {
  transition-duration: 700ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear {
  transition-timing-function: linear;
}
@keyframes enter {

  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit {

  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.animate-in {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.fade-in-0 {
  --tw-enter-opacity: 0;
}
.zoom-in-95 {
  --tw-enter-scale: .95;
}
.duration-1000 {
  animation-duration: 1000ms;
}
.duration-200 {
  animation-duration: 200ms;
}
.duration-300 {
  animation-duration: 300ms;
}
.duration-500 {
  animation-duration: 500ms;
}
.duration-700 {
  animation-duration: 700ms;
}
.ease-in-out {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear {
  animation-timing-function: linear;
}
.\[animation-delay\:-0\.15s\] {
  animation-delay: -0.15s;
}
.\[animation-delay\:-0\.3s\] {
  animation-delay: -0.3s;
}
.\[animation-delay\:0\.2s\] {
  animation-delay: 0.2s;
}
.\[animation-delay\:0\.4s\] {
  animation-delay: 0.4s;
}

@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

@custom-variant dark (&:is(.dark *));

:root {
  --font-size: 16px;

  /* Luxury neutral color palette */
  --background: #fefdfb;
  --foreground: #3c3530;
  --card: #ffffff;
  --card-foreground: #3c3530;
  --popover: #ffffff;
  --popover-foreground: #3c3530;
  --primary: #8b7355;
  --primary-foreground: #ffffff;
  --secondary: #f8f6f3;
  --secondary-foreground: #3c3530;
  --muted: #f5f3f0;
  --muted-foreground: #8a7968;
  --accent: #d4af37;
  --accent-foreground: #3c3530;
  --destructive: #d97706;
  --destructive-foreground: #ffffff;
  --border: #e8e4df;
  --input: transparent;
  --input-background: #fefdfb;
  --switch-background: #e8e4df;
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --ring: #d4af37;
  --chart-1: #8b7355;
  --chart-2: #d4af37;
  --chart-3: #c19a6b;
  --chart-4: #a0845c;
  --chart-5: #b8956d;
  --radius: 0.75rem;
  --sidebar: #f8f6f3;
  --sidebar-foreground: #3c3530;
  --sidebar-primary: #8b7355;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f5f3f0;
  --sidebar-accent-foreground: #3c3530;
  --sidebar-border: #e8e4df;
  --sidebar-ring: #d4af37;

  /* Luxury theme colors */
  --ivory: #fefdfb;
  --beige: #f5f1eb;
  --tan: #d2c7b8;
  --soft-brown: #8b7355;
  --gold: #d4af37;
  --light-gold: #f4e8c1;
  --warm-gray: #8a7968;
  --soft-gray: #c9bfb3;

  /* Splash screen luxury colors */
  --charcoal: #2c2420;
  --deep-charcoal: #1a1512;
  --rich-charcoal: #3a2f26;
  --splash-gold: #d4af37;
  --splash-ivory: #fefdfb;
  --splash-gold-glow: rgba(212, 175, 55, 0.6);
  --splash-ambient: rgba(212, 175, 55, 0.1);

  /* Luxury spacing */
  --section-padding: 5rem;
  --large-padding: 2rem;
  --medium-padding: 1.5rem;
  --small-padding: 1rem;
  --container-max-width: 1200px;

  /* Animation timing variables */
  --splash-logo-trace-duration: 1000ms;
  --splash-glow-delay: 1200ms;
  --splash-fade-delay: 1800ms;
  --splash-exit-delay: 3000ms;
  --splash-complete-delay: 4000ms;
}

.dark {
  /* Keep light theme for luxury feel - no dark mode */
  --background: #fefdfb;
  --foreground: #3c3530;
  --card: #ffffff;
  --card-foreground: #3c3530;
  --popover: #ffffff;
  --popover-foreground: #3c3530;
  --primary: #8b7355;
  --primary-foreground: #ffffff;
  --secondary: #f8f6f3;
  --secondary-foreground: #3c3530;
  --muted: #f5f3f0;
  --muted-foreground: #8a7968;
  --accent: #d4af37;
  --accent-foreground: #3c3530;
  --destructive: #d97706;
  --destructive-foreground: #ffffff;
  --border: #e8e4df;
  --input: #fefdfb;
  --ring: #d4af37;
  --chart-1: #8b7355;
  --chart-2: #d4af37;
  --chart-3: #c19a6b;
  --chart-4: #a0845c;
  --chart-5: #b8956d;
  --sidebar: #f8f6f3;
  --sidebar-foreground: #3c3530;
  --sidebar-primary: #8b7355;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f5f3f0;
  --sidebar-accent-foreground: #3c3530;
  --sidebar-border: #e8e4df;
  --sidebar-ring: #d4af37;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-switch-background: var(--switch-background);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Luxury colors */
  --color-ivory: var(--ivory);
  --color-beige: var(--beige);
  --color-tan: var(--tan);
  --color-soft-brown: var(--soft-brown);
  --color-gold: var(--gold);
  --color-light-gold: var(--light-gold);
  --color-warm-gray: var(--warm-gray);
  --color-soft-gray: var(--soft-gray);
  --color-charcoal: var(--charcoal);
  --color-deep-charcoal: var(--deep-charcoal);
  --color-rich-charcoal: var(--rich-charcoal);
  --color-splash-gold: var(--splash-gold);
  --color-splash-ivory: var(--splash-ivory);

  /* Legacy color support for existing components */
  --color-light-gray: var(--beige);
  --color-deep-blue: var(--soft-brown);
  --color-emerald-green: var(--gold);
}

/* Luxury Container */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* Luxury spacing utilities */
.section-padding {
  padding: var(--section-padding) 0;
}

.large-padding {
  padding: var(--large-padding);
}

.medium-padding {
  padding: var(--medium-padding);
}

.small-padding {
  padding: var(--small-padding);
}

/* Responsive typography */
@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.75rem;
  }

  .section-padding {
    padding: 3.5rem 0;
  }
}

@media (max-width: 640px) {
  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.75rem;
  }

  h3 {
    font-size: 1.5rem;
  }

  .section-padding {
    padding: 3rem 0;
  }

  .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}

/* Font utility classes */
.font-heading {
  font-family: var(--font-playfair), Playfair Display, serif;
}

.font-body {
  font-family: var(--font-inter), Inter, sans-serif;
}

/* Luxury accent elements */
.gold-accent {
  color: var(--gold);
}

.gold-bg {
  background-color: var(--gold);
}

.beige-bg {
  background-color: var(--beige);
}

.ivory-bg {
  background-color: var(--ivory);
}

.soft-brown-text {
  color: var(--soft-brown);
}

.warm-gray-text {
  color: var(--warm-gray);
}

/* Luxury dividers */
.luxury-divider {
  height: 1px;
  background: linear-gradient(to right, transparent, var(--gold), transparent);
  margin: 3rem 0;
}

/* Smooth transitions for luxury feel */
* {
  transition: color 0.3s ease, background-color 0.3s ease,
    border-color 0.3s ease;
}

/* Horizontal scroll styling */
.overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: var(--gold) var(--beige);
  scroll-behavior: smooth;
}

.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: var(--beige);
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: var(--gold);
  border-radius: 4px;
  -webkit-transition: background 0.3s ease;
  transition: background 0.3s ease;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: var(--soft-brown);
}

/* ========================================
   CINEMATIC SPLASH SCREEN ANIMATIONS
   ======================================== */

/* Cinematic easing functions */
:root {
  --ease-in-expo: cubic-bezier(0.95, 0.05, 0.795, 0.035);
  --ease-out-expo: cubic-bezier(0.19, 1, 0.22, 1);
  --ease-in-out-expo: cubic-bezier(0.87, 0, 0.13, 1);
  --ease-out-quart: cubic-bezier(0.165, 0.84, 0.44, 1);
  --ease-in-out-quart: cubic-bezier(0.77, 0, 0.175, 1);
  --ease-out-circ: cubic-bezier(0.075, 0.82, 0.165, 1);
  --ease-in-out-back: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ========================================
   APP CONTAINER & SMOOTH TRANSITIONS
   ======================================== */

/* Main app container for managing splash/content transitions */
.app-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow-x: hidden;
  background: var(--ivory);
}

/* Splash overlay for perfect layering */
.splash-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  opacity: 1;
  transition: opacity 800ms var(--ease-in-out-expo),
    transform 800ms var(--ease-in-out-expo);
  transform: scale(1);
}

.splash-overlay-exiting {
  opacity: 0;
  transform: scale(1.02);
  pointer-events: none;
}

/* Main content container with smooth entrance */
.app-content {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: var(--ivory);
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Content states for seamless transitions */
.app-content-hidden {
  opacity: 0;
  transform: scale(0.97) translateY(20px);
  transition: none;
}

.app-content-entering {
  opacity: 0;
  transform: scale(0.97) translateY(20px);
  animation: contentSmoothEntrance 1000ms var(--ease-out-quart) 200ms forwards;
}

.app-content-visible {
  opacity: 1;
  transform: scale(1) translateY(0);
  transition: opacity 300ms var(--ease-out-quart),
    transform 300ms var(--ease-out-quart);
}

/* Splash screen container */
.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  overflow: hidden;

  /* Multi-layered gradient background */
  background: radial-gradient(
      circle at 20% 80%,
      var(--splash-ambient) 0%,
      transparent 40%
    ),
    radial-gradient(
      circle at 80% 20%,
      var(--splash-ambient) 0%,
      transparent 40%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(212, 175, 55, 0.05) 0%,
      transparent 60%
    ),
    linear-gradient(
      135deg,
      var(--deep-charcoal) 0%,
      var(--charcoal) 40%,
      var(--rich-charcoal) 100%
    );
  background-size: 400% 400%, 300% 300%, 500% 500%, 100% 100%;
  animation: splashBackgroundShift 12s ease-in-out infinite;
}

/* Golden texture overlay */
.splash-screen::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 30% 70%,
      rgba(212, 175, 55, 0.08) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 30%,
      rgba(212, 175, 55, 0.06) 0%,
      transparent 50%
    );
  opacity: 0.6;
  animation: textureShift 8s ease-in-out infinite alternate;
}

/* Abstract Dubai skyline silhouettes */
.splash-screen::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 35vh;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 300'%3E%3Cpath d='M0,300 L0,250 L40,250 L40,180 L80,180 L80,200 L120,200 L120,140 L160,140 L160,220 L200,220 L200,100 L240,100 L240,120 L280,120 L280,160 L320,160 L320,80 L360,80 L360,180 L400,180 L400,200 L440,200 L440,120 L480,120 L480,240 L520,240 L520,60 L560,60 L560,190 L600,190 L600,50 L640,50 L640,170 L680,170 L680,140 L720,140 L720,110 L760,110 L760,250 L800,250 L800,40 L840,40 L840,200 L880,200 L880,160 L920,160 L920,280 L960,280 L960,90 L1000,90 L1000,300 L1200,300 Z' fill='%23d4af37' fill-opacity='0.08'/%3E%3C/svg%3E")
    bottom center/cover no-repeat;
  opacity: 0.3;
  animation: skylineGlow 6s ease-in-out infinite;
}

/* Floating ambient particles */
.splash-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.splash-particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: var(--splash-gold);
  border-radius: 50%;
  opacity: 0.4;
}

.splash-particle:nth-child(1) {
  left: 10%;
  top: 20%;
  animation: particleFloat 8s ease-in-out infinite;
  animation-delay: 0s;
}
.splash-particle:nth-child(2) {
  left: 20%;
  top: 60%;
  animation: particleFloat 6s ease-in-out infinite;
  animation-delay: 1s;
}
.splash-particle:nth-child(3) {
  left: 30%;
  top: 10%;
  animation: particleFloat 10s ease-in-out infinite;
  animation-delay: 2s;
}
.splash-particle:nth-child(4) {
  left: 40%;
  top: 80%;
  animation: particleFloat 7s ease-in-out infinite;
  animation-delay: 0.5s;
}
.splash-particle:nth-child(5) {
  left: 60%;
  top: 30%;
  animation: particleFloat 9s ease-in-out infinite;
  animation-delay: 1.5s;
}
.splash-particle:nth-child(6) {
  left: 70%;
  top: 70%;
  animation: particleFloat 8s ease-in-out infinite;
  animation-delay: 2.5s;
}
.splash-particle:nth-child(7) {
  left: 80%;
  top: 15%;
  animation: particleFloat 11s ease-in-out infinite;
  animation-delay: 3s;
}
.splash-particle:nth-child(8) {
  left: 90%;
  top: 50%;
  animation: particleFloat 6s ease-in-out infinite;
  animation-delay: 0.8s;
}

/* Pulsing ambient lights */
.splash-ambient-light {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    var(--splash-gold-glow) 0%,
    transparent 70%
  );
  animation: ambientPulse 4s ease-in-out infinite;
}

.splash-ambient-light:nth-child(1) {
  width: 200px;
  height: 200px;
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.splash-ambient-light:nth-child(2) {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.splash-ambient-light:nth-child(3) {
  width: 100px;
  height: 100px;
  bottom: 30%;
  left: 70%;
  animation-delay: 4s;
}

/* Main splash content container */
.splash-content {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  text-align: center;
  padding: 2rem;
}

/* Logo container with four-stage animation */
.splash-logo-container {
  position: relative;
  margin-bottom: 3rem;
  opacity: 0;
  animation: logoContainerFadeIn 800ms var(--ease-out-quart) 200ms forwards;
}

.splash-logo {
  width: 280px;
  height: auto;
  max-width: 90vw;
}

@media (max-width: 768px) {
  .splash-logo {
    width: 220px;
  }
}

/* Logo SVG animations */
.splash-logo-svg {
  width: 100%;
  height: auto;
  filter: drop-shadow(0 0 0 transparent);
}

/* Stage 1: Glowing line trace */
.splash-logo-path {
  stroke: var(--splash-gold);
  stroke-width: 2;
  fill: var(--splash-gold);
  fill-opacity: 0;
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: logoTrace var(--splash-logo-trace-duration) var(--ease-out-expo)
      200ms forwards,
    logoFill 600ms var(--ease-out-quart) calc(var(--splash-glow-delay) - 200ms)
      forwards,
    logoGlow 800ms var(--ease-in-out-quart) var(--splash-glow-delay) forwards;
}

/* Brand text with luxury typography */
.splash-brand-text {
  font-family: var(--font-playfair), Playfair Display, serif;
  font-size: 3.5rem;
  font-weight: 700;
  color: var(--splash-ivory);
  letter-spacing: 0.02em;
  margin-bottom: 1rem;
  opacity: 0;
  transform: translateY(30px);
  animation: textFadeUp 800ms var(--ease-out-quart) var(--splash-fade-delay)
    forwards;
}

@media (max-width: 768px) {
  .splash-brand-text {
    font-size: 2.8rem;
  }
}

@media (max-width: 480px) {
  .splash-brand-text {
    font-size: 2.2rem;
  }
}

/* Tagline text */
.splash-tagline {
  font-family: var(--font-inter), Inter, sans-serif;
  font-size: 1.25rem;
  font-weight: 400;
  color: rgba(254, 253, 251, 0.8);
  letter-spacing: 0.05em;
  text-transform: uppercase;
  margin-bottom: 4rem;
  opacity: 0;
  transform: translateY(20px);
  animation: textFadeUp 600ms var(--ease-out-quart)
    calc(var(--splash-fade-delay) + 300ms) forwards;
}

@media (max-width: 768px) {
  .splash-tagline {
    font-size: 1.1rem;
    margin-bottom: 3rem;
  }
}

/* Loading experience section */
.splash-loading-section {
  position: relative;
  width: 100%;
  max-width: 400px;
  opacity: 0;
  animation: fadeInScale 600ms var(--ease-out-quart)
    calc(var(--splash-fade-delay) + 600ms) forwards;
}

/* Loading experience text */
.splash-loading-text {
  font-family: var(--font-inter), Inter, sans-serif;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(254, 253, 251, 0.7);
  letter-spacing: 0.15em;
  text-transform: uppercase;
  margin-bottom: 2rem;
  text-align: center;
}

/* Circular pulse animation */
.splash-pulse-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
}

.splash-pulse-circle {
  width: 60px;
  height: 60px;
  border: 2px solid rgba(212, 175, 55, 0.3);
  border-radius: 50%;
  position: relative;
  animation: circlePulse 2s ease-in-out infinite;
}

.splash-pulse-circle::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  background: var(--splash-gold);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: centerPulse 2s ease-in-out infinite;
}

/* Progress line */
.splash-progress-container {
  position: relative;
  width: 100%;
  height: 2px;
  background: rgba(212, 175, 55, 0.2);
  border-radius: 1px;
  overflow: hidden;
}

.splash-progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--splash-gold),
    rgba(212, 175, 55, 0.8),
    var(--splash-gold)
  );
  border-radius: 1px;
  transform-origin: left;
  transform: scaleX(0);
  animation: progressFill 2500ms var(--ease-out-quart)
    calc(var(--splash-fade-delay) + 800ms) forwards;
}

/* Exit transition animations */
.splash-screen-exit {
  animation: splashExit 1000ms var(--ease-in-out-expo) forwards;
}

.main-content-enter {
  opacity: 0;
  transform: scale(0.98);
  animation: contentEntrance 800ms var(--ease-out-quart) 200ms forwards;
}

/* Enhanced crossfade transition for logo clicks */
.splash-crossfade-enter {
  animation: splashCrossfadeIn 600ms var(--ease-out-quart) forwards;
}

.content-crossfade-enter {
  animation: contentCrossfadeIn 800ms var(--ease-out-quart) 400ms forwards;
}

/* ========================================
   KEYFRAME ANIMATIONS
   ======================================== */

@keyframes splashBackgroundShift {
  0%,
  100% {
    background-position: 0% 50%, 0% 50%, 0% 50%;
  }
  50% {
    background-position: 100% 50%, 100% 50%, 100% 50%;
  }
}

@keyframes textureShift {
  0% {
    opacity: 0.6;
    transform: translateX(0) translateY(0);
  }
  100% {
    opacity: 0.4;
    transform: translateX(20px) translateY(-10px);
  }
}

@keyframes skylineGlow {
  0%,
  100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes particleFloat {
  0%,
  100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.4;
  }
  25% {
    transform: translateY(-30px) translateX(15px);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-15px) translateX(-15px);
    opacity: 0.6;
  }
  75% {
    transform: translateY(-45px) translateX(10px);
    opacity: 1;
  }
}

@keyframes ambientPulse {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
}

@keyframes logoContainerFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes logoTrace {
  0% {
    stroke-dashoffset: 1000;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes logoFill {
  0% {
    fill-opacity: 0;
  }
  100% {
    fill-opacity: 1;
  }
}

@keyframes logoGlow {
  0% {
    filter: drop-shadow(0 0 0 transparent);
  }
  50% {
    filter: drop-shadow(0 0 25px var(--splash-gold-glow))
      drop-shadow(0 0 50px rgba(212, 175, 55, 0.4));
  }
  100% {
    filter: drop-shadow(0 0 15px var(--splash-gold-glow))
      drop-shadow(0 0 30px rgba(212, 175, 55, 0.3));
  }
}

@keyframes textFadeUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes circlePulse {
  0%,
  100% {
    border-color: rgba(212, 175, 55, 0.3);
    transform: scale(1);
  }
  50% {
    border-color: rgba(212, 175, 55, 0.8);
    transform: scale(1.1);
  }
}

@keyframes centerPulse {
  0%,
  100% {
    background: var(--splash-gold);
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    background: rgba(212, 175, 55, 0.8);
    transform: translate(-50%, -50%) scale(1.3);
  }
}

@keyframes progressFill {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleX(1);
  }
}

@keyframes splashExit {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  70% {
    opacity: 0.3;
    transform: scale(1.05);
  }
  100% {
    opacity: 0;
    transform: scale(1.1);
  }
}

@keyframes splashCrossfadeIn {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes contentCrossfadeIn {
  0% {
    opacity: 0;
    transform: scale(0.96) translateY(30px);
    filter: blur(3px);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.98) translateY(15px);
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(0px);
  }
}

@keyframes contentEntrance {
  0% {
    opacity: 0;
    transform: scale(0.98);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes contentSmoothEntrance {
  0% {
    opacity: 0;
    transform: scale(0.97) translateY(20px);
    filter: blur(2px);
  }
  40% {
    opacity: 0.3;
    transform: scale(0.985) translateY(10px);
    filter: blur(1px);
  }
  70% {
    opacity: 0.8;
    transform: scale(1.005) translateY(-2px);
    filter: blur(0px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(0px);
  }
}

/* Mobile menu animations */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slideInFromRight {
  opacity: 0;
  animation: slideInFromRight 0.3s ease-out forwards;
}

/* Map marker pulse animation */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(212, 175, 55, 0.4);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 8px rgba(212, 175, 55, 0.1);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(212, 175, 55, 0);
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Performance optimizations */
.splash-screen,
.splash-screen::before,
.splash-screen::after,
.splash-particles,
.splash-particle,
.splash-ambient-light,
.splash-content,
.splash-logo-container,
.splash-logo-svg,
.splash-logo-path,
.app-container,
.app-content,
.splash-overlay {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Smooth scroll behavior for entire app */
html {
  font-size: 16px;
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  overflow-x: hidden;
  background: var(--ivory);
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .splash-screen,
  .splash-screen::before,
  .splash-screen::after,
  .splash-particle,
  .splash-ambient-light,
  .splash-logo-path,
  .splash-pulse-circle,
  .splash-pulse-circle::before,
  .splash-progress-bar {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
}

/* Tailwind utility classes for shadows instead of custom classes */
/* Use these Tailwind classes instead of the custom ones:
   - For luxury-shadow: shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)]
   - For luxury-shadow-hover: hover:shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] hover:-translate-y-0.5 transition-all duration-300
   - For card-shadow: same as luxury-shadow
*/
.selection\:bg-primary *::-moz-selection {
  background-color: hsl(var(--primary));
}
.selection\:bg-primary *::selection {
  background-color: hsl(var(--primary));
}
.selection\:text-primary-foreground *::-moz-selection {
  color: hsl(var(--primary-foreground));
}
.selection\:text-primary-foreground *::selection {
  color: hsl(var(--primary-foreground));
}
.selection\:bg-primary::-moz-selection {
  background-color: hsl(var(--primary));
}
.selection\:bg-primary::selection {
  background-color: hsl(var(--primary));
}
.selection\:text-primary-foreground::-moz-selection {
  color: hsl(var(--primary-foreground));
}
.selection\:text-primary-foreground::selection {
  color: hsl(var(--primary-foreground));
}
.file\:inline-flex::file-selector-button {
  display: inline-flex;
}
.file\:h-7::file-selector-button {
  height: 1.75rem;
}
.file\:border-0::file-selector-button {
  border-width: 0px;
}
.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}
.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.file\:font-medium::file-selector-button {
  font-weight: 500;
}
.file\:text-foreground::file-selector-button {
  color: hsl(var(--foreground));
}
.placeholder\:text-muted-foreground::-moz-placeholder {
  color: hsl(var(--muted-foreground));
}
.placeholder\:text-muted-foreground::placeholder {
  color: hsl(var(--muted-foreground));
}
.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}
.after\:-inset-2::after {
  content: var(--tw-content);
  inset: -0.5rem;
}
.after\:inset-y-0::after {
  content: var(--tw-content);
  top: 0px;
  bottom: 0px;
}
.after\:left-1\/2::after {
  content: var(--tw-content);
  left: 50%;
}
.after\:w-1::after {
  content: var(--tw-content);
  width: 0.25rem;
}
.after\:w-\[2px\]::after {
  content: var(--tw-content);
  width: 2px;
}
.after\:-translate-x-1\/2::after {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.first\:rounded-l-md:first-child {
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}
.first\:border-l:first-child {
  border-left-width: 1px;
}
.last\:rounded-r-md:last-child {
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.last\:border-b-0:last-child {
  border-bottom-width: 0px;
}
.focus-within\:relative:focus-within {
  position: relative;
}
.focus-within\:z-20:focus-within {
  z-index: 20;
}
.hover\:-translate-y-0\.5:hover {
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:-translate-y-1:hover {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:-translate-y-2:hover {
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:border-gold:hover {
  --tw-border-opacity: 1;
  border-color: rgb(212 175 55 / var(--tw-border-opacity, 1));
}
.hover\:border-gold\/50:hover {
  border-color: rgb(212 175 55 / 0.5);
}
.hover\:border-soft-brown\/50:hover {
  border-color: rgb(139 69 19 / 0.5);
}
.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}
.hover\:bg-black\/70:hover {
  background-color: rgb(0 0 0 / 0.7);
}
.hover\:bg-deep-brown:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(101 67 33 / var(--tw-bg-opacity, 1));
}
.hover\:bg-destructive\/90:hover {
  background-color: hsl(var(--destructive) / 0.9);
}
.hover\:bg-gold:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(212 175 55 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gold\/10:hover {
  background-color: rgb(212 175 55 / 0.1);
}
.hover\:bg-gold\/5:hover {
  background-color: rgb(212 175 55 / 0.05);
}
.hover\:bg-gold\/90:hover {
  background-color: rgb(212 175 55 / 0.9);
}
.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.hover\:bg-muted:hover {
  background-color: hsl(var(--muted));
}
.hover\:bg-muted\/50:hover {
  background-color: hsl(var(--muted) / 0.5);
}
.hover\:bg-primary:hover {
  background-color: hsl(var(--primary));
}
.hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9);
}
.hover\:bg-red-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.hover\:bg-secondary\/80:hover {
  background-color: hsl(var(--secondary) / 0.8);
}
.hover\:bg-soft-brown:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(139 69 19 / var(--tw-bg-opacity, 1));
}
.hover\:bg-soft-brown\/10:hover {
  background-color: rgb(139 69 19 / 0.1);
}
.hover\:bg-soft-brown\/90:hover {
  background-color: rgb(139 69 19 / 0.9);
}
.hover\:bg-warm-gray\/10:hover {
  background-color: rgb(139 134 128 / 0.1);
}
.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-white\/10:hover {
  background-color: rgb(255 255 255 / 0.1);
}
.hover\:bg-white\/20:hover {
  background-color: rgb(255 255 255 / 0.2);
}
.hover\:bg-white\/90:hover {
  background-color: rgb(255 255 255 / 0.9);
}
.hover\:from-gold\/90:hover {
  --tw-gradient-from: rgb(212 175 55 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(212 175 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-soft-brown\/90:hover {
  --tw-gradient-from: rgb(139 69 19 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 69 19 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:to-gold\/70:hover {
  --tw-gradient-to: rgb(212 175 55 / 0.7) var(--tw-gradient-to-position);
}
.hover\:to-ivory\/70:hover {
  --tw-gradient-to: rgb(248 246 240 / 0.7) var(--tw-gradient-to-position);
}
.hover\:to-soft-brown\/80:hover {
  --tw-gradient-to: rgb(139 69 19 / 0.8) var(--tw-gradient-to-position);
}
.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}
.hover\:text-foreground:hover {
  color: hsl(var(--foreground));
}
.hover\:text-gold:hover {
  --tw-text-opacity: 1;
  color: rgb(212 175 55 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.hover\:text-muted-foreground:hover {
  color: hsl(var(--muted-foreground));
}
.hover\:text-primary-foreground:hover {
  color: hsl(var(--primary-foreground));
}
.hover\:text-soft-brown:hover {
  --tw-text-opacity: 1;
  color: rgb(139 69 19 / var(--tw-text-opacity, 1));
}
.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.hover\:underline:hover {
  text-decoration-line: underline;
}
.hover\:no-underline:hover {
  text-decoration-line: none;
}
.hover\:opacity-100:hover {
  opacity: 1;
}
.hover\:opacity-80:hover {
  opacity: 0.8;
}
.hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover {
  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-accent));
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-\[0_12px_40px_-4px_rgba\(139\2c 115\2c 85\2c 0\.15\)\2c 0_6px_20px_-4px_rgba\(139\2c 115\2c 85\2c 0\.1\)\]:hover {
  --tw-shadow: 0 12px 40px -4px rgba(139,115,85,0.15),0 6px 20px -4px rgba(139,115,85,0.1);
  --tw-shadow-colored: 0 12px 40px -4px var(--tw-shadow-color), 0 6px 20px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-\[0_12px_48px_-4px_rgba\(139\2c 115\2c 85\2c 0\.15\)\]:hover {
  --tw-shadow: 0 12px 48px -4px rgba(139,115,85,0.15);
  --tw-shadow-colored: 0 12px 48px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-\[0_4px_16px_-2px_rgba\(212\2c 175\2c 55\2c 0\.3\)\]:hover {
  --tw-shadow: 0 4px 16px -2px rgba(212,175,55,0.3);
  --tw-shadow-colored: 0 4px 16px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-\[0_8px_32px_-4px_rgba\(139\2c 115\2c 85\2c 0\.12\)\2c 0_4px_16px_-4px_rgba\(139\2c 115\2c 85\2c 0\.08\)\]:hover {
  --tw-shadow: 0 8px 32px -4px rgba(139,115,85,0.12),0 4px 16px -4px rgba(139,115,85,0.08);
  --tw-shadow-colored: 0 8px 32px -4px var(--tw-shadow-color), 0 4px 16px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-\[0_8px_32px_-4px_rgba\(139\2c 115\2c 85\2c 0\.15\)\]:hover {
  --tw-shadow: 0 8px 32px -4px rgba(139,115,85,0.15);
  --tw-shadow-colored: 0 8px 32px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-\[0_8px_32px_-4px_rgba\(212\2c 175\2c 55\2c 0\.4\)\]:hover {
  --tw-shadow: 0 8px 32px -4px rgba(212,175,55,0.4);
  --tw-shadow-colored: 0 8px 32px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-sm:hover {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:ring-4:hover {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:z-10:focus {
  z-index: 10;
}
.focus\:border-gold:focus {
  --tw-border-opacity: 1;
  border-color: rgb(212 175 55 / var(--tw-border-opacity, 1));
}
.focus\:border-red-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.focus\:bg-accent:focus {
  background-color: hsl(var(--accent));
}
.focus\:bg-primary:focus {
  background-color: hsl(var(--primary));
}
.focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground));
}
.focus\:text-primary-foreground:focus {
  color: hsl(var(--primary-foreground));
}
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-gold:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(212 175 55 / var(--tw-ring-opacity, 1));
}
.focus\:ring-gold\/20:focus {
  --tw-ring-color: rgb(212 175 55 / 0.2);
}
.focus\:ring-ring:focus {
  --tw-ring-color: hsl(var(--ring));
}
.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}
.focus-visible\:z-10:focus-visible {
  z-index: 10;
}
.focus-visible\:border-ring:focus-visible {
  border-color: hsl(var(--ring));
}
.focus-visible\:outline-1:focus-visible {
  outline-width: 1px;
}
.focus-visible\:outline-ring:focus-visible {
  outline-color: hsl(var(--ring));
}
.focus-visible\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-4:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-\[3px\]:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-destructive\/20:focus-visible {
  --tw-ring-color: hsl(var(--destructive) / 0.2);
}
.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}
.focus-visible\:ring-ring\/50:focus-visible {
  --tw-ring-color: hsl(var(--ring) / 0.5);
}
.focus-visible\:ring-offset-1:focus-visible {
  --tw-ring-offset-width: 1px;
}
.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}
.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}
.disabled\:opacity-50:disabled {
  opacity: 0.5;
}
.group\/menu-item:focus-within .group-focus-within\/menu-item\:opacity-100 {
  opacity: 1;
}
.group:hover .group-hover\:left-1\/2 {
  left: 50%;
}
.group:hover .group-hover\:w-6 {
  width: 1.5rem;
}
.group\/item:hover .group-hover\/item\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group\/mobile:hover .group-hover\/mobile\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:-translate-y-1 {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group\/item:hover .group-hover\/item\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group\/mobile:hover .group-hover\/mobile\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:bg-soft-brown {
  --tw-bg-opacity: 1;
  background-color: rgb(139 69 19 / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}
.group:hover .group-hover\:bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.group:hover .group-hover\:from-gold {
  --tw-gradient-from: #D4AF37 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(212 175 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.group:hover .group-hover\:to-gold\/80 {
  --tw-gradient-to: rgb(212 175 55 / 0.8) var(--tw-gradient-to-position);
}
.group:hover .group-hover\:text-gold {
  --tw-text-opacity: 1;
  color: rgb(212 175 55 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-soft-brown {
  --tw-text-opacity: 1;
  color: rgb(139 69 19 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.group\/menu-item:hover .group-hover\/menu-item\:opacity-100 {
  opacity: 1;
}
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}
.group:hover .group-hover\:shadow-\[0_12px_48px_-4px_rgba\(212\2c 175\2c 55\2c 0\.35\)\2c 0_8px_24px_-4px_rgba\(212\2c 175\2c 55\2c 0\.25\)\] {
  --tw-shadow: 0 12px 48px -4px rgba(212,175,55,0.35),0 8px 24px -4px rgba(212,175,55,0.25);
  --tw-shadow-colored: 0 12px 48px -4px var(--tw-shadow-color), 0 8px 24px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:hover .group-hover\:shadow-\[0_8px_32px_-4px_rgba\(139\2c 115\2c 85\2c 0\.12\)\2c 0_4px_16px_-4px_rgba\(139\2c 115\2c 85\2c 0\.08\)\] {
  --tw-shadow: 0 8px 32px -4px rgba(139,115,85,0.12),0 4px 16px -4px rgba(139,115,85,0.08);
  --tw-shadow-colored: 0 8px 32px -4px var(--tw-shadow-color), 0 4px 16px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:hover .group-hover\:shadow-\[0_8px_32px_-4px_rgba\(139\2c 115\2c 85\2c 0\.15\)\] {
  --tw-shadow: 0 8px 32px -4px rgba(139,115,85,0.15);
  --tw-shadow-colored: 0 8px 32px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}
.peer:disabled ~ .peer-disabled\:opacity-50 {
  opacity: 0.5;
}
.has-\[\>svg\]\:grid-cols-\[calc\(var\(--spacing\)\*4\)_1fr\]:has(>svg) {
  grid-template-columns: calc(var(--spacing) * 4) 1fr;
}
.has-\[\>svg\]\:gap-x-3:has(>svg) {
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}
.has-\[\>svg\]\:px-2\.5:has(>svg) {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.has-\[\>svg\]\:px-3:has(>svg) {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.has-\[\>svg\]\:px-4:has(>svg) {
  padding-left: 1rem;
  padding-right: 1rem;
}
.aria-disabled\:pointer-events-none[aria-disabled="true"] {
  pointer-events: none;
}
.aria-disabled\:opacity-50[aria-disabled="true"] {
  opacity: 0.5;
}
.aria-selected\:bg-accent[aria-selected="true"] {
  background-color: hsl(var(--accent));
}
.aria-selected\:bg-primary[aria-selected="true"] {
  background-color: hsl(var(--primary));
}
.aria-selected\:text-accent-foreground[aria-selected="true"] {
  color: hsl(var(--accent-foreground));
}
.aria-selected\:text-muted-foreground[aria-selected="true"] {
  color: hsl(var(--muted-foreground));
}
.aria-selected\:text-primary-foreground[aria-selected="true"] {
  color: hsl(var(--primary-foreground));
}
.aria-selected\:opacity-100[aria-selected="true"] {
  opacity: 1;
}
.data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"] {
  pointer-events: none;
}
.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}
.data-\[vaul-drawer-direction\=bottom\]\:inset-x-0[data-vaul-drawer-direction="bottom"] {
  left: 0px;
  right: 0px;
}
.data-\[vaul-drawer-direction\=left\]\:inset-y-0[data-vaul-drawer-direction="left"] {
  top: 0px;
  bottom: 0px;
}
.data-\[vaul-drawer-direction\=right\]\:inset-y-0[data-vaul-drawer-direction="right"] {
  top: 0px;
  bottom: 0px;
}
.data-\[vaul-drawer-direction\=top\]\:inset-x-0[data-vaul-drawer-direction="top"] {
  left: 0px;
  right: 0px;
}
.data-\[vaul-drawer-direction\=bottom\]\:bottom-0[data-vaul-drawer-direction="bottom"] {
  bottom: 0px;
}
.data-\[vaul-drawer-direction\=left\]\:left-0[data-vaul-drawer-direction="left"] {
  left: 0px;
}
.data-\[vaul-drawer-direction\=right\]\:right-0[data-vaul-drawer-direction="right"] {
  right: 0px;
}
.data-\[vaul-drawer-direction\=top\]\:top-0[data-vaul-drawer-direction="top"] {
  top: 0px;
}
.data-\[active\=true\]\:z-10[data-active="true"] {
  z-index: 10;
}
.data-\[vaul-drawer-direction\=bottom\]\:mt-24[data-vaul-drawer-direction="bottom"] {
  margin-top: 6rem;
}
.data-\[vaul-drawer-direction\=top\]\:mb-24[data-vaul-drawer-direction="top"] {
  margin-bottom: 6rem;
}
.data-\[orientation\=horizontal\]\:h-4[data-orientation="horizontal"] {
  height: 1rem;
}
.data-\[orientation\=horizontal\]\:h-full[data-orientation="horizontal"] {
  height: 100%;
}
.data-\[orientation\=horizontal\]\:h-px[data-orientation="horizontal"] {
  height: 1px;
}
.data-\[orientation\=vertical\]\:h-full[data-orientation="vertical"] {
  height: 100%;
}
.data-\[panel-group-direction\=vertical\]\:h-px[data-panel-group-direction="vertical"] {
  height: 1px;
}
.data-\[size\=default\]\:h-9[data-size="default"] {
  height: 2.25rem;
}
.data-\[size\=sm\]\:h-8[data-size="sm"] {
  height: 2rem;
}
.data-\[vaul-drawer-direction\=bottom\]\:max-h-\[80vh\][data-vaul-drawer-direction="bottom"] {
  max-height: 80vh;
}
.data-\[vaul-drawer-direction\=top\]\:max-h-\[80vh\][data-vaul-drawer-direction="top"] {
  max-height: 80vh;
}
.data-\[orientation\=vertical\]\:min-h-44[data-orientation="vertical"] {
  min-height: 11rem;
}
.data-\[orientation\=horizontal\]\:w-full[data-orientation="horizontal"] {
  width: 100%;
}
.data-\[orientation\=vertical\]\:w-1\.5[data-orientation="vertical"] {
  width: 0.375rem;
}
.data-\[orientation\=vertical\]\:w-auto[data-orientation="vertical"] {
  width: auto;
}
.data-\[orientation\=vertical\]\:w-full[data-orientation="vertical"] {
  width: 100%;
}
.data-\[orientation\=vertical\]\:w-px[data-orientation="vertical"] {
  width: 1px;
}
.data-\[panel-group-direction\=vertical\]\:w-full[data-panel-group-direction="vertical"] {
  width: 100%;
}
.data-\[vaul-drawer-direction\=left\]\:w-3\/4[data-vaul-drawer-direction="left"] {
  width: 75%;
}
.data-\[vaul-drawer-direction\=right\]\:w-3\/4[data-vaul-drawer-direction="right"] {
  width: 75%;
}
.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[state\=checked\]\:translate-x-\[calc\(100\%-2px\)\][data-state="checked"] {
  --tw-translate-x: calc(100% - 2px);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"] {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes accordion-up {

  from {
    height: var(--radix-accordion-content-height);
  }

  to {
    height: 0;
  }
}
.data-\[state\=closed\]\:animate-accordion-up[data-state="closed"] {
  animation: accordion-up 0.2s ease-out;
}
@keyframes accordion-down {

  from {
    height: 0;
  }

  to {
    height: var(--radix-accordion-content-height);
  }
}
.data-\[state\=open\]\:animate-accordion-down[data-state="open"] {
  animation: accordion-down 0.2s ease-out;
}
.data-\[orientation\=vertical\]\:flex-col[data-orientation="vertical"] {
  flex-direction: column;
}
.data-\[panel-group-direction\=vertical\]\:flex-col[data-panel-group-direction="vertical"] {
  flex-direction: column;
}
.data-\[vaul-drawer-direction\=bottom\]\:rounded-t-lg[data-vaul-drawer-direction="bottom"] {
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}
.data-\[vaul-drawer-direction\=top\]\:rounded-b-lg[data-vaul-drawer-direction="top"] {
  border-bottom-right-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
}
.data-\[variant\=outline\]\:border-l-0[data-variant="outline"] {
  border-left-width: 0px;
}
.data-\[vaul-drawer-direction\=bottom\]\:border-t[data-vaul-drawer-direction="bottom"] {
  border-top-width: 1px;
}
.data-\[vaul-drawer-direction\=left\]\:border-r[data-vaul-drawer-direction="left"] {
  border-right-width: 1px;
}
.data-\[vaul-drawer-direction\=right\]\:border-l[data-vaul-drawer-direction="right"] {
  border-left-width: 1px;
}
.data-\[vaul-drawer-direction\=top\]\:border-b[data-vaul-drawer-direction="top"] {
  border-bottom-width: 1px;
}
.data-\[active\=true\]\:border-ring[data-active="true"] {
  border-color: hsl(var(--ring));
}
.data-\[state\=checked\]\:border-primary[data-state="checked"] {
  border-color: hsl(var(--primary));
}
.data-\[active\=true\]\:bg-accent\/50[data-active="true"] {
  background-color: hsl(var(--accent) / 0.5);
}
.data-\[selected\=true\]\:bg-accent[data-selected="true"] {
  background-color: hsl(var(--accent));
}
.data-\[state\=active\]\:bg-card[data-state="active"] {
  background-color: hsl(var(--card));
}
.data-\[state\=active\]\:bg-white[data-state="active"] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.data-\[state\=checked\]\:bg-gold[data-state="checked"] {
  --tw-bg-opacity: 1;
  background-color: rgb(212 175 55 / var(--tw-bg-opacity, 1));
}
.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
  background-color: hsl(var(--primary));
}
.data-\[state\=on\]\:bg-accent[data-state="on"] {
  background-color: hsl(var(--accent));
}
.data-\[state\=open\]\:bg-accent[data-state="open"] {
  background-color: hsl(var(--accent));
}
.data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
  background-color: hsl(var(--accent) / 0.5);
}
.data-\[state\=open\]\:bg-secondary[data-state="open"] {
  background-color: hsl(var(--secondary));
}
.data-\[state\=selected\]\:bg-muted[data-state="selected"] {
  background-color: hsl(var(--muted));
}
.data-\[inset\]\:pl-8[data-inset] {
  padding-left: 2rem;
}
.data-\[active\=true\]\:font-medium[data-active="true"] {
  font-weight: 500;
}
.data-\[active\=true\]\:text-accent-foreground[data-active="true"] {
  color: hsl(var(--accent-foreground));
}
.data-\[error\=true\]\:text-destructive[data-error="true"] {
  color: hsl(var(--destructive));
}
.data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
  color: hsl(var(--muted-foreground));
}
.data-\[selected\=true\]\:text-accent-foreground[data-selected="true"] {
  color: hsl(var(--accent-foreground));
}
.data-\[state\=active\]\:text-soft-brown[data-state="active"] {
  --tw-text-opacity: 1;
  color: rgb(139 69 19 / var(--tw-text-opacity, 1));
}
.data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
  color: hsl(var(--primary-foreground));
}
.data-\[state\=on\]\:text-accent-foreground[data-state="on"] {
  color: hsl(var(--accent-foreground));
}
.data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
  color: hsl(var(--accent-foreground));
}
.data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
  color: hsl(var(--muted-foreground));
}
.data-\[variant\=destructive\]\:text-destructive[data-variant="destructive"] {
  color: hsl(var(--destructive));
}
.data-\[disabled\=true\]\:opacity-50[data-disabled="true"] {
  opacity: 0.5;
}
.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: 0.5;
}
.data-\[state\=open\]\:opacity-100[data-state="open"] {
  opacity: 1;
}
.data-\[active\=true\]\:ring-\[3px\][data-active="true"] {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.data-\[active\=true\]\:ring-ring\/50[data-active="true"] {
  --tw-ring-color: hsl(var(--ring) / 0.5);
}
.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  transition-duration: 300ms;
}
.data-\[state\=open\]\:duration-500[data-state="open"] {
  transition-duration: 500ms;
}
.data-\[motion\^\=from-\]\:animate-in[data-motion^="from-"] {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.data-\[state\=open\]\:animate-in[data-state="open"] {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.data-\[state\=visible\]\:animate-in[data-state="visible"] {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.data-\[motion\^\=to-\]\:animate-out[data-motion^="to-"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.data-\[state\=hidden\]\:animate-out[data-state="hidden"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.data-\[motion\^\=from-\]\:fade-in[data-motion^="from-"] {
  --tw-enter-opacity: 0;
}
.data-\[motion\^\=to-\]\:fade-out[data-motion^="to-"] {
  --tw-exit-opacity: 0;
}
.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}
.data-\[state\=hidden\]\:fade-out[data-state="hidden"] {
  --tw-exit-opacity: 0;
}
.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}
.data-\[state\=visible\]\:fade-in[data-state="visible"] {
  --tw-enter-opacity: 0;
}
.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}
.data-\[state\=open\]\:zoom-in-90[data-state="open"] {
  --tw-enter-scale: .9;
}
.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}
.data-\[motion\=from-end\]\:slide-in-from-right-52[data-motion="from-end"] {
  --tw-enter-translate-x: 13rem;
}
.data-\[motion\=from-start\]\:slide-in-from-left-52[data-motion="from-start"] {
  --tw-enter-translate-x: -13rem;
}
.data-\[motion\=to-end\]\:slide-out-to-right-52[data-motion="to-end"] {
  --tw-exit-translate-x: 13rem;
}
.data-\[motion\=to-start\]\:slide-out-to-left-52[data-motion="to-start"] {
  --tw-exit-translate-x: -13rem;
}
.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: -0.5rem;
}
.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: 0.5rem;
}
.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: -0.5rem;
}
.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: 0.5rem;
}
.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
  --tw-exit-translate-y: 100%;
}
.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
  --tw-exit-translate-x: -100%;
}
.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
  --tw-exit-translate-x: 100%;
}
.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
  --tw-exit-translate-y: -100%;
}
.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
  --tw-enter-translate-y: 100%;
}
.data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
  --tw-enter-translate-x: -100%;
}
.data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
  --tw-enter-translate-x: 100%;
}
.data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
  --tw-enter-translate-y: -100%;
}
.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  animation-duration: 300ms;
}
.data-\[state\=open\]\:duration-500[data-state="open"] {
  animation-duration: 500ms;
}
.\*\:data-\[slot\=select-value\]\:line-clamp-1[data-slot="select-value"] > * {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.\*\:data-\[slot\=select-value\]\:flex[data-slot="select-value"] > * {
  display: flex;
}
.\*\:data-\[slot\=select-value\]\:items-center[data-slot="select-value"] > * {
  align-items: center;
}
.\*\:data-\[slot\=select-value\]\:gap-2[data-slot="select-value"] > * {
  gap: 0.5rem;
}
.\*\:data-\[slot\=alert-description\]\:text-destructive\/90[data-slot="alert-description"] > * {
  color: hsl(var(--destructive) / 0.9);
}
.data-\[panel-group-direction\=vertical\]\:after\:left-0[data-panel-group-direction="vertical"]::after {
  content: var(--tw-content);
  left: 0px;
}
.data-\[panel-group-direction\=vertical\]\:after\:h-1[data-panel-group-direction="vertical"]::after {
  content: var(--tw-content);
  height: 0.25rem;
}
.data-\[panel-group-direction\=vertical\]\:after\:w-full[data-panel-group-direction="vertical"]::after {
  content: var(--tw-content);
  width: 100%;
}
.data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2[data-panel-group-direction="vertical"]::after {
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[panel-group-direction\=vertical\]\:after\:translate-x-0[data-panel-group-direction="vertical"]::after {
  content: var(--tw-content);
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[variant\=outline\]\:first\:border-l:first-child[data-variant="outline"] {
  border-left-width: 1px;
}
.data-\[active\=true\]\:hover\:bg-accent:hover[data-active="true"] {
  background-color: hsl(var(--accent));
}
.data-\[state\=open\]\:hover\:bg-accent:hover[data-state="open"] {
  background-color: hsl(var(--accent));
}
.data-\[active\=true\]\:focus\:bg-accent:focus[data-active="true"] {
  background-color: hsl(var(--accent));
}
.data-\[state\=open\]\:focus\:bg-accent:focus[data-state="open"] {
  background-color: hsl(var(--accent));
}
.data-\[variant\=destructive\]\:focus\:bg-destructive\/10:focus[data-variant="destructive"] {
  background-color: hsl(var(--destructive) / 0.1);
}
.data-\[variant\=destructive\]\:focus\:text-destructive:focus[data-variant="destructive"] {
  color: hsl(var(--destructive));
}
.group[data-disabled="true"] .group-data-\[disabled\=true\]\:pointer-events-none {
  pointer-events: none;
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\] {
  left: calc(var(--sidebar-width) * -1);
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\] {
  right: calc(var(--sidebar-width) * -1);
}
.group[data-side="left"] .group-data-\[side\=left\]\:-right-4 {
  right: -1rem;
}
.group[data-side="right"] .group-data-\[side\=right\]\:left-0 {
  left: 0px;
}
.group\/navigation-menu[data-viewport="false"] .group-data-\[viewport\=false\]\/navigation-menu\:top-full {
  top: 100%;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:-mt-8 {
  margin-top: -2rem;
}
.group\/navigation-menu[data-viewport="false"] .group-data-\[viewport\=false\]\/navigation-menu\:mt-1\.5 {
  margin-top: 0.375rem;
}
.group\/drawer-content[data-vaul-drawer-direction="bottom"] .group-data-\[vaul-drawer-direction\=bottom\]\/drawer-content\:block {
  display: block;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:hidden {
  display: none;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\)\] {
  width: calc(var(--sidebar-width-icon) + (--spacing(4)));
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\+2px\)\] {
  width: calc(var(--sidebar-width-icon) + (--spacing(4)) + 2px);
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:w-0 {
  width: 0px;
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-side="right"] .group-data-\[side\=right\]\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-state="open"] .group-data-\[state\=open\]\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:overflow-hidden {
  overflow: hidden;
}
.group\/navigation-menu[data-viewport="false"] .group-data-\[viewport\=false\]\/navigation-menu\:overflow-hidden {
  overflow: hidden;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:rounded-lg {
  border-radius: var(--radius);
}
.group\/navigation-menu[data-viewport="false"] .group-data-\[viewport\=false\]\/navigation-menu\:rounded-md {
  border-radius: calc(var(--radius) - 2px);
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:border {
  border-width: 1px;
}
.group\/navigation-menu[data-viewport="false"] .group-data-\[viewport\=false\]\/navigation-menu\:border {
  border-width: 1px;
}
.group[data-side="left"] .group-data-\[side\=left\]\:border-r {
  border-right-width: 1px;
}
.group[data-side="right"] .group-data-\[side\=right\]\:border-l {
  border-left-width: 1px;
}
.group\/navigation-menu[data-viewport="false"] .group-data-\[viewport\=false\]\/navigation-menu\:bg-popover {
  background-color: hsl(var(--popover));
}
.group\/navigation-menu[data-viewport="false"] .group-data-\[viewport\=false\]\/navigation-menu\:text-popover-foreground {
  color: hsl(var(--popover-foreground));
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:opacity-0 {
  opacity: 0;
}
.group[data-disabled="true"] .group-data-\[disabled\=true\]\:opacity-50 {
  opacity: 0.5;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group\/navigation-menu[data-viewport="false"] .group-data-\[viewport\=false\]\/navigation-menu\:shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group\/navigation-menu[data-viewport="false"] .group-data-\[viewport\=false\]\/navigation-menu\:duration-200 {
  transition-duration: 200ms;
  animation-duration: 200ms;
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:after\:left-full::after {
  content: var(--tw-content);
  left: 100%;
}
.group\/navigation-menu[data-viewport="false"] .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:animate-in[data-state="open"] {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.group\/navigation-menu[data-viewport="false"] .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:animate-out[data-state="closed"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.group\/navigation-menu[data-viewport="false"] .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}
.group\/navigation-menu[data-viewport="false"] .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}
.group\/navigation-menu[data-viewport="false"] .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}
.group\/navigation-menu[data-viewport="false"] .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}
.peer\/menu-button[data-size="default"] ~ .peer-data-\[size\=default\]\/menu-button\:top-1\.5 {
  top: 0.375rem;
}
.peer\/menu-button[data-size="lg"] ~ .peer-data-\[size\=lg\]\/menu-button\:top-2\.5 {
  top: 0.625rem;
}
.peer\/menu-button[data-size="sm"] ~ .peer-data-\[size\=sm\]\/menu-button\:top-1 {
  top: 0.25rem;
}
.dark\:border-input:is(.dark *) {
  border-color: hsl(var(--input));
}
.dark\:bg-destructive\/60:is(.dark *) {
  background-color: hsl(var(--destructive) / 0.6);
}
.dark\:bg-input\/30:is(.dark *) {
  background-color: hsl(var(--input) / 0.3);
}
.dark\:text-muted-foreground:is(.dark *) {
  color: hsl(var(--muted-foreground));
}
.dark\:hover\:bg-accent\/50:hover:is(.dark *) {
  background-color: hsl(var(--accent) / 0.5);
}
.dark\:hover\:bg-input\/50:hover:is(.dark *) {
  background-color: hsl(var(--input) / 0.5);
}
.dark\:focus-visible\:ring-destructive\/40:focus-visible:is(.dark *) {
  --tw-ring-color: hsl(var(--destructive) / 0.4);
}
.dark\:data-\[state\=active\]\:border-input[data-state="active"]:is(.dark *) {
  border-color: hsl(var(--input));
}
.dark\:data-\[state\=active\]\:bg-input\/30[data-state="active"]:is(.dark *) {
  background-color: hsl(var(--input) / 0.3);
}
.dark\:data-\[state\=checked\]\:bg-primary[data-state="checked"]:is(.dark *) {
  background-color: hsl(var(--primary));
}
.dark\:data-\[state\=checked\]\:bg-primary-foreground[data-state="checked"]:is(.dark *) {
  background-color: hsl(var(--primary-foreground));
}
.dark\:data-\[state\=unchecked\]\:bg-card-foreground[data-state="unchecked"]:is(.dark *) {
  background-color: hsl(var(--card-foreground));
}
.dark\:data-\[state\=unchecked\]\:bg-input\/80[data-state="unchecked"]:is(.dark *) {
  background-color: hsl(var(--input) / 0.8);
}
.dark\:data-\[state\=active\]\:text-foreground[data-state="active"]:is(.dark *) {
  color: hsl(var(--foreground));
}
.dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:focus[data-variant="destructive"]:is(.dark *) {
  background-color: hsl(var(--destructive) / 0.2);
}
@media (min-width: 640px) {

  .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .sm\:block {
    display: block;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:max-w-6xl {
    max-width: 72rem;
  }

  .sm\:max-w-lg {
    max-width: 32rem;
  }

  .sm\:max-w-md {
    max-width: 28rem;
  }

  .sm\:max-w-sm {
    max-width: 24rem;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:gap-2\.5 {
    gap: 0.625rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:pl-2\.5 {
    padding-left: 0.625rem;
  }

  .sm\:pr-2\.5 {
    padding-right: 0.625rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .data-\[vaul-drawer-direction\=left\]\:sm\:max-w-sm[data-vaul-drawer-direction="left"] {
    max-width: 24rem;
  }

  .data-\[vaul-drawer-direction\=right\]\:sm\:max-w-sm[data-vaul-drawer-direction="right"] {
    max-width: 24rem;
  }
}
@media (min-width: 768px) {

  .md\:absolute {
    position: absolute;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:grid {
    display: grid;
  }

  .md\:aspect-auto {
    aspect-ratio: auto;
  }

  .md\:h-48 {
    height: 12rem;
  }

  .md\:w-80 {
    width: 20rem;
  }

  .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\] {
    width: var(--radix-navigation-menu-viewport-width);
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .md\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:gap-0 {
    gap: 0px;
  }

  .md\:p-12 {
    padding: 3rem;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:opacity-0 {
    opacity: 0;
  }

  .md\:after\:hidden::after {
    content: var(--tw-content);
    display: none;
  }

  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:m-2 {
    margin: 0.5rem;
  }

  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:ml-0 {
    margin-left: 0px;
  }

  .peer[data-variant="inset"][data-state="collapsed"] ~ .md\:peer-data-\[variant\=inset\]\:peer-data-\[state\=collapsed\]\:ml-2 {
    margin-left: 0.5rem;
  }

  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:rounded-xl {
    border-radius: 0.75rem;
  }

  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:shadow-sm {
    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}
@media (min-width: 1024px) {

  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:mb-0 {
    margin-bottom: 0px;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-\[500px\] {
    height: 500px;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .lg\:grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:gap-6 {
    gap: 1.5rem;
  }

  .lg\:p-12 {
    padding: 3rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .lg\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .lg\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .lg\:text-7xl {
    font-size: 4.5rem;
    line-height: 1;
  }
}
@media (min-width: 1280px) {

  .xl\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}
.\[\&\:has\(\>\.day-range-end\)\]\:rounded-r-md:has(>.day-range-end) {
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\>\.day-range-start\)\]\:rounded-l-md:has(>.day-range-start) {
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[aria-selected\]\)\]\:rounded-md:has([aria-selected]) {
  border-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]) {
  background-color: hsl(var(--accent));
}
.first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:has([aria-selected]):first-child {
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}
.last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:has([aria-selected]):last-child {
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has([aria-selected].day-range-end) {
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role=checkbox]) {
  padding-right: 0px;
}
.\[\&\:last-child\]\:pb-6:last-child {
  padding-bottom: 1.5rem;
}
.\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\]>[role=checkbox] {
  --tw-translate-y: 2px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\>button\]\:hidden>button {
  display: none;
}
.\[\&\>span\:last-child\]\:truncate>span:last-child {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.\[\&\>svg\]\:pointer-events-none>svg {
  pointer-events: none;
}
.\[\&\>svg\]\:size-3>svg {
  width: 0.75rem;
  height: 0.75rem;
}
.\[\&\>svg\]\:size-3\.5>svg {
  width: 0.875rem;
  height: 0.875rem;
}
.\[\&\>svg\]\:size-4>svg {
  width: 1rem;
  height: 1rem;
}
.\[\&\>svg\]\:h-2\.5>svg {
  height: 0.625rem;
}
.\[\&\>svg\]\:h-3>svg {
  height: 0.75rem;
}
.\[\&\>svg\]\:w-2\.5>svg {
  width: 0.625rem;
}
.\[\&\>svg\]\:w-3>svg {
  width: 0.75rem;
}
.\[\&\>svg\]\:shrink-0>svg {
  flex-shrink: 0;
}
.\[\&\>svg\]\:translate-y-0\.5>svg {
  --tw-translate-y: 0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\>svg\]\:text-current>svg {
  color: currentColor;
}
.\[\&\>svg\]\:text-muted-foreground>svg {
  color: hsl(var(--muted-foreground));
}
.\[\&\>tr\]\:last\:border-b-0:last-child>tr {
  border-bottom-width: 0px;
}
.\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction=vertical]>div {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state=open]>svg {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\[data-state\=open\]\]\:text-gold[data-state=open] {
  --tw-text-opacity: 1;
  color: rgb(212 175 55 / var(--tw-text-opacity, 1));
}
.\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .recharts-cartesian-axis-tick text {
  fill: hsl(var(--muted-foreground));
}
.\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke='#ccc'] {
  stroke: hsl(var(--border) / 0.5);
}
.\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-curve.recharts-tooltip-cursor {
  stroke: hsl(var(--border));
}
.\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-dot[stroke='#fff'] {
  stroke: transparent;
}
.\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-polar-grid [stroke='#ccc'] {
  stroke: hsl(var(--border));
}
.\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radial-bar-background-sector {
  fill: hsl(var(--muted));
}
.\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor {
  fill: hsl(var(--muted));
}
.\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-reference-line [stroke='#ccc'] {
  stroke: hsl(var(--border));
}
.\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-sector[stroke='#fff'] {
  stroke: transparent;
}
.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] {
  font-size: 0.75rem;
  line-height: 1rem;
}
.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] {
  font-weight: 500;
}
.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
  color: hsl(var(--muted-foreground));
}
.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group] {
  padding-top: 0px;
}
.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg {
  height: 1.25rem;
}
.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg {
  width: 1.25rem;
}
.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] {
  height: 3rem;
}
.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg {
  height: 1.25rem;
}
.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg {
  width: 1.25rem;
}
.\[\&_p\]\:leading-relaxed p {
  line-height: 1.625;
}
.\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*='size-']) {
  width: 1rem;
  height: 1rem;
}
.\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*='text-']) {
  color: hsl(var(--muted-foreground));
}
.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}
.\[\&_svg\]\:shrink-0 svg {
  flex-shrink: 0;
}
.\[\&_tr\:last-child\]\:border-0 tr:last-child {
  border-width: 0px;
}
.\[\&_tr\]\:border-b tr {
  border-bottom-width: 1px;
}
[data-side=left][data-collapsible=offcanvas] .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2 {
  right: -0.5rem;
}
[data-side=left][data-state=collapsed] .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize {
  cursor: e-resize;
}
[data-side=right][data-collapsible=offcanvas] .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2 {
  left: -0.5rem;
}
[data-side=right][data-state=collapsed] .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize {
  cursor: w-resize;
}
a.\[a\&\]\:hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}
a.\[a\&\]\:hover\:bg-destructive\/90:hover {
  background-color: hsl(var(--destructive) / 0.9);
}
a.\[a\&\]\:hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9);
}
a.\[a\&\]\:hover\:bg-secondary\/90:hover {
  background-color: hsl(var(--secondary) / 0.9);
}
a.\[a\&\]\:hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}

