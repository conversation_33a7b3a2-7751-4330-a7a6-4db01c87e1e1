"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"a3c945a526bb\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/NDE5OSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImEzYzk0NWE1MjZiYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/error-handler.ts":
/*!**********************************!*\
  !*** ./src/lib/error-handler.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorType: function() { return /* binding */ ErrorType; },\n/* harmony export */   classifyError: function() { return /* binding */ classifyError; },\n/* harmony export */   dismissAllToasts: function() { return /* binding */ dismissAllToasts; },\n/* harmony export */   dismissToast: function() { return /* binding */ dismissToast; },\n/* harmony export */   formatValidationErrors: function() { return /* binding */ formatValidationErrors; },\n/* harmony export */   logError: function() { return /* binding */ logError; },\n/* harmony export */   showError: function() { return /* binding */ showError; },\n/* harmony export */   showInfo: function() { return /* binding */ showInfo; },\n/* harmony export */   showLoading: function() { return /* binding */ showLoading; },\n/* harmony export */   showSuccess: function() { return /* binding */ showSuccess; },\n/* harmony export */   withRetry: function() { return /* binding */ withRetry; }\n/* harmony export */ });\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _error_monitoring__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error-monitoring */ \"(app-pages-browser)/./src/lib/error-monitoring.ts\");\n\n\nvar ErrorType;\n(function(ErrorType) {\n    ErrorType[\"NETWORK\"] = \"NETWORK\";\n    ErrorType[\"VALIDATION\"] = \"VALIDATION\";\n    ErrorType[\"AUTHENTICATION\"] = \"AUTHENTICATION\";\n    ErrorType[\"AUTHORIZATION\"] = \"AUTHORIZATION\";\n    ErrorType[\"NOT_FOUND\"] = \"NOT_FOUND\";\n    ErrorType[\"SERVER\"] = \"SERVER\";\n    ErrorType[\"UNKNOWN\"] = \"UNKNOWN\";\n})(ErrorType || (ErrorType = {}));\n// Error classification\nconst classifyError = (error)=>{\n    const timestamp = new Date();\n    // Handle API errors\n    if (error && typeof error === \"object\" && \"status\" in error) {\n        const apiError = error;\n        switch(apiError.status){\n            case 400:\n                return {\n                    type: \"VALIDATION\",\n                    message: apiError.message || \"Invalid request data\",\n                    code: apiError.code,\n                    details: apiError.details,\n                    timestamp\n                };\n            case 401:\n                return {\n                    type: \"AUTHENTICATION\",\n                    message: \"Please log in to continue\",\n                    code: apiError.code,\n                    timestamp\n                };\n            case 403:\n                return {\n                    type: \"AUTHORIZATION\",\n                    message: \"You do not have permission to perform this action\",\n                    code: apiError.code,\n                    timestamp\n                };\n            case 404:\n                return {\n                    type: \"NOT_FOUND\",\n                    message: \"The requested resource was not found\",\n                    code: apiError.code,\n                    timestamp\n                };\n            case 422:\n                return {\n                    type: \"VALIDATION\",\n                    message: apiError.message || \"Validation failed\",\n                    code: apiError.code,\n                    details: apiError.details,\n                    timestamp\n                };\n            case 500:\n            case 502:\n            case 503:\n            case 504:\n                return {\n                    type: \"SERVER\",\n                    message: \"Server error. Please try again later.\",\n                    code: apiError.code,\n                    timestamp\n                };\n            default:\n                return {\n                    type: \"UNKNOWN\",\n                    message: apiError.message || \"An unexpected error occurred\",\n                    code: apiError.code,\n                    timestamp\n                };\n        }\n    }\n    // Handle network errors\n    if (error && error.code === \"NETWORK_ERROR\") {\n        return {\n            type: \"NETWORK\",\n            message: \"Network error. Please check your connection.\",\n            code: \"NETWORK_ERROR\",\n            timestamp\n        };\n    }\n    // Handle generic errors\n    if (error instanceof Error) {\n        return {\n            type: \"UNKNOWN\",\n            message: error.message || \"An unexpected error occurred\",\n            timestamp\n        };\n    }\n    // Fallback\n    return {\n        type: \"UNKNOWN\",\n        message: \"An unexpected error occurred\",\n        timestamp\n    };\n};\n// Error display functions\nconst showError = (error, customMessage)=>{\n    const appError = classifyError(error);\n    const message = customMessage || appError.message;\n    // Log error in development\n    if (true) {\n        console.error(\"\\uD83D\\uDEA8 Error:\", appError);\n    }\n    // Send to error monitoring system\n    (0,_error_monitoring__WEBPACK_IMPORTED_MODULE_1__.captureError)(error, {\n        customMessage,\n        appError\n    }, \"error\");\n    // Show toast notification\n    switch(appError.type){\n        case \"VALIDATION\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 6000,\n                icon: \"⚠️\"\n            });\n            break;\n        case \"AUTHENTICATION\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 8000,\n                icon: \"\\uD83D\\uDD10\"\n            });\n            break;\n        case \"AUTHORIZATION\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 6000,\n                icon: \"\\uD83D\\uDEAB\"\n            });\n            break;\n        case \"NOT_FOUND\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 5000,\n                icon: \"\\uD83D\\uDD0D\"\n            });\n            break;\n        case \"NETWORK\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 8000,\n                icon: \"\\uD83D\\uDCE1\"\n            });\n            break;\n        case \"SERVER\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 8000,\n                icon: \"\\uD83D\\uDD27\"\n            });\n            break;\n        default:\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 5000,\n                icon: \"❌\"\n            });\n    }\n    return appError;\n};\n// Success notifications\nconst showSuccess = function(message) {\n    let duration = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 4000;\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].success(message, {\n        duration,\n        icon: \"✅\"\n    });\n};\n// Info notifications\nconst showInfo = function(message) {\n    let duration = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 4000;\n    (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(message, {\n        duration,\n        icon: \"ℹ️\"\n    });\n};\n// Loading notifications\nconst showLoading = function() {\n    let message = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"Loading...\";\n    return react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].loading(message, {\n        icon: \"⏳\"\n    });\n};\n// Dismiss specific toast\nconst dismissToast = (toastId)=>{\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].dismiss(toastId);\n};\n// Dismiss all toasts\nconst dismissAllToasts = ()=>{\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].dismiss();\n};\n// Error boundary helper\nconst logError = (error, errorInfo)=>{\n    console.error(\"\\uD83D\\uDEA8 Error Boundary:\", error, errorInfo);\n    // Send to error monitoring system\n    (0,_error_monitoring__WEBPACK_IMPORTED_MODULE_1__.captureError)(error, errorInfo, \"error\");\n};\n// Validation error helpers\nconst formatValidationErrors = (errors)=>{\n    const messages = Object.entries(errors).map((param)=>{\n        let [field, fieldErrors] = param;\n        const fieldName = field.charAt(0).toUpperCase() + field.slice(1);\n        return \"\".concat(fieldName, \": \").concat(fieldErrors.join(\", \"));\n    });\n    return messages.join(\"\\n\");\n};\n// Retry helper\nconst withRetry = async function(fn) {\n    let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1000;\n    let lastError;\n    for(let i = 0; i <= maxRetries; i++){\n        try {\n            return await fn();\n        } catch (error) {\n            lastError = error;\n            if (i === maxRetries) {\n                throw error;\n            }\n            // Wait before retrying\n            await new Promise((resolve)=>setTimeout(resolve, delay * Math.pow(2, i)));\n        }\n    }\n    throw lastError;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/error-handler.ts\n"));

/***/ })

});