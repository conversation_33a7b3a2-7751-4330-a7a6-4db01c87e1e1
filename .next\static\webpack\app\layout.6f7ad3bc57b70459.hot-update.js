/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/build/polyfills/process.js ***!
  \***********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nvar _global_process, _global_process1;\nmodule.exports = ((_global_process = __webpack_require__.g.process) == null ? void 0 : _global_process.env) && typeof ((_global_process1 = __webpack_require__.g.process) == null ? void 0 : _global_process1.env) === \"object\" ? __webpack_require__.g.process : __webpack_require__(/*! next/dist/compiled/process */ \"(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js\");\n\n//# sourceMappingURL=process.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvcG9seWZpbGxzL3Byb2Nlc3MuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLHFDQUFxQyxxQkFBTSxpRkFBaUYscUJBQU0sa0VBQWtFLHFCQUFNLFdBQVcsbUJBQU8sQ0FBQyw0R0FBNEI7O0FBRXpQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvcG9seWZpbGxzL3Byb2Nlc3MuanM/NWNlOCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfZ2xvYmFsX3Byb2Nlc3MsIF9nbG9iYWxfcHJvY2VzczE7XG5tb2R1bGUuZXhwb3J0cyA9ICgoX2dsb2JhbF9wcm9jZXNzID0gZ2xvYmFsLnByb2Nlc3MpID09IG51bGwgPyB2b2lkIDAgOiBfZ2xvYmFsX3Byb2Nlc3MuZW52KSAmJiB0eXBlb2YgKChfZ2xvYmFsX3Byb2Nlc3MxID0gZ2xvYmFsLnByb2Nlc3MpID09IG51bGwgPyB2b2lkIDAgOiBfZ2xvYmFsX3Byb2Nlc3MxLmVudikgPT09IFwib2JqZWN0XCIgPyBnbG9iYWwucHJvY2VzcyA6IHJlcXVpcmUoXCJuZXh0L2Rpc3QvY29tcGlsZWQvcHJvY2Vzc1wiKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cHJvY2Vzcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/compiled/process/browser.js ***!
  \************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(function(){var e={229:function(e){var t=e.exports={};var r;var n;function defaultSetTimout(){throw new Error(\"setTimeout has not been defined\")}function defaultClearTimeout(){throw new Error(\"clearTimeout has not been defined\")}(function(){try{if(typeof setTimeout===\"function\"){r=setTimeout}else{r=defaultSetTimout}}catch(e){r=defaultSetTimout}try{if(typeof clearTimeout===\"function\"){n=clearTimeout}else{n=defaultClearTimeout}}catch(e){n=defaultClearTimeout}})();function runTimeout(e){if(r===setTimeout){return setTimeout(e,0)}if((r===defaultSetTimout||!r)&&setTimeout){r=setTimeout;return setTimeout(e,0)}try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}function runClearTimeout(e){if(n===clearTimeout){return clearTimeout(e)}if((n===defaultClearTimeout||!n)&&clearTimeout){n=clearTimeout;return clearTimeout(e)}try{return n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}var i=[];var o=false;var u;var a=-1;function cleanUpNextTick(){if(!o||!u){return}o=false;if(u.length){i=u.concat(i)}else{a=-1}if(i.length){drainQueue()}}function drainQueue(){if(o){return}var e=runTimeout(cleanUpNextTick);o=true;var t=i.length;while(t){u=i;i=[];while(++a<t){if(u){u[a].run()}}a=-1;t=i.length}u=null;o=false;runClearTimeout(e)}t.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var r=1;r<arguments.length;r++){t[r-1]=arguments[r]}}i.push(new Item(e,t));if(i.length===1&&!o){runTimeout(drainQueue)}};function Item(e,t){this.fun=e;this.array=t}Item.prototype.run=function(){this.fun.apply(null,this.array)};t.title=\"browser\";t.browser=true;t.env={};t.argv=[];t.version=\"\";t.versions={};function noop(){}t.on=noop;t.addListener=noop;t.once=noop;t.off=noop;t.removeListener=noop;t.removeAllListeners=noop;t.emit=noop;t.prependListener=noop;t.prependOnceListener=noop;t.listeners=function(e){return[]};t.binding=function(e){throw new Error(\"process.binding is not supported\")};t.cwd=function(){return\"/\"};t.chdir=function(e){throw new Error(\"process.chdir is not supported\")};t.umask=function(){return 0}}};var t={};function __nccwpck_require__(r){var n=t[r];if(n!==undefined){return n.exports}var i=t[r]={exports:{}};var o=true;try{e[r](i,i.exports,__nccwpck_require__);o=false}finally{if(o)delete t[r]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var r=__nccwpck_require__(229);module.exports=r})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"03ebd9382654\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/NDE5OSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjAzZWJkOTM4MjY1NFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/error-handler.ts":
/*!**********************************!*\
  !*** ./src/lib/error-handler.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorType: function() { return /* binding */ ErrorType; },\n/* harmony export */   classifyError: function() { return /* binding */ classifyError; },\n/* harmony export */   dismissAllToasts: function() { return /* binding */ dismissAllToasts; },\n/* harmony export */   dismissToast: function() { return /* binding */ dismissToast; },\n/* harmony export */   formatValidationErrors: function() { return /* binding */ formatValidationErrors; },\n/* harmony export */   logError: function() { return /* binding */ logError; },\n/* harmony export */   showError: function() { return /* binding */ showError; },\n/* harmony export */   showInfo: function() { return /* binding */ showInfo; },\n/* harmony export */   showLoading: function() { return /* binding */ showLoading; },\n/* harmony export */   showSuccess: function() { return /* binding */ showSuccess; },\n/* harmony export */   withRetry: function() { return /* binding */ withRetry; }\n/* harmony export */ });\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _error_monitoring__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error-monitoring */ \"(app-pages-browser)/./src/lib/error-monitoring.ts\");\n\n\nvar ErrorType;\n(function(ErrorType) {\n    ErrorType[\"NETWORK\"] = \"NETWORK\";\n    ErrorType[\"VALIDATION\"] = \"VALIDATION\";\n    ErrorType[\"AUTHENTICATION\"] = \"AUTHENTICATION\";\n    ErrorType[\"AUTHORIZATION\"] = \"AUTHORIZATION\";\n    ErrorType[\"NOT_FOUND\"] = \"NOT_FOUND\";\n    ErrorType[\"SERVER\"] = \"SERVER\";\n    ErrorType[\"UNKNOWN\"] = \"UNKNOWN\";\n})(ErrorType || (ErrorType = {}));\n// Error classification\nconst classifyError = (error)=>{\n    const timestamp = new Date();\n    // Handle API errors\n    if (error && typeof error === \"object\" && \"status\" in error) {\n        const apiError = error;\n        switch(apiError.status){\n            case 400:\n                return {\n                    type: \"VALIDATION\",\n                    message: apiError.message || \"Invalid request data\",\n                    code: apiError.code,\n                    details: apiError.details,\n                    timestamp\n                };\n            case 401:\n                return {\n                    type: \"AUTHENTICATION\",\n                    message: \"Please log in to continue\",\n                    code: apiError.code,\n                    timestamp\n                };\n            case 403:\n                return {\n                    type: \"AUTHORIZATION\",\n                    message: \"You do not have permission to perform this action\",\n                    code: apiError.code,\n                    timestamp\n                };\n            case 404:\n                return {\n                    type: \"NOT_FOUND\",\n                    message: \"The requested resource was not found\",\n                    code: apiError.code,\n                    timestamp\n                };\n            case 422:\n                return {\n                    type: \"VALIDATION\",\n                    message: apiError.message || \"Validation failed\",\n                    code: apiError.code,\n                    details: apiError.details,\n                    timestamp\n                };\n            case 500:\n            case 502:\n            case 503:\n            case 504:\n                return {\n                    type: \"SERVER\",\n                    message: \"Server error. Please try again later.\",\n                    code: apiError.code,\n                    timestamp\n                };\n            default:\n                return {\n                    type: \"UNKNOWN\",\n                    message: apiError.message || \"An unexpected error occurred\",\n                    code: apiError.code,\n                    timestamp\n                };\n        }\n    }\n    // Handle network errors\n    if (error && error.code === \"NETWORK_ERROR\") {\n        return {\n            type: \"NETWORK\",\n            message: \"Network error. Please check your connection.\",\n            code: \"NETWORK_ERROR\",\n            timestamp\n        };\n    }\n    // Handle generic errors\n    if (error instanceof Error) {\n        return {\n            type: \"UNKNOWN\",\n            message: error.message || \"An unexpected error occurred\",\n            timestamp\n        };\n    }\n    // Fallback\n    return {\n        type: \"UNKNOWN\",\n        message: \"An unexpected error occurred\",\n        timestamp\n    };\n};\n// Error display functions\nconst showError = (error, customMessage)=>{\n    const appError = classifyError(error);\n    const message = customMessage || appError.message;\n    // Log error in development\n    if (true) {\n        console.error(\"\\uD83D\\uDEA8 Error:\", appError);\n    }\n    // Show toast notification\n    switch(appError.type){\n        case \"VALIDATION\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 6000,\n                icon: \"⚠️\"\n            });\n            break;\n        case \"AUTHENTICATION\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 8000,\n                icon: \"\\uD83D\\uDD10\"\n            });\n            break;\n        case \"AUTHORIZATION\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 6000,\n                icon: \"\\uD83D\\uDEAB\"\n            });\n            break;\n        case \"NOT_FOUND\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 5000,\n                icon: \"\\uD83D\\uDD0D\"\n            });\n            break;\n        case \"NETWORK\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 8000,\n                icon: \"\\uD83D\\uDCE1\"\n            });\n            break;\n        case \"SERVER\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 8000,\n                icon: \"\\uD83D\\uDD27\"\n            });\n            break;\n        default:\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 5000,\n                icon: \"❌\"\n            });\n    }\n    return appError;\n};\n// Success notifications\nconst showSuccess = function(message) {\n    let duration = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 4000;\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].success(message, {\n        duration,\n        icon: \"✅\"\n    });\n};\n// Info notifications\nconst showInfo = function(message) {\n    let duration = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 4000;\n    (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(message, {\n        duration,\n        icon: \"ℹ️\"\n    });\n};\n// Loading notifications\nconst showLoading = function() {\n    let message = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"Loading...\";\n    return react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].loading(message, {\n        icon: \"⏳\"\n    });\n};\n// Dismiss specific toast\nconst dismissToast = (toastId)=>{\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].dismiss(toastId);\n};\n// Dismiss all toasts\nconst dismissAllToasts = ()=>{\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].dismiss();\n};\n// Error boundary helper\nconst logError = (error, errorInfo)=>{\n    console.error(\"\\uD83D\\uDEA8 Error Boundary:\", error, errorInfo);\n    // Send to error monitoring system\n    (0,_error_monitoring__WEBPACK_IMPORTED_MODULE_1__.captureError)(error, errorInfo, \"error\");\n};\n// Validation error helpers\nconst formatValidationErrors = (errors)=>{\n    const messages = Object.entries(errors).map((param)=>{\n        let [field, fieldErrors] = param;\n        const fieldName = field.charAt(0).toUpperCase() + field.slice(1);\n        return \"\".concat(fieldName, \": \").concat(fieldErrors.join(\", \"));\n    });\n    return messages.join(\"\\n\");\n};\n// Retry helper\nconst withRetry = async function(fn) {\n    let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1000;\n    let lastError;\n    for(let i = 0; i <= maxRetries; i++){\n        try {\n            return await fn();\n        } catch (error) {\n            lastError = error;\n            if (i === maxRetries) {\n                throw error;\n            }\n            // Wait before retrying\n            await new Promise((resolve)=>setTimeout(resolve, delay * Math.pow(2, i)));\n        }\n    }\n    throw lastError;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/error-handler.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/error-monitoring.ts":
/*!*************************************!*\
  !*** ./src/lib/error-monitoring.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addContext: function() { return /* binding */ addContext; },\n/* harmony export */   captureError: function() { return /* binding */ captureError; },\n/* harmony export */   capturePerformanceMetric: function() { return /* binding */ capturePerformanceMetric; },\n/* harmony export */   captureUserAction: function() { return /* binding */ captureUserAction; },\n/* harmony export */   errorMonitor: function() { return /* binding */ errorMonitor; },\n/* harmony export */   setUser: function() { return /* binding */ setUser; }\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Error monitoring and tracking system\nclass ErrorMonitor {\n    generateSessionId() {\n        return \"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    setupGlobalErrorHandlers() {\n        var _this = this;\n        // Handle JavaScript errors\n        window.addEventListener(\"error\", (event)=>{\n            this.captureError(event.error || new Error(event.message), {\n                filename: event.filename,\n                lineno: event.lineno,\n                colno: event.colno,\n                type: \"javascript\"\n            });\n        });\n        // Handle unhandled promise rejections\n        window.addEventListener(\"unhandledrejection\", (event)=>{\n            this.captureError(new Error(event.reason), {\n                type: \"unhandled_promise_rejection\",\n                reason: event.reason\n            });\n        });\n        // Handle React errors (if using React Error Boundary)\n        const originalConsoleError = console.error;\n        console.error = function() {\n            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                args[_key] = arguments[_key];\n            }\n            if (args[0] && typeof args[0] === \"string\" && args[0].includes(\"React\")) {\n                _this.captureError(new Error(args.join(\" \")), {\n                    type: \"react_error\",\n                    args\n                });\n            }\n            originalConsoleError.apply(console, args);\n        };\n    }\n    setupNetworkMonitoring() {\n        var _this = this;\n        // Monitor network status\n        window.addEventListener(\"online\", ()=>{\n            this.isOnline = true;\n            this.processQueue();\n        });\n        window.addEventListener(\"offline\", ()=>{\n            this.isOnline = false;\n        });\n        // Monitor fetch requests\n        const originalFetch = window.fetch;\n        window.fetch = async function() {\n            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                args[_key] = arguments[_key];\n            }\n            const startTime = performance.now();\n            try {\n                const response = await originalFetch(...args);\n                const endTime = performance.now();\n                const duration = endTime - startTime;\n                // Log slow requests\n                if (duration > 5000) {\n                    _this.capturePerformanceMetric(\"slow_request\", duration, \"ms\", {\n                        url: args[0],\n                        status: response.status\n                    });\n                }\n                // Log failed requests\n                if (!response.ok) {\n                    _this.captureError(new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText)), {\n                        type: \"http_error\",\n                        url: args[0],\n                        status: response.status,\n                        statusText: response.statusText\n                    });\n                }\n                return response;\n            } catch (error) {\n                _this.captureError(error, {\n                    type: \"network_error\",\n                    url: args[0]\n                });\n                throw error;\n            }\n        };\n    }\n    setupPerformanceMonitoring() {\n        // Monitor page load performance\n        window.addEventListener(\"load\", ()=>{\n            setTimeout(()=>{\n                const navigation = performance.getEntriesByType(\"navigation\")[0];\n                if (navigation) {\n                    this.capturePerformanceMetric(\"page_load_time\", navigation.loadEventEnd - navigation.fetchStart, \"ms\");\n                    this.capturePerformanceMetric(\"dom_content_loaded\", navigation.domContentLoadedEventEnd - navigation.fetchStart, \"ms\");\n                    this.capturePerformanceMetric(\"first_byte\", navigation.responseStart - navigation.fetchStart, \"ms\");\n                }\n                // Monitor Core Web Vitals\n                this.monitorCoreWebVitals();\n            }, 0);\n        });\n    }\n    monitorCoreWebVitals() {\n        // This would typically use the web-vitals library\n        // For now, we'll implement basic monitoring\n        // Monitor Largest Contentful Paint (LCP)\n        const observer = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            const lastEntry = entries[entries.length - 1];\n            if (lastEntry) {\n                this.capturePerformanceMetric(\"lcp\", lastEntry.startTime, \"ms\");\n            }\n        });\n        try {\n            observer.observe({\n                entryTypes: [\n                    \"largest-contentful-paint\"\n                ]\n            });\n        } catch (e) {\n        // LCP not supported\n        }\n        // Monitor Cumulative Layout Shift (CLS)\n        let clsValue = 0;\n        const clsObserver = new PerformanceObserver((list)=>{\n            for (const entry of list.getEntries()){\n                if (!entry.hadRecentInput) {\n                    clsValue += entry.value;\n                }\n            }\n        });\n        try {\n            clsObserver.observe({\n                entryTypes: [\n                    \"layout-shift\"\n                ]\n            });\n            // Report CLS on page unload\n            window.addEventListener(\"beforeunload\", ()=>{\n                this.capturePerformanceMetric(\"cls\", clsValue, \"score\");\n            });\n        } catch (e) {\n        // CLS not supported\n        }\n    }\n    startQueueProcessor() {\n        setInterval(()=>{\n            if (this.isOnline && this.queue.length > 0) {\n                this.processQueue();\n            }\n        }, 5000) // Process queue every 5 seconds\n        ;\n    }\n    async processQueue() {\n        if (this.queue.length === 0) return;\n        const batch = this.queue.splice(0, 10) // Process up to 10 errors at a time\n        ;\n        try {\n            await this.sendErrorBatch(batch);\n        } catch (error) {\n            // Put errors back in queue for retry\n            this.queue.unshift(...batch);\n            console.warn(\"Failed to send error batch:\", error);\n        }\n    }\n    async sendErrorBatch(errors) {\n        if (!this.isEnabled) return;\n        const payload = {\n            errors,\n            sessionId: this.sessionId,\n            userId: this.userId,\n            timestamp: new Date().toISOString()\n        };\n        const response = await fetch(this.apiEndpoint, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to send errors: \".concat(response.status));\n        }\n    }\n    captureError(error, context) {\n        let level = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"error\";\n        if (!this.isEnabled) {\n            console.error(\"Error captured:\", error, context);\n            return;\n        }\n        const errorEvent = {\n            id: \"error_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            timestamp: new Date().toISOString(),\n            message: error.message,\n            stack: error.stack,\n            url: window.location.href,\n            userAgent: navigator.userAgent,\n            userId: this.userId,\n            sessionId: this.sessionId,\n            level,\n            context,\n            fingerprint: this.generateFingerprint(error)\n        };\n        this.queue.push(errorEvent);\n        // Send immediately for critical errors\n        if (level === \"error\" && this.isOnline) {\n            this.sendErrorBatch([\n                errorEvent\n            ]).catch(()=>{\n            // Error will remain in queue for retry\n            });\n        }\n    }\n    capturePerformanceMetric(name, value, unit, context) {\n        if (!this.isEnabled) return;\n        const metric = {\n            id: \"metric_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            timestamp: new Date().toISOString(),\n            name,\n            value,\n            unit,\n            context\n        };\n        // Send performance metrics to a separate endpoint\n        fetch(\"/api/metrics\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(metric)\n        }).catch(()=>{\n        // Ignore metric sending failures\n        });\n    }\n    captureUserAction(action, element, context) {\n        if (!this.isEnabled) return;\n        const userAction = {\n            id: \"action_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            timestamp: new Date().toISOString(),\n            action,\n            element,\n            page: window.location.pathname,\n            userId: this.userId,\n            sessionId: this.sessionId,\n            context\n        };\n        // Send user actions to analytics endpoint\n        fetch(\"/api/analytics\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(userAction)\n        }).catch(()=>{\n        // Ignore analytics sending failures\n        });\n    }\n    setUser(userId) {\n        this.userId = userId;\n    }\n    addContext(key, value) {\n        // Add global context that will be included with all errors\n        if (true) {\n            window.__errorMonitorContext = {\n                ...window.__errorMonitorContext,\n                [key]: value\n            };\n        }\n    }\n    generateFingerprint(error) {\n        // Generate a fingerprint for grouping similar errors\n        const message = error.message || \"Unknown error\";\n        const stack = error.stack || \"\";\n        const firstStackLine = stack.split(\"\\n\")[1] || \"\";\n        return btoa(\"\".concat(message, \":\").concat(firstStackLine)).substr(0, 16);\n    }\n    getSessionId() {\n        return this.sessionId;\n    }\n    isMonitoringEnabled() {\n        return this.isEnabled;\n    }\n    constructor(){\n        this.maxRetries = 3;\n        this.retryDelay = 1000;\n        this.queue = [];\n        this.isOnline = true;\n        this.sessionId = this.generateSessionId();\n        this.isEnabled =  false || process.env.NEXT_PUBLIC_ENABLE_ERROR_MONITORING === \"true\";\n        this.apiEndpoint = process.env.NEXT_PUBLIC_ERROR_MONITORING_ENDPOINT || \"/api/errors\";\n        if (true) {\n            this.setupGlobalErrorHandlers();\n            this.setupNetworkMonitoring();\n            this.setupPerformanceMonitoring();\n            this.startQueueProcessor();\n        }\n    }\n}\n// Create global instance\nconst errorMonitor = new ErrorMonitor();\n// Convenience functions\nconst captureError = (error, context, level)=>{\n    errorMonitor.captureError(error, context, level);\n};\nconst capturePerformanceMetric = (name, value, unit, context)=>{\n    errorMonitor.capturePerformanceMetric(name, value, unit, context);\n};\nconst captureUserAction = (action, element, context)=>{\n    errorMonitor.captureUserAction(action, element, context);\n};\nconst setUser = (userId)=>{\n    errorMonitor.setUser(userId);\n};\nconst addContext = (key, value)=>{\n    errorMonitor.addContext(key, value);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvZXJyb3ItbW9uaXRvcmluZy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUEsdUNBQXVDO0FBcUN2QyxNQUFNQTtJQXVCSUMsb0JBQTRCO1FBQ2xDLE9BQU8sV0FBeUJDLE9BQWRDLEtBQUtDLEdBQUcsSUFBRyxLQUEyQyxPQUF4Q0YsS0FBS0csTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUc7SUFDdkU7SUFFUUMsMkJBQWlDOztRQUN2QywyQkFBMkI7UUFDM0JDLE9BQU9DLGdCQUFnQixDQUFDLFNBQVMsQ0FBQ0M7WUFDaEMsSUFBSSxDQUFDQyxZQUFZLENBQUNELE1BQU1FLEtBQUssSUFBSSxJQUFJQyxNQUFNSCxNQUFNSSxPQUFPLEdBQUc7Z0JBQ3pEQyxVQUFVTCxNQUFNSyxRQUFRO2dCQUN4QkMsUUFBUU4sTUFBTU0sTUFBTTtnQkFDcEJDLE9BQU9QLE1BQU1PLEtBQUs7Z0JBQ2xCQyxNQUFNO1lBQ1I7UUFDRjtRQUVBLHNDQUFzQztRQUN0Q1YsT0FBT0MsZ0JBQWdCLENBQUMsc0JBQXNCLENBQUNDO1lBQzdDLElBQUksQ0FBQ0MsWUFBWSxDQUFDLElBQUlFLE1BQU1ILE1BQU1TLE1BQU0sR0FBRztnQkFDekNELE1BQU07Z0JBQ05DLFFBQVFULE1BQU1TLE1BQU07WUFDdEI7UUFDRjtRQUVBLHNEQUFzRDtRQUN0RCxNQUFNQyx1QkFBdUJDLFFBQVFULEtBQUs7UUFDMUNTLFFBQVFULEtBQUssR0FBRzs2Q0FBSVU7Z0JBQUFBOztZQUNsQixJQUFJQSxJQUFJLENBQUMsRUFBRSxJQUFJLE9BQU9BLElBQUksQ0FBQyxFQUFFLEtBQUssWUFBWUEsSUFBSSxDQUFDLEVBQUUsQ0FBQ0MsUUFBUSxDQUFDLFVBQVU7Z0JBQ3ZFLE1BQUtaLFlBQVksQ0FBQyxJQUFJRSxNQUFNUyxLQUFLRSxJQUFJLENBQUMsT0FBTztvQkFDM0NOLE1BQU07b0JBQ05JO2dCQUNGO1lBQ0Y7WUFDQUYscUJBQXFCSyxLQUFLLENBQUNKLFNBQVNDO1FBQ3RDO0lBQ0Y7SUFFUUkseUJBQStCOztRQUNyQyx5QkFBeUI7UUFDekJsQixPQUFPQyxnQkFBZ0IsQ0FBQyxVQUFVO1lBQ2hDLElBQUksQ0FBQ2tCLFFBQVEsR0FBRztZQUNoQixJQUFJLENBQUNDLFlBQVk7UUFDbkI7UUFFQXBCLE9BQU9DLGdCQUFnQixDQUFDLFdBQVc7WUFDakMsSUFBSSxDQUFDa0IsUUFBUSxHQUFHO1FBQ2xCO1FBRUEseUJBQXlCO1FBQ3pCLE1BQU1FLGdCQUFnQnJCLE9BQU9zQixLQUFLO1FBQ2xDdEIsT0FBT3NCLEtBQUssR0FBRzs2Q0FBVVI7Z0JBQUFBOztZQUN2QixNQUFNUyxZQUFZQyxZQUFZN0IsR0FBRztZQUVqQyxJQUFJO2dCQUNGLE1BQU04QixXQUFXLE1BQU1KLGlCQUFpQlA7Z0JBRXhDLE1BQU1ZLFVBQVVGLFlBQVk3QixHQUFHO2dCQUMvQixNQUFNZ0MsV0FBV0QsVUFBVUg7Z0JBRTNCLG9CQUFvQjtnQkFDcEIsSUFBSUksV0FBVyxNQUFNO29CQUNuQixNQUFLQyx3QkFBd0IsQ0FBQyxnQkFBZ0JELFVBQVUsTUFBTTt3QkFDNURFLEtBQUtmLElBQUksQ0FBQyxFQUFFO3dCQUNaZ0IsUUFBUUwsU0FBU0ssTUFBTTtvQkFDekI7Z0JBQ0Y7Z0JBRUEsc0JBQXNCO2dCQUN0QixJQUFJLENBQUNMLFNBQVNNLEVBQUUsRUFBRTtvQkFDaEIsTUFBSzVCLFlBQVksQ0FBQyxJQUFJRSxNQUFNLFFBQTRCb0IsT0FBcEJBLFNBQVNLLE1BQU0sRUFBQyxNQUF3QixPQUFwQkwsU0FBU08sVUFBVSxJQUFLO3dCQUM5RXRCLE1BQU07d0JBQ05tQixLQUFLZixJQUFJLENBQUMsRUFBRTt3QkFDWmdCLFFBQVFMLFNBQVNLLE1BQU07d0JBQ3ZCRSxZQUFZUCxTQUFTTyxVQUFVO29CQUNqQztnQkFDRjtnQkFFQSxPQUFPUDtZQUNULEVBQUUsT0FBT3JCLE9BQU87Z0JBQ2QsTUFBS0QsWUFBWSxDQUFDQyxPQUFnQjtvQkFDaENNLE1BQU07b0JBQ05tQixLQUFLZixJQUFJLENBQUMsRUFBRTtnQkFDZDtnQkFDQSxNQUFNVjtZQUNSO1FBQ0Y7SUFDRjtJQUVRNkIsNkJBQW1DO1FBQ3pDLGdDQUFnQztRQUNoQ2pDLE9BQU9DLGdCQUFnQixDQUFDLFFBQVE7WUFDOUJpQyxXQUFXO2dCQUNULE1BQU1DLGFBQWFYLFlBQVlZLGdCQUFnQixDQUFDLGFBQWEsQ0FBQyxFQUFFO2dCQUVoRSxJQUFJRCxZQUFZO29CQUNkLElBQUksQ0FBQ1Asd0JBQXdCLENBQUMsa0JBQWtCTyxXQUFXRSxZQUFZLEdBQUdGLFdBQVdHLFVBQVUsRUFBRTtvQkFDakcsSUFBSSxDQUFDVix3QkFBd0IsQ0FBQyxzQkFBc0JPLFdBQVdJLHdCQUF3QixHQUFHSixXQUFXRyxVQUFVLEVBQUU7b0JBQ2pILElBQUksQ0FBQ1Ysd0JBQXdCLENBQUMsY0FBY08sV0FBV0ssYUFBYSxHQUFHTCxXQUFXRyxVQUFVLEVBQUU7Z0JBQ2hHO2dCQUVBLDBCQUEwQjtnQkFDMUIsSUFBSSxDQUFDRyxvQkFBb0I7WUFDM0IsR0FBRztRQUNMO0lBQ0Y7SUFFUUEsdUJBQTZCO1FBQ25DLGtEQUFrRDtRQUNsRCw0Q0FBNEM7UUFFNUMseUNBQXlDO1FBQ3pDLE1BQU1DLFdBQVcsSUFBSUMsb0JBQW9CLENBQUNDO1lBQ3hDLE1BQU1DLFVBQVVELEtBQUtFLFVBQVU7WUFDL0IsTUFBTUMsWUFBWUYsT0FBTyxDQUFDQSxRQUFRRyxNQUFNLEdBQUcsRUFBRTtZQUU3QyxJQUFJRCxXQUFXO2dCQUNiLElBQUksQ0FBQ25CLHdCQUF3QixDQUFDLE9BQU9tQixVQUFVeEIsU0FBUyxFQUFFO1lBQzVEO1FBQ0Y7UUFFQSxJQUFJO1lBQ0ZtQixTQUFTTyxPQUFPLENBQUM7Z0JBQUVDLFlBQVk7b0JBQUM7aUJBQTJCO1lBQUM7UUFDOUQsRUFBRSxPQUFPQyxHQUFHO1FBQ1Ysb0JBQW9CO1FBQ3RCO1FBRUEsd0NBQXdDO1FBQ3hDLElBQUlDLFdBQVc7UUFDZixNQUFNQyxjQUFjLElBQUlWLG9CQUFvQixDQUFDQztZQUMzQyxLQUFLLE1BQU1VLFNBQVNWLEtBQUtFLFVBQVUsR0FBSTtnQkFDckMsSUFBSSxDQUFDLE1BQWVTLGNBQWMsRUFBRTtvQkFDbENILFlBQVksTUFBZUksS0FBSztnQkFDbEM7WUFDRjtRQUNGO1FBRUEsSUFBSTtZQUNGSCxZQUFZSixPQUFPLENBQUM7Z0JBQUVDLFlBQVk7b0JBQUM7aUJBQWU7WUFBQztZQUVuRCw0QkFBNEI7WUFDNUJsRCxPQUFPQyxnQkFBZ0IsQ0FBQyxnQkFBZ0I7Z0JBQ3RDLElBQUksQ0FBQzJCLHdCQUF3QixDQUFDLE9BQU93QixVQUFVO1lBQ2pEO1FBQ0YsRUFBRSxPQUFPRCxHQUFHO1FBQ1Ysb0JBQW9CO1FBQ3RCO0lBQ0Y7SUFFUU0sc0JBQTRCO1FBQ2xDQyxZQUFZO1lBQ1YsSUFBSSxJQUFJLENBQUN2QyxRQUFRLElBQUksSUFBSSxDQUFDd0MsS0FBSyxDQUFDWCxNQUFNLEdBQUcsR0FBRztnQkFDMUMsSUFBSSxDQUFDNUIsWUFBWTtZQUNuQjtRQUNGLEdBQUcsTUFBTSxnQ0FBZ0M7O0lBQzNDO0lBRUEsTUFBY0EsZUFBOEI7UUFDMUMsSUFBSSxJQUFJLENBQUN1QyxLQUFLLENBQUNYLE1BQU0sS0FBSyxHQUFHO1FBRTdCLE1BQU1ZLFFBQVEsSUFBSSxDQUFDRCxLQUFLLENBQUNFLE1BQU0sQ0FBQyxHQUFHLElBQUksb0NBQW9DOztRQUUzRSxJQUFJO1lBQ0YsTUFBTSxJQUFJLENBQUNDLGNBQWMsQ0FBQ0Y7UUFDNUIsRUFBRSxPQUFPeEQsT0FBTztZQUNkLHFDQUFxQztZQUNyQyxJQUFJLENBQUN1RCxLQUFLLENBQUNJLE9BQU8sSUFBSUg7WUFDdEIvQyxRQUFRbUQsSUFBSSxDQUFDLCtCQUErQjVEO1FBQzlDO0lBQ0Y7SUFFQSxNQUFjMEQsZUFBZUcsTUFBb0IsRUFBaUI7UUFDaEUsSUFBSSxDQUFDLElBQUksQ0FBQ0MsU0FBUyxFQUFFO1FBRXJCLE1BQU1DLFVBQVU7WUFDZEY7WUFDQUcsV0FBVyxJQUFJLENBQUNBLFNBQVM7WUFDekJDLFFBQVEsSUFBSSxDQUFDQSxNQUFNO1lBQ25CQyxXQUFXLElBQUk1RSxPQUFPNkUsV0FBVztRQUNuQztRQUVBLE1BQU05QyxXQUFXLE1BQU1ILE1BQU0sSUFBSSxDQUFDa0QsV0FBVyxFQUFFO1lBQzdDQyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1lBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ1Y7UUFDdkI7UUFFQSxJQUFJLENBQUMxQyxTQUFTTSxFQUFFLEVBQUU7WUFDaEIsTUFBTSxJQUFJMUIsTUFBTSwwQkFBMEMsT0FBaEJvQixTQUFTSyxNQUFNO1FBQzNEO0lBQ0Y7SUFFTzNCLGFBQWFDLEtBQVksRUFBRTBFLE9BQTZCLEVBQXVEO1lBQXJEQyxRQUFBQSxpRUFBc0M7UUFDckcsSUFBSSxDQUFDLElBQUksQ0FBQ2IsU0FBUyxFQUFFO1lBQ25CckQsUUFBUVQsS0FBSyxDQUFDLG1CQUFtQkEsT0FBTzBFO1lBQ3hDO1FBQ0Y7UUFFQSxNQUFNRSxhQUF5QjtZQUM3QkMsSUFBSSxTQUF1QnhGLE9BQWRDLEtBQUtDLEdBQUcsSUFBRyxLQUEyQyxPQUF4Q0YsS0FBS0csTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUc7WUFDaEV3RSxXQUFXLElBQUk1RSxPQUFPNkUsV0FBVztZQUNqQ2pFLFNBQVNGLE1BQU1FLE9BQU87WUFDdEI0RSxPQUFPOUUsTUFBTThFLEtBQUs7WUFDbEJyRCxLQUFLN0IsT0FBT21GLFFBQVEsQ0FBQ0MsSUFBSTtZQUN6QkMsV0FBV0MsVUFBVUQsU0FBUztZQUM5QmhCLFFBQVEsSUFBSSxDQUFDQSxNQUFNO1lBQ25CRCxXQUFXLElBQUksQ0FBQ0EsU0FBUztZQUN6Qlc7WUFDQUQ7WUFDQVMsYUFBYSxJQUFJLENBQUNDLG1CQUFtQixDQUFDcEY7UUFDeEM7UUFFQSxJQUFJLENBQUN1RCxLQUFLLENBQUM4QixJQUFJLENBQUNUO1FBRWhCLHVDQUF1QztRQUN2QyxJQUFJRCxVQUFVLFdBQVcsSUFBSSxDQUFDNUQsUUFBUSxFQUFFO1lBQ3RDLElBQUksQ0FBQzJDLGNBQWMsQ0FBQztnQkFBQ2tCO2FBQVcsRUFBRVUsS0FBSyxDQUFDO1lBQ3RDLHVDQUF1QztZQUN6QztRQUNGO0lBQ0Y7SUFFTzlELHlCQUF5QitELElBQVksRUFBRW5DLEtBQWEsRUFBRW9DLElBQVksRUFBRWQsT0FBNkIsRUFBUTtRQUM5RyxJQUFJLENBQUMsSUFBSSxDQUFDWixTQUFTLEVBQUU7UUFFckIsTUFBTTJCLFNBQTRCO1lBQ2hDWixJQUFJLFVBQXdCeEYsT0FBZEMsS0FBS0MsR0FBRyxJQUFHLEtBQTJDLE9BQXhDRixLQUFLRyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRztZQUNqRXdFLFdBQVcsSUFBSTVFLE9BQU82RSxXQUFXO1lBQ2pDb0I7WUFDQW5DO1lBQ0FvQztZQUNBZDtRQUNGO1FBRUEsa0RBQWtEO1FBQ2xEeEQsTUFBTSxnQkFBZ0I7WUFDcEJtRCxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1lBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ2dCO1FBQ3ZCLEdBQUdILEtBQUssQ0FBQztRQUNQLGlDQUFpQztRQUNuQztJQUNGO0lBRU9JLGtCQUFrQkMsTUFBYyxFQUFFQyxPQUFnQixFQUFFbEIsT0FBNkIsRUFBUTtRQUM5RixJQUFJLENBQUMsSUFBSSxDQUFDWixTQUFTLEVBQUU7UUFFckIsTUFBTStCLGFBQXlCO1lBQzdCaEIsSUFBSSxVQUF3QnhGLE9BQWRDLEtBQUtDLEdBQUcsSUFBRyxLQUEyQyxPQUF4Q0YsS0FBS0csTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUc7WUFDakV3RSxXQUFXLElBQUk1RSxPQUFPNkUsV0FBVztZQUNqQ3dCO1lBQ0FDO1lBQ0FFLE1BQU1sRyxPQUFPbUYsUUFBUSxDQUFDZ0IsUUFBUTtZQUM5QjlCLFFBQVEsSUFBSSxDQUFDQSxNQUFNO1lBQ25CRCxXQUFXLElBQUksQ0FBQ0EsU0FBUztZQUN6QlU7UUFDRjtRQUVBLDBDQUEwQztRQUMxQ3hELE1BQU0sa0JBQWtCO1lBQ3RCbUQsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtZQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNvQjtRQUN2QixHQUFHUCxLQUFLLENBQUM7UUFDUCxvQ0FBb0M7UUFDdEM7SUFDRjtJQUVPVSxRQUFRL0IsTUFBYyxFQUFRO1FBQ25DLElBQUksQ0FBQ0EsTUFBTSxHQUFHQTtJQUNoQjtJQUVPZ0MsV0FBV0MsR0FBVyxFQUFFOUMsS0FBVSxFQUFRO1FBQy9DLDJEQUEyRDtRQUMzRCxJQUFJLElBQWtCLEVBQWE7WUFDaEN4RCxPQUFldUcscUJBQXFCLEdBQUc7Z0JBQ3RDLEdBQUcsT0FBZ0JBLHFCQUFxQjtnQkFDeEMsQ0FBQ0QsSUFBSSxFQUFFOUM7WUFDVDtRQUNGO0lBQ0Y7SUFFUWdDLG9CQUFvQnBGLEtBQVksRUFBVTtRQUNoRCxxREFBcUQ7UUFDckQsTUFBTUUsVUFBVUYsTUFBTUUsT0FBTyxJQUFJO1FBQ2pDLE1BQU00RSxRQUFROUUsTUFBTThFLEtBQUssSUFBSTtRQUM3QixNQUFNc0IsaUJBQWlCdEIsTUFBTXVCLEtBQUssQ0FBQyxLQUFLLENBQUMsRUFBRSxJQUFJO1FBRS9DLE9BQU9DLEtBQUssR0FBY0YsT0FBWGxHLFNBQVEsS0FBa0IsT0FBZmtHLGlCQUFrQjFHLE1BQU0sQ0FBQyxHQUFHO0lBQ3hEO0lBRU82RyxlQUF1QjtRQUM1QixPQUFPLElBQUksQ0FBQ3ZDLFNBQVM7SUFDdkI7SUFFT3dDLHNCQUErQjtRQUNwQyxPQUFPLElBQUksQ0FBQzFDLFNBQVM7SUFDdkI7SUExVEEyQyxhQUFjO2FBTE5DLGFBQXFCO2FBQ3JCQyxhQUFxQjthQUNyQnBELFFBQXNCLEVBQUU7YUFDeEJ4QyxXQUFvQjtRQUcxQixJQUFJLENBQUNpRCxTQUFTLEdBQUcsSUFBSSxDQUFDNUUsaUJBQWlCO1FBQ3ZDLElBQUksQ0FBQzBFLFNBQVMsR0FBRzhDLE1BQXlCLElBQWdCQSxPQUFPQSxDQUFDQyxHQUFHLENBQUNDLG1DQUFtQyxLQUFLO1FBQzlHLElBQUksQ0FBQzFDLFdBQVcsR0FBR3dDLE9BQU9BLENBQUNDLEdBQUcsQ0FBQ0UscUNBQXFDLElBQUk7UUFFeEUsSUFBSSxJQUFrQixFQUFhO1lBQ2pDLElBQUksQ0FBQ3BILHdCQUF3QjtZQUM3QixJQUFJLENBQUNtQixzQkFBc0I7WUFDM0IsSUFBSSxDQUFDZSwwQkFBMEI7WUFDL0IsSUFBSSxDQUFDd0IsbUJBQW1CO1FBQzFCO0lBQ0Y7QUFnVEY7QUFFQSx5QkFBeUI7QUFDbEIsTUFBTTJELGVBQWUsSUFBSTdILGVBQWM7QUFFOUMsd0JBQXdCO0FBQ2pCLE1BQU1ZLGVBQWUsQ0FBQ0MsT0FBYzBFLFNBQStCQztJQUN4RXFDLGFBQWFqSCxZQUFZLENBQUNDLE9BQU8wRSxTQUFTQztBQUM1QyxFQUFDO0FBRU0sTUFBTW5ELDJCQUEyQixDQUFDK0QsTUFBY25DLE9BQWVvQyxNQUFjZDtJQUNsRnNDLGFBQWF4Rix3QkFBd0IsQ0FBQytELE1BQU1uQyxPQUFPb0MsTUFBTWQ7QUFDM0QsRUFBQztBQUVNLE1BQU1nQixvQkFBb0IsQ0FBQ0MsUUFBZ0JDLFNBQWtCbEI7SUFDbEVzQyxhQUFhdEIsaUJBQWlCLENBQUNDLFFBQVFDLFNBQVNsQjtBQUNsRCxFQUFDO0FBRU0sTUFBTXNCLFVBQVUsQ0FBQy9CO0lBQ3RCK0MsYUFBYWhCLE9BQU8sQ0FBQy9CO0FBQ3ZCLEVBQUM7QUFFTSxNQUFNZ0MsYUFBYSxDQUFDQyxLQUFhOUM7SUFDdEM0RCxhQUFhZixVQUFVLENBQUNDLEtBQUs5QztBQUMvQixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9saWIvZXJyb3ItbW9uaXRvcmluZy50cz85NWViIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEVycm9yIG1vbml0b3JpbmcgYW5kIHRyYWNraW5nIHN5c3RlbVxuXG5pbnRlcmZhY2UgRXJyb3JFdmVudCB7XG4gIGlkOiBzdHJpbmdcbiAgdGltZXN0YW1wOiBzdHJpbmdcbiAgbWVzc2FnZTogc3RyaW5nXG4gIHN0YWNrPzogc3RyaW5nXG4gIHVybDogc3RyaW5nXG4gIHVzZXJBZ2VudDogc3RyaW5nXG4gIHVzZXJJZD86IHN0cmluZ1xuICBzZXNzaW9uSWQ6IHN0cmluZ1xuICBsZXZlbDogJ2Vycm9yJyB8ICd3YXJuaW5nJyB8ICdpbmZvJ1xuICBjb250ZXh0PzogUmVjb3JkPHN0cmluZywgYW55PlxuICB0YWdzPzogc3RyaW5nW11cbiAgZmluZ2VycHJpbnQ/OiBzdHJpbmdcbn1cblxuaW50ZXJmYWNlIFBlcmZvcm1hbmNlTWV0cmljIHtcbiAgaWQ6IHN0cmluZ1xuICB0aW1lc3RhbXA6IHN0cmluZ1xuICBuYW1lOiBzdHJpbmdcbiAgdmFsdWU6IG51bWJlclxuICB1bml0OiBzdHJpbmdcbiAgY29udGV4dD86IFJlY29yZDxzdHJpbmcsIGFueT5cbn1cblxuaW50ZXJmYWNlIFVzZXJBY3Rpb24ge1xuICBpZDogc3RyaW5nXG4gIHRpbWVzdGFtcDogc3RyaW5nXG4gIGFjdGlvbjogc3RyaW5nXG4gIGVsZW1lbnQ/OiBzdHJpbmdcbiAgcGFnZTogc3RyaW5nXG4gIHVzZXJJZD86IHN0cmluZ1xuICBzZXNzaW9uSWQ6IHN0cmluZ1xuICBjb250ZXh0PzogUmVjb3JkPHN0cmluZywgYW55PlxufVxuXG5jbGFzcyBFcnJvck1vbml0b3Ige1xuICBwcml2YXRlIHNlc3Npb25JZDogc3RyaW5nXG4gIHByaXZhdGUgdXNlcklkPzogc3RyaW5nXG4gIHByaXZhdGUgaXNFbmFibGVkOiBib29sZWFuXG4gIHByaXZhdGUgYXBpRW5kcG9pbnQ6IHN0cmluZ1xuICBwcml2YXRlIG1heFJldHJpZXM6IG51bWJlciA9IDNcbiAgcHJpdmF0ZSByZXRyeURlbGF5OiBudW1iZXIgPSAxMDAwXG4gIHByaXZhdGUgcXVldWU6IEVycm9yRXZlbnRbXSA9IFtdXG4gIHByaXZhdGUgaXNPbmxpbmU6IGJvb2xlYW4gPSB0cnVlXG5cbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5zZXNzaW9uSWQgPSB0aGlzLmdlbmVyYXRlU2Vzc2lvbklkKClcbiAgICB0aGlzLmlzRW5hYmxlZCA9IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicgfHwgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRU5BQkxFX0VSUk9SX01PTklUT1JJTkcgPT09ICd0cnVlJ1xuICAgIHRoaXMuYXBpRW5kcG9pbnQgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19FUlJPUl9NT05JVE9SSU5HX0VORFBPSU5UIHx8ICcvYXBpL2Vycm9ycydcbiAgICBcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHRoaXMuc2V0dXBHbG9iYWxFcnJvckhhbmRsZXJzKClcbiAgICAgIHRoaXMuc2V0dXBOZXR3b3JrTW9uaXRvcmluZygpXG4gICAgICB0aGlzLnNldHVwUGVyZm9ybWFuY2VNb25pdG9yaW5nKClcbiAgICAgIHRoaXMuc3RhcnRRdWV1ZVByb2Nlc3NvcigpXG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBnZW5lcmF0ZVNlc3Npb25JZCgpOiBzdHJpbmcge1xuICAgIHJldHVybiBgc2Vzc2lvbl8ke0RhdGUubm93KCl9XyR7TWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpfWBcbiAgfVxuXG4gIHByaXZhdGUgc2V0dXBHbG9iYWxFcnJvckhhbmRsZXJzKCk6IHZvaWQge1xuICAgIC8vIEhhbmRsZSBKYXZhU2NyaXB0IGVycm9yc1xuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdlcnJvcicsIChldmVudCkgPT4ge1xuICAgICAgdGhpcy5jYXB0dXJlRXJyb3IoZXZlbnQuZXJyb3IgfHwgbmV3IEVycm9yKGV2ZW50Lm1lc3NhZ2UpLCB7XG4gICAgICAgIGZpbGVuYW1lOiBldmVudC5maWxlbmFtZSxcbiAgICAgICAgbGluZW5vOiBldmVudC5saW5lbm8sXG4gICAgICAgIGNvbG5vOiBldmVudC5jb2xubyxcbiAgICAgICAgdHlwZTogJ2phdmFzY3JpcHQnLFxuICAgICAgfSlcbiAgICB9KVxuXG4gICAgLy8gSGFuZGxlIHVuaGFuZGxlZCBwcm9taXNlIHJlamVjdGlvbnNcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigndW5oYW5kbGVkcmVqZWN0aW9uJywgKGV2ZW50KSA9PiB7XG4gICAgICB0aGlzLmNhcHR1cmVFcnJvcihuZXcgRXJyb3IoZXZlbnQucmVhc29uKSwge1xuICAgICAgICB0eXBlOiAndW5oYW5kbGVkX3Byb21pc2VfcmVqZWN0aW9uJyxcbiAgICAgICAgcmVhc29uOiBldmVudC5yZWFzb24sXG4gICAgICB9KVxuICAgIH0pXG5cbiAgICAvLyBIYW5kbGUgUmVhY3QgZXJyb3JzIChpZiB1c2luZyBSZWFjdCBFcnJvciBCb3VuZGFyeSlcbiAgICBjb25zdCBvcmlnaW5hbENvbnNvbGVFcnJvciA9IGNvbnNvbGUuZXJyb3JcbiAgICBjb25zb2xlLmVycm9yID0gKC4uLmFyZ3MpID0+IHtcbiAgICAgIGlmIChhcmdzWzBdICYmIHR5cGVvZiBhcmdzWzBdID09PSAnc3RyaW5nJyAmJiBhcmdzWzBdLmluY2x1ZGVzKCdSZWFjdCcpKSB7XG4gICAgICAgIHRoaXMuY2FwdHVyZUVycm9yKG5ldyBFcnJvcihhcmdzLmpvaW4oJyAnKSksIHtcbiAgICAgICAgICB0eXBlOiAncmVhY3RfZXJyb3InLFxuICAgICAgICAgIGFyZ3MsXG4gICAgICAgIH0pXG4gICAgICB9XG4gICAgICBvcmlnaW5hbENvbnNvbGVFcnJvci5hcHBseShjb25zb2xlLCBhcmdzKVxuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgc2V0dXBOZXR3b3JrTW9uaXRvcmluZygpOiB2b2lkIHtcbiAgICAvLyBNb25pdG9yIG5ldHdvcmsgc3RhdHVzXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ29ubGluZScsICgpID0+IHtcbiAgICAgIHRoaXMuaXNPbmxpbmUgPSB0cnVlXG4gICAgICB0aGlzLnByb2Nlc3NRdWV1ZSgpXG4gICAgfSlcblxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdvZmZsaW5lJywgKCkgPT4ge1xuICAgICAgdGhpcy5pc09ubGluZSA9IGZhbHNlXG4gICAgfSlcblxuICAgIC8vIE1vbml0b3IgZmV0Y2ggcmVxdWVzdHNcbiAgICBjb25zdCBvcmlnaW5hbEZldGNoID0gd2luZG93LmZldGNoXG4gICAgd2luZG93LmZldGNoID0gYXN5bmMgKC4uLmFyZ3MpID0+IHtcbiAgICAgIGNvbnN0IHN0YXJ0VGltZSA9IHBlcmZvcm1hbmNlLm5vdygpXG4gICAgICBcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgb3JpZ2luYWxGZXRjaCguLi5hcmdzKVxuICAgICAgICBcbiAgICAgICAgY29uc3QgZW5kVGltZSA9IHBlcmZvcm1hbmNlLm5vdygpXG4gICAgICAgIGNvbnN0IGR1cmF0aW9uID0gZW5kVGltZSAtIHN0YXJ0VGltZVxuXG4gICAgICAgIC8vIExvZyBzbG93IHJlcXVlc3RzXG4gICAgICAgIGlmIChkdXJhdGlvbiA+IDUwMDApIHtcbiAgICAgICAgICB0aGlzLmNhcHR1cmVQZXJmb3JtYW5jZU1ldHJpYygnc2xvd19yZXF1ZXN0JywgZHVyYXRpb24sICdtcycsIHtcbiAgICAgICAgICAgIHVybDogYXJnc1swXSxcbiAgICAgICAgICAgIHN0YXR1czogcmVzcG9uc2Uuc3RhdHVzLFxuICAgICAgICAgIH0pXG4gICAgICAgIH1cblxuICAgICAgICAvLyBMb2cgZmFpbGVkIHJlcXVlc3RzXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgICB0aGlzLmNhcHR1cmVFcnJvcihuZXcgRXJyb3IoYEhUVFAgJHtyZXNwb25zZS5zdGF0dXN9OiAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCksIHtcbiAgICAgICAgICAgIHR5cGU6ICdodHRwX2Vycm9yJyxcbiAgICAgICAgICAgIHVybDogYXJnc1swXSxcbiAgICAgICAgICAgIHN0YXR1czogcmVzcG9uc2Uuc3RhdHVzLFxuICAgICAgICAgICAgc3RhdHVzVGV4dDogcmVzcG9uc2Uuc3RhdHVzVGV4dCxcbiAgICAgICAgICB9KVxuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIHJlc3BvbnNlXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICB0aGlzLmNhcHR1cmVFcnJvcihlcnJvciBhcyBFcnJvciwge1xuICAgICAgICAgIHR5cGU6ICduZXR3b3JrX2Vycm9yJyxcbiAgICAgICAgICB1cmw6IGFyZ3NbMF0sXG4gICAgICAgIH0pXG4gICAgICAgIHRocm93IGVycm9yXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBzZXR1cFBlcmZvcm1hbmNlTW9uaXRvcmluZygpOiB2b2lkIHtcbiAgICAvLyBNb25pdG9yIHBhZ2UgbG9hZCBwZXJmb3JtYW5jZVxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdsb2FkJywgKCkgPT4ge1xuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGNvbnN0IG5hdmlnYXRpb24gPSBwZXJmb3JtYW5jZS5nZXRFbnRyaWVzQnlUeXBlKCduYXZpZ2F0aW9uJylbMF0gYXMgUGVyZm9ybWFuY2VOYXZpZ2F0aW9uVGltaW5nXG4gICAgICAgIFxuICAgICAgICBpZiAobmF2aWdhdGlvbikge1xuICAgICAgICAgIHRoaXMuY2FwdHVyZVBlcmZvcm1hbmNlTWV0cmljKCdwYWdlX2xvYWRfdGltZScsIG5hdmlnYXRpb24ubG9hZEV2ZW50RW5kIC0gbmF2aWdhdGlvbi5mZXRjaFN0YXJ0LCAnbXMnKVxuICAgICAgICAgIHRoaXMuY2FwdHVyZVBlcmZvcm1hbmNlTWV0cmljKCdkb21fY29udGVudF9sb2FkZWQnLCBuYXZpZ2F0aW9uLmRvbUNvbnRlbnRMb2FkZWRFdmVudEVuZCAtIG5hdmlnYXRpb24uZmV0Y2hTdGFydCwgJ21zJylcbiAgICAgICAgICB0aGlzLmNhcHR1cmVQZXJmb3JtYW5jZU1ldHJpYygnZmlyc3RfYnl0ZScsIG5hdmlnYXRpb24ucmVzcG9uc2VTdGFydCAtIG5hdmlnYXRpb24uZmV0Y2hTdGFydCwgJ21zJylcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIE1vbml0b3IgQ29yZSBXZWIgVml0YWxzXG4gICAgICAgIHRoaXMubW9uaXRvckNvcmVXZWJWaXRhbHMoKVxuICAgICAgfSwgMClcbiAgICB9KVxuICB9XG5cbiAgcHJpdmF0ZSBtb25pdG9yQ29yZVdlYlZpdGFscygpOiB2b2lkIHtcbiAgICAvLyBUaGlzIHdvdWxkIHR5cGljYWxseSB1c2UgdGhlIHdlYi12aXRhbHMgbGlicmFyeVxuICAgIC8vIEZvciBub3csIHdlJ2xsIGltcGxlbWVudCBiYXNpYyBtb25pdG9yaW5nXG4gICAgXG4gICAgLy8gTW9uaXRvciBMYXJnZXN0IENvbnRlbnRmdWwgUGFpbnQgKExDUClcbiAgICBjb25zdCBvYnNlcnZlciA9IG5ldyBQZXJmb3JtYW5jZU9ic2VydmVyKChsaXN0KSA9PiB7XG4gICAgICBjb25zdCBlbnRyaWVzID0gbGlzdC5nZXRFbnRyaWVzKClcbiAgICAgIGNvbnN0IGxhc3RFbnRyeSA9IGVudHJpZXNbZW50cmllcy5sZW5ndGggLSAxXVxuICAgICAgXG4gICAgICBpZiAobGFzdEVudHJ5KSB7XG4gICAgICAgIHRoaXMuY2FwdHVyZVBlcmZvcm1hbmNlTWV0cmljKCdsY3AnLCBsYXN0RW50cnkuc3RhcnRUaW1lLCAnbXMnKVxuICAgICAgfVxuICAgIH0pXG5cbiAgICB0cnkge1xuICAgICAgb2JzZXJ2ZXIub2JzZXJ2ZSh7IGVudHJ5VHlwZXM6IFsnbGFyZ2VzdC1jb250ZW50ZnVsLXBhaW50J10gfSlcbiAgICB9IGNhdGNoIChlKSB7XG4gICAgICAvLyBMQ1Agbm90IHN1cHBvcnRlZFxuICAgIH1cblxuICAgIC8vIE1vbml0b3IgQ3VtdWxhdGl2ZSBMYXlvdXQgU2hpZnQgKENMUylcbiAgICBsZXQgY2xzVmFsdWUgPSAwXG4gICAgY29uc3QgY2xzT2JzZXJ2ZXIgPSBuZXcgUGVyZm9ybWFuY2VPYnNlcnZlcigobGlzdCkgPT4ge1xuICAgICAgZm9yIChjb25zdCBlbnRyeSBvZiBsaXN0LmdldEVudHJpZXMoKSkge1xuICAgICAgICBpZiAoIShlbnRyeSBhcyBhbnkpLmhhZFJlY2VudElucHV0KSB7XG4gICAgICAgICAgY2xzVmFsdWUgKz0gKGVudHJ5IGFzIGFueSkudmFsdWVcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pXG5cbiAgICB0cnkge1xuICAgICAgY2xzT2JzZXJ2ZXIub2JzZXJ2ZSh7IGVudHJ5VHlwZXM6IFsnbGF5b3V0LXNoaWZ0J10gfSlcbiAgICAgIFxuICAgICAgLy8gUmVwb3J0IENMUyBvbiBwYWdlIHVubG9hZFxuICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2JlZm9yZXVubG9hZCcsICgpID0+IHtcbiAgICAgICAgdGhpcy5jYXB0dXJlUGVyZm9ybWFuY2VNZXRyaWMoJ2NscycsIGNsc1ZhbHVlLCAnc2NvcmUnKVxuICAgICAgfSlcbiAgICB9IGNhdGNoIChlKSB7XG4gICAgICAvLyBDTFMgbm90IHN1cHBvcnRlZFxuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgc3RhcnRRdWV1ZVByb2Nlc3NvcigpOiB2b2lkIHtcbiAgICBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICBpZiAodGhpcy5pc09ubGluZSAmJiB0aGlzLnF1ZXVlLmxlbmd0aCA+IDApIHtcbiAgICAgICAgdGhpcy5wcm9jZXNzUXVldWUoKVxuICAgICAgfVxuICAgIH0sIDUwMDApIC8vIFByb2Nlc3MgcXVldWUgZXZlcnkgNSBzZWNvbmRzXG4gIH1cblxuICBwcml2YXRlIGFzeW5jIHByb2Nlc3NRdWV1ZSgpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBpZiAodGhpcy5xdWV1ZS5sZW5ndGggPT09IDApIHJldHVyblxuXG4gICAgY29uc3QgYmF0Y2ggPSB0aGlzLnF1ZXVlLnNwbGljZSgwLCAxMCkgLy8gUHJvY2VzcyB1cCB0byAxMCBlcnJvcnMgYXQgYSB0aW1lXG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHRoaXMuc2VuZEVycm9yQmF0Y2goYmF0Y2gpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIC8vIFB1dCBlcnJvcnMgYmFjayBpbiBxdWV1ZSBmb3IgcmV0cnlcbiAgICAgIHRoaXMucXVldWUudW5zaGlmdCguLi5iYXRjaClcbiAgICAgIGNvbnNvbGUud2FybignRmFpbGVkIHRvIHNlbmQgZXJyb3IgYmF0Y2g6JywgZXJyb3IpXG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBhc3luYyBzZW5kRXJyb3JCYXRjaChlcnJvcnM6IEVycm9yRXZlbnRbXSk6IFByb21pc2U8dm9pZD4ge1xuICAgIGlmICghdGhpcy5pc0VuYWJsZWQpIHJldHVyblxuXG4gICAgY29uc3QgcGF5bG9hZCA9IHtcbiAgICAgIGVycm9ycyxcbiAgICAgIHNlc3Npb25JZDogdGhpcy5zZXNzaW9uSWQsXG4gICAgICB1c2VySWQ6IHRoaXMudXNlcklkLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgfVxuXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCh0aGlzLmFwaUVuZHBvaW50LCB7XG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIH0sXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShwYXlsb2FkKSxcbiAgICB9KVxuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gc2VuZCBlcnJvcnM6ICR7cmVzcG9uc2Uuc3RhdHVzfWApXG4gICAgfVxuICB9XG5cbiAgcHVibGljIGNhcHR1cmVFcnJvcihlcnJvcjogRXJyb3IsIGNvbnRleHQ/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+LCBsZXZlbDogJ2Vycm9yJyB8ICd3YXJuaW5nJyB8ICdpbmZvJyA9ICdlcnJvcicpOiB2b2lkIHtcbiAgICBpZiAoIXRoaXMuaXNFbmFibGVkKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjYXB0dXJlZDonLCBlcnJvciwgY29udGV4dClcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGNvbnN0IGVycm9yRXZlbnQ6IEVycm9yRXZlbnQgPSB7XG4gICAgICBpZDogYGVycm9yXyR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YCxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgbWVzc2FnZTogZXJyb3IubWVzc2FnZSxcbiAgICAgIHN0YWNrOiBlcnJvci5zdGFjayxcbiAgICAgIHVybDogd2luZG93LmxvY2F0aW9uLmhyZWYsXG4gICAgICB1c2VyQWdlbnQ6IG5hdmlnYXRvci51c2VyQWdlbnQsXG4gICAgICB1c2VySWQ6IHRoaXMudXNlcklkLFxuICAgICAgc2Vzc2lvbklkOiB0aGlzLnNlc3Npb25JZCxcbiAgICAgIGxldmVsLFxuICAgICAgY29udGV4dCxcbiAgICAgIGZpbmdlcnByaW50OiB0aGlzLmdlbmVyYXRlRmluZ2VycHJpbnQoZXJyb3IpLFxuICAgIH1cblxuICAgIHRoaXMucXVldWUucHVzaChlcnJvckV2ZW50KVxuXG4gICAgLy8gU2VuZCBpbW1lZGlhdGVseSBmb3IgY3JpdGljYWwgZXJyb3JzXG4gICAgaWYgKGxldmVsID09PSAnZXJyb3InICYmIHRoaXMuaXNPbmxpbmUpIHtcbiAgICAgIHRoaXMuc2VuZEVycm9yQmF0Y2goW2Vycm9yRXZlbnRdKS5jYXRjaCgoKSA9PiB7XG4gICAgICAgIC8vIEVycm9yIHdpbGwgcmVtYWluIGluIHF1ZXVlIGZvciByZXRyeVxuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICBwdWJsaWMgY2FwdHVyZVBlcmZvcm1hbmNlTWV0cmljKG5hbWU6IHN0cmluZywgdmFsdWU6IG51bWJlciwgdW5pdDogc3RyaW5nLCBjb250ZXh0PzogUmVjb3JkPHN0cmluZywgYW55Pik6IHZvaWQge1xuICAgIGlmICghdGhpcy5pc0VuYWJsZWQpIHJldHVyblxuXG4gICAgY29uc3QgbWV0cmljOiBQZXJmb3JtYW5jZU1ldHJpYyA9IHtcbiAgICAgIGlkOiBgbWV0cmljXyR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YCxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgbmFtZSxcbiAgICAgIHZhbHVlLFxuICAgICAgdW5pdCxcbiAgICAgIGNvbnRleHQsXG4gICAgfVxuXG4gICAgLy8gU2VuZCBwZXJmb3JtYW5jZSBtZXRyaWNzIHRvIGEgc2VwYXJhdGUgZW5kcG9pbnRcbiAgICBmZXRjaCgnL2FwaS9tZXRyaWNzJywge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICB9LFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkobWV0cmljKSxcbiAgICB9KS5jYXRjaCgoKSA9PiB7XG4gICAgICAvLyBJZ25vcmUgbWV0cmljIHNlbmRpbmcgZmFpbHVyZXNcbiAgICB9KVxuICB9XG5cbiAgcHVibGljIGNhcHR1cmVVc2VyQWN0aW9uKGFjdGlvbjogc3RyaW5nLCBlbGVtZW50Pzogc3RyaW5nLCBjb250ZXh0PzogUmVjb3JkPHN0cmluZywgYW55Pik6IHZvaWQge1xuICAgIGlmICghdGhpcy5pc0VuYWJsZWQpIHJldHVyblxuXG4gICAgY29uc3QgdXNlckFjdGlvbjogVXNlckFjdGlvbiA9IHtcbiAgICAgIGlkOiBgYWN0aW9uXyR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YCxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgYWN0aW9uLFxuICAgICAgZWxlbWVudCxcbiAgICAgIHBhZ2U6IHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZSxcbiAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsXG4gICAgICBzZXNzaW9uSWQ6IHRoaXMuc2Vzc2lvbklkLFxuICAgICAgY29udGV4dCxcbiAgICB9XG5cbiAgICAvLyBTZW5kIHVzZXIgYWN0aW9ucyB0byBhbmFseXRpY3MgZW5kcG9pbnRcbiAgICBmZXRjaCgnL2FwaS9hbmFseXRpY3MnLCB7XG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIH0sXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh1c2VyQWN0aW9uKSxcbiAgICB9KS5jYXRjaCgoKSA9PiB7XG4gICAgICAvLyBJZ25vcmUgYW5hbHl0aWNzIHNlbmRpbmcgZmFpbHVyZXNcbiAgICB9KVxuICB9XG5cbiAgcHVibGljIHNldFVzZXIodXNlcklkOiBzdHJpbmcpOiB2b2lkIHtcbiAgICB0aGlzLnVzZXJJZCA9IHVzZXJJZFxuICB9XG5cbiAgcHVibGljIGFkZENvbnRleHQoa2V5OiBzdHJpbmcsIHZhbHVlOiBhbnkpOiB2b2lkIHtcbiAgICAvLyBBZGQgZ2xvYmFsIGNvbnRleHQgdGhhdCB3aWxsIGJlIGluY2x1ZGVkIHdpdGggYWxsIGVycm9yc1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgKHdpbmRvdyBhcyBhbnkpLl9fZXJyb3JNb25pdG9yQ29udGV4dCA9IHtcbiAgICAgICAgLi4uKHdpbmRvdyBhcyBhbnkpLl9fZXJyb3JNb25pdG9yQ29udGV4dCxcbiAgICAgICAgW2tleV06IHZhbHVlLFxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgZ2VuZXJhdGVGaW5nZXJwcmludChlcnJvcjogRXJyb3IpOiBzdHJpbmcge1xuICAgIC8vIEdlbmVyYXRlIGEgZmluZ2VycHJpbnQgZm9yIGdyb3VwaW5nIHNpbWlsYXIgZXJyb3JzXG4gICAgY29uc3QgbWVzc2FnZSA9IGVycm9yLm1lc3NhZ2UgfHwgJ1Vua25vd24gZXJyb3InXG4gICAgY29uc3Qgc3RhY2sgPSBlcnJvci5zdGFjayB8fCAnJ1xuICAgIGNvbnN0IGZpcnN0U3RhY2tMaW5lID0gc3RhY2suc3BsaXQoJ1xcbicpWzFdIHx8ICcnXG4gICAgXG4gICAgcmV0dXJuIGJ0b2EoYCR7bWVzc2FnZX06JHtmaXJzdFN0YWNrTGluZX1gKS5zdWJzdHIoMCwgMTYpXG4gIH1cblxuICBwdWJsaWMgZ2V0U2Vzc2lvbklkKCk6IHN0cmluZyB7XG4gICAgcmV0dXJuIHRoaXMuc2Vzc2lvbklkXG4gIH1cblxuICBwdWJsaWMgaXNNb25pdG9yaW5nRW5hYmxlZCgpOiBib29sZWFuIHtcbiAgICByZXR1cm4gdGhpcy5pc0VuYWJsZWRcbiAgfVxufVxuXG4vLyBDcmVhdGUgZ2xvYmFsIGluc3RhbmNlXG5leHBvcnQgY29uc3QgZXJyb3JNb25pdG9yID0gbmV3IEVycm9yTW9uaXRvcigpXG5cbi8vIENvbnZlbmllbmNlIGZ1bmN0aW9uc1xuZXhwb3J0IGNvbnN0IGNhcHR1cmVFcnJvciA9IChlcnJvcjogRXJyb3IsIGNvbnRleHQ/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+LCBsZXZlbD86ICdlcnJvcicgfCAnd2FybmluZycgfCAnaW5mbycpID0+IHtcbiAgZXJyb3JNb25pdG9yLmNhcHR1cmVFcnJvcihlcnJvciwgY29udGV4dCwgbGV2ZWwpXG59XG5cbmV4cG9ydCBjb25zdCBjYXB0dXJlUGVyZm9ybWFuY2VNZXRyaWMgPSAobmFtZTogc3RyaW5nLCB2YWx1ZTogbnVtYmVyLCB1bml0OiBzdHJpbmcsIGNvbnRleHQ/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+KSA9PiB7XG4gIGVycm9yTW9uaXRvci5jYXB0dXJlUGVyZm9ybWFuY2VNZXRyaWMobmFtZSwgdmFsdWUsIHVuaXQsIGNvbnRleHQpXG59XG5cbmV4cG9ydCBjb25zdCBjYXB0dXJlVXNlckFjdGlvbiA9IChhY3Rpb246IHN0cmluZywgZWxlbWVudD86IHN0cmluZywgY29udGV4dD86IFJlY29yZDxzdHJpbmcsIGFueT4pID0+IHtcbiAgZXJyb3JNb25pdG9yLmNhcHR1cmVVc2VyQWN0aW9uKGFjdGlvbiwgZWxlbWVudCwgY29udGV4dClcbn1cblxuZXhwb3J0IGNvbnN0IHNldFVzZXIgPSAodXNlcklkOiBzdHJpbmcpID0+IHtcbiAgZXJyb3JNb25pdG9yLnNldFVzZXIodXNlcklkKVxufVxuXG5leHBvcnQgY29uc3QgYWRkQ29udGV4dCA9IChrZXk6IHN0cmluZywgdmFsdWU6IGFueSkgPT4ge1xuICBlcnJvck1vbml0b3IuYWRkQ29udGV4dChrZXksIHZhbHVlKVxufVxuIl0sIm5hbWVzIjpbIkVycm9yTW9uaXRvciIsImdlbmVyYXRlU2Vzc2lvbklkIiwiTWF0aCIsIkRhdGUiLCJub3ciLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0ciIsInNldHVwR2xvYmFsRXJyb3JIYW5kbGVycyIsIndpbmRvdyIsImFkZEV2ZW50TGlzdGVuZXIiLCJldmVudCIsImNhcHR1cmVFcnJvciIsImVycm9yIiwiRXJyb3IiLCJtZXNzYWdlIiwiZmlsZW5hbWUiLCJsaW5lbm8iLCJjb2xubyIsInR5cGUiLCJyZWFzb24iLCJvcmlnaW5hbENvbnNvbGVFcnJvciIsImNvbnNvbGUiLCJhcmdzIiwiaW5jbHVkZXMiLCJqb2luIiwiYXBwbHkiLCJzZXR1cE5ldHdvcmtNb25pdG9yaW5nIiwiaXNPbmxpbmUiLCJwcm9jZXNzUXVldWUiLCJvcmlnaW5hbEZldGNoIiwiZmV0Y2giLCJzdGFydFRpbWUiLCJwZXJmb3JtYW5jZSIsInJlc3BvbnNlIiwiZW5kVGltZSIsImR1cmF0aW9uIiwiY2FwdHVyZVBlcmZvcm1hbmNlTWV0cmljIiwidXJsIiwic3RhdHVzIiwib2siLCJzdGF0dXNUZXh0Iiwic2V0dXBQZXJmb3JtYW5jZU1vbml0b3JpbmciLCJzZXRUaW1lb3V0IiwibmF2aWdhdGlvbiIsImdldEVudHJpZXNCeVR5cGUiLCJsb2FkRXZlbnRFbmQiLCJmZXRjaFN0YXJ0IiwiZG9tQ29udGVudExvYWRlZEV2ZW50RW5kIiwicmVzcG9uc2VTdGFydCIsIm1vbml0b3JDb3JlV2ViVml0YWxzIiwib2JzZXJ2ZXIiLCJQZXJmb3JtYW5jZU9ic2VydmVyIiwibGlzdCIsImVudHJpZXMiLCJnZXRFbnRyaWVzIiwibGFzdEVudHJ5IiwibGVuZ3RoIiwib2JzZXJ2ZSIsImVudHJ5VHlwZXMiLCJlIiwiY2xzVmFsdWUiLCJjbHNPYnNlcnZlciIsImVudHJ5IiwiaGFkUmVjZW50SW5wdXQiLCJ2YWx1ZSIsInN0YXJ0UXVldWVQcm9jZXNzb3IiLCJzZXRJbnRlcnZhbCIsInF1ZXVlIiwiYmF0Y2giLCJzcGxpY2UiLCJzZW5kRXJyb3JCYXRjaCIsInVuc2hpZnQiLCJ3YXJuIiwiZXJyb3JzIiwiaXNFbmFibGVkIiwicGF5bG9hZCIsInNlc3Npb25JZCIsInVzZXJJZCIsInRpbWVzdGFtcCIsInRvSVNPU3RyaW5nIiwiYXBpRW5kcG9pbnQiLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJjb250ZXh0IiwibGV2ZWwiLCJlcnJvckV2ZW50IiwiaWQiLCJzdGFjayIsImxvY2F0aW9uIiwiaHJlZiIsInVzZXJBZ2VudCIsIm5hdmlnYXRvciIsImZpbmdlcnByaW50IiwiZ2VuZXJhdGVGaW5nZXJwcmludCIsInB1c2giLCJjYXRjaCIsIm5hbWUiLCJ1bml0IiwibWV0cmljIiwiY2FwdHVyZVVzZXJBY3Rpb24iLCJhY3Rpb24iLCJlbGVtZW50IiwidXNlckFjdGlvbiIsInBhZ2UiLCJwYXRobmFtZSIsInNldFVzZXIiLCJhZGRDb250ZXh0Iiwia2V5IiwiX19lcnJvck1vbml0b3JDb250ZXh0IiwiZmlyc3RTdGFja0xpbmUiLCJzcGxpdCIsImJ0b2EiLCJnZXRTZXNzaW9uSWQiLCJpc01vbml0b3JpbmdFbmFibGVkIiwiY29uc3RydWN0b3IiLCJtYXhSZXRyaWVzIiwicmV0cnlEZWxheSIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19FTkFCTEVfRVJST1JfTU9OSVRPUklORyIsIk5FWFRfUFVCTElDX0VSUk9SX01PTklUT1JJTkdfRU5EUE9JTlQiLCJlcnJvck1vbml0b3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/error-monitoring.ts\n"));

/***/ })

});