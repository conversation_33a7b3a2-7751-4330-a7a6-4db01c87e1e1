/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22PageErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CErrorMonitoringProvider.tsx%22%2C%22ids%22%3A%5B%22ErrorMonitoringProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22PageErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CErrorMonitoringProvider.tsx%22%2C%22ids%22%3A%5B%22ErrorMonitoringProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-playfair\"}],\"variableName\":\"playfair\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-playfair\\\"}],\\\"variableName\\\":\\\"playfair\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(app-pages-browser)/./src/components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ErrorMonitoringProvider.tsx */ \"(app-pages-browser)/./src/components/providers/ErrorMonitoringProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ToastProvider.tsx */ \"(app-pages-browser)/./src/components/providers/ToastProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/styles/globals.css */ \"(app-pages-browser)/./src/styles/globals.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQmxhY2slNUMlNUNEZXNrdG9wJTVDJTVDc21hcnQtb2ZmLXBsYW4lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTJDJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtaW50ZXIlNUMlMjIlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNCbGFjayU1QyU1Q0Rlc2t0b3AlNUMlNUNzbWFydC1vZmYtcGxhbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIyUGxheWZhaXJfRGlzcGxheSU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCUyQyU1QyUyMnZhcmlhYmxlJTVDJTIyJTNBJTVDJTIyLS1mb250LXBsYXlmYWlyJTVDJTIyJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIycGxheWZhaXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQmxhY2slNUMlNUNEZXNrdG9wJTVDJTVDc21hcnQtb2ZmLXBsYW4lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDRXJyb3JCb3VuZGFyeS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQYWdlRXJyb3JCb3VuZGFyeSUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNCbGFjayU1QyU1Q0Rlc2t0b3AlNUMlNUNzbWFydC1vZmYtcGxhbiU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNwcm92aWRlcnMlNUMlNUNFcnJvck1vbml0b3JpbmdQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJFcnJvck1vbml0b3JpbmdQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNCbGFjayU1QyU1Q0Rlc2t0b3AlNUMlNUNzbWFydC1vZmYtcGxhbiU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNwcm92aWRlcnMlNUMlNUNUb2FzdFByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRvYXN0UHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQmxhY2slNUMlNUNEZXNrdG9wJTVDJTVDc21hcnQtb2ZmLXBsYW4lNUMlNUNzcmMlNUMlNUNzdHlsZXMlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDBmQUE0UjtBQUM1UjtBQUNBLDRoQkFBNlM7QUFDN1M7QUFDQSw4TEFBeUo7QUFDeko7QUFDQSxzT0FBb0w7QUFDcEw7QUFDQSxrTkFBZ0s7QUFDaEs7QUFDQSwwS0FBd0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz8zY2RlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQmxhY2tcXFxcRGVza3RvcFxcXFxzbWFydC1vZmYtcGxhblxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxmb250XFxcXGdvb2dsZVxcXFx0YXJnZXQuY3NzP3tcXFwicGF0aFxcXCI6XFxcInNyY1xcXFxcXFxcYXBwXFxcXFxcXFxsYXlvdXQudHN4XFxcIixcXFwiaW1wb3J0XFxcIjpcXFwiSW50ZXJcXFwiLFxcXCJhcmd1bWVudHNcXFwiOlt7XFxcInN1YnNldHNcXFwiOltcXFwibGF0aW5cXFwiXSxcXFwidmFyaWFibGVcXFwiOlxcXCItLWZvbnQtaW50ZXJcXFwifV0sXFxcInZhcmlhYmxlTmFtZVxcXCI6XFxcImludGVyXFxcIn1cIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEJsYWNrXFxcXERlc2t0b3BcXFxcc21hcnQtb2ZmLXBsYW5cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZm9udFxcXFxnb29nbGVcXFxcdGFyZ2V0LmNzcz97XFxcInBhdGhcXFwiOlxcXCJzcmNcXFxcXFxcXGFwcFxcXFxcXFxcbGF5b3V0LnRzeFxcXCIsXFxcImltcG9ydFxcXCI6XFxcIlBsYXlmYWlyX0Rpc3BsYXlcXFwiLFxcXCJhcmd1bWVudHNcXFwiOlt7XFxcInN1YnNldHNcXFwiOltcXFwibGF0aW5cXFwiXSxcXFwidmFyaWFibGVcXFwiOlxcXCItLWZvbnQtcGxheWZhaXJcXFwifV0sXFxcInZhcmlhYmxlTmFtZVxcXCI6XFxcInBsYXlmYWlyXFxcIn1cIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlBhZ2VFcnJvckJvdW5kYXJ5XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQmxhY2tcXFxcRGVza3RvcFxcXFxzbWFydC1vZmYtcGxhblxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxFcnJvckJvdW5kYXJ5LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiRXJyb3JNb25pdG9yaW5nUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxCbGFja1xcXFxEZXNrdG9wXFxcXHNtYXJ0LW9mZi1wbGFuXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFxFcnJvck1vbml0b3JpbmdQcm92aWRlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0UHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxCbGFja1xcXFxEZXNrdG9wXFxcXHNtYXJ0LW9mZi1wbGFuXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFxUb2FzdFByb3ZpZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQmxhY2tcXFxcRGVza3RvcFxcXFxzbWFydC1vZmYtcGxhblxcXFxzcmNcXFxcc3R5bGVzXFxcXGdsb2JhbHMuY3NzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22PageErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CErrorMonitoringProvider.tsx%22%2C%22ids%22%3A%5B%22ErrorMonitoringProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/ErrorMonitoringProvider.tsx":
/*!**************************************************************!*\
  !*** ./src/components/providers/ErrorMonitoringProvider.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorMonitoringProvider: function() { return /* binding */ ErrorMonitoringProvider; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/error-monitoring */ \"(app-pages-browser)/./src/lib/error-monitoring.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ ErrorMonitoringProvider auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ErrorMonitoringProvider(param) {\n    let { children } = param;\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize error monitoring\n        if (true) {\n            // Add global context\n            (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.addContext)(\"app_version\", process.env.NEXT_PUBLIC_APP_VERSION || \"1.0.0\");\n            (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.addContext)(\"environment\", \"development\");\n            (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.addContext)(\"build_time\", process.env.NEXT_PUBLIC_BUILD_TIME || new Date().toISOString());\n            // Add browser information\n            (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.addContext)(\"browser\", {\n                userAgent: navigator.userAgent,\n                language: navigator.language,\n                platform: navigator.platform,\n                cookieEnabled: navigator.cookieEnabled,\n                onLine: navigator.onLine\n            });\n            // Add screen information\n            (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.addContext)(\"screen\", {\n                width: screen.width,\n                height: screen.height,\n                colorDepth: screen.colorDepth,\n                pixelDepth: screen.pixelDepth\n            });\n            // Add viewport information\n            (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.addContext)(\"viewport\", {\n                width: window.innerWidth,\n                height: window.innerHeight,\n                devicePixelRatio: window.devicePixelRatio\n            });\n            // Monitor route changes\n            const handleRouteChange = ()=>{\n                (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.addContext)(\"current_route\", window.location.pathname);\n                (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.addContext)(\"current_url\", window.location.href);\n            };\n            // Initial route\n            handleRouteChange();\n            // Listen for route changes (for client-side navigation)\n            window.addEventListener(\"popstate\", handleRouteChange);\n            // Monitor performance\n            const observer = new PerformanceObserver((list)=>{\n                for (const entry of list.getEntries()){\n                    if (entry.entryType === \"navigation\") {\n                        const navEntry = entry;\n                        (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.addContext)(\"page_load_performance\", {\n                            loadTime: navEntry.loadEventEnd - navEntry.fetchStart,\n                            domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.fetchStart,\n                            firstByte: navEntry.responseStart - navEntry.fetchStart\n                        });\n                    }\n                }\n            });\n            try {\n                observer.observe({\n                    entryTypes: [\n                        \"navigation\"\n                    ]\n                });\n            } catch (e) {\n            // Performance Observer not supported\n            }\n            // Monitor memory usage (if available)\n            if (\"memory\" in performance) {\n                const memoryInfo = performance.memory;\n                (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.addContext)(\"memory\", {\n                    usedJSHeapSize: memoryInfo.usedJSHeapSize,\n                    totalJSHeapSize: memoryInfo.totalJSHeapSize,\n                    jsHeapSizeLimit: memoryInfo.jsHeapSizeLimit\n                });\n            }\n            // Monitor connection (if available)\n            if (\"connection\" in navigator) {\n                const connection = navigator.connection;\n                (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.addContext)(\"connection\", {\n                    effectiveType: connection.effectiveType,\n                    downlink: connection.downlink,\n                    rtt: connection.rtt,\n                    saveData: connection.saveData\n                });\n            }\n            // Cleanup function\n            return ()=>{\n                window.removeEventListener(\"popstate\", handleRouteChange);\n                observer.disconnect();\n            };\n        }\n    }, []);\n    // Set user when authentication state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This would typically come from your auth context/store\n        // For now, we'll check localStorage for user info\n        const checkUser = ()=>{\n            try {\n                const userStr = localStorage.getItem(\"user\");\n                if (userStr) {\n                    const user = JSON.parse(userStr);\n                    if (user.id) {\n                        (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.setUser)(user.id);\n                        (0,_lib_error_monitoring__WEBPACK_IMPORTED_MODULE_2__.addContext)(\"user\", {\n                            id: user.id,\n                            email: user.email,\n                            role: user.role\n                        });\n                    }\n                }\n            } catch (e) {\n            // Ignore parsing errors\n            }\n        };\n        checkUser();\n        // Listen for storage changes (user login/logout)\n        window.addEventListener(\"storage\", checkUser);\n        return ()=>{\n            window.removeEventListener(\"storage\", checkUser);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(ErrorMonitoringProvider, \"3ubReDTFssvu4DHeldAg55cW/CI=\");\n_c = ErrorMonitoringProvider;\nvar _c;\n$RefreshReg$(_c, \"ErrorMonitoringProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/ErrorMonitoringProvider.tsx\n"));

/***/ })

});