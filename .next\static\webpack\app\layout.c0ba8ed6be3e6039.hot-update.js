"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"5a7a43a0dc17\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/NDE5OSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjVhN2E0M2EwZGMxN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ComponentErrorBoundary: function() { return /* binding */ ComponentErrorBoundary; },\n/* harmony export */   ErrorBoundary: function() { return /* binding */ ErrorBoundary; },\n/* harmony export */   PageErrorBoundary: function() { return /* binding */ PageErrorBoundary; },\n/* harmony export */   SectionErrorBoundary: function() { return /* binding */ SectionErrorBoundary; },\n/* harmony export */   withErrorBoundary: function() { return /* binding */ withErrorBoundary; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _lib_error_handler__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/error-handler */ \"(app-pages-browser)/./src/lib/error-handler.ts\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,PageErrorBoundary,SectionErrorBoundary,ComponentErrorBoundary,withErrorBoundary auto */ \n\n\n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    static getDerivedStateFromError(error) {\n        // Update state so the next render will show the fallback UI\n        return {\n            hasError: true,\n            error,\n            errorId: \"error_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9))\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        // Log the error\n        (0,_lib_error_handler__WEBPACK_IMPORTED_MODULE_3__.logError)(error, errorInfo);\n        // Update state with error info\n        this.setState({\n            errorInfo\n        });\n        // Call custom error handler if provided\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        }\n        // Send to error monitoring service in production\n        if (false) {}\n    }\n    render() {\n        if (this.state.hasError) {\n            // Custom fallback UI\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            // Default fallback UI based on level\n            return this.renderErrorUI();\n        }\n        return this.props.children;\n    }\n    renderErrorUI() {\n        const { level = \"component\", showDetails = false } = this.props;\n        const { error, errorInfo, errorId } = this.state;\n        // Component-level error (minimal UI)\n        if (level === \"component\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-4 bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-8 h-8 text-red-500 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-700 mb-2\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: this.handleRetry,\n                            size: \"sm\",\n                            variant: \"outline\",\n                            className: \"text-red-700 border-red-300 hover:bg-red-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, this);\n        }\n        // Section-level error (medium UI)\n        if (level === \"section\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-[200px] bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-md px-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-12 h-12 text-red-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-red-800 mb-2\",\n                            children: \"Oops! Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600 mb-4\",\n                            children: \"We encountered an error while loading this section. Please try again.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: this.handleRetry,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    className: \"text-red-700 border-red-300 hover:bg-red-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Try Again\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: this.handleReload,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    className: \"text-red-700 border-red-300 hover:bg-red-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Reload Page\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this);\n        }\n        // Page-level error (full UI)\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-ivory flex items-center justify-center px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-20 h-20 text-red-500 mx-auto mb-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-soft-brown mb-4\",\n                                children: \"Something went wrong\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-warm-gray text-lg mb-6\",\n                                children: \"We're sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            showDetails && error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                className: \"text-left bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                        className: \"cursor-pointer text-red-700 font-medium mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Error Details\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-red-600 space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Error ID:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" \",\n                                                    errorId\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Message:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" \",\n                                                    error.message\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 19\n                                            }, this),\n                                             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Stack:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                className: \"mt-1 text-xs bg-red-100 p-2 rounded overflow-auto\",\n                                                                children: error.stack\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    errorInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Component Stack:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                className: \"mt-1 text-xs bg-red-100 p-2 rounded overflow-auto\",\n                                                                children: errorInfo.componentStack\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: this.handleRetry,\n                                className: \"bg-soft-brown hover:bg-deep-brown text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Try Again\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: this.handleReload,\n                                variant: \"outline\",\n                                className: \"border-soft-brown text-soft-brown hover:bg-soft-brown hover:text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Reload Page\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: this.handleGoHome,\n                                variant: \"outline\",\n                                className: \"border-soft-brown text-soft-brown hover:bg-soft-brown hover:text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Go Home\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 text-sm text-warm-gray\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"If this problem persists, please contact our support team at\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:<EMAIL>\",\n                                        className: \"text-soft-brown hover:underline\",\n                                        children: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this),\n                            errorId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2\",\n                                children: [\n                                    \"Reference ID:\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"bg-gray-100 px-1 rounded\",\n                                        children: errorId\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, this);\n    }\n    constructor(props){\n        super(props);\n        this.handleRetry = ()=>{\n            this.setState({\n                hasError: false,\n                error: null,\n                errorInfo: null,\n                errorId: null\n            });\n        };\n        this.handleReload = ()=>{\n            window.location.reload();\n        };\n        this.handleGoHome = ()=>{\n            window.location.href = \"/\";\n        };\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null,\n            errorId: null\n        };\n    }\n}\n// Specialized Error Boundaries\nfunction PageErrorBoundary(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n        level: \"page\",\n        showDetails: \"development\" === \"development\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 260,\n        columnNumber: 5\n    }, this);\n}\n_c = PageErrorBoundary;\nfunction SectionErrorBoundary(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n        level: \"section\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 270,\n        columnNumber: 10\n    }, this);\n}\n_c1 = SectionErrorBoundary;\nfunction ComponentErrorBoundary(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n        level: \"component\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 274,\n        columnNumber: 10\n    }, this);\n}\n_c2 = ComponentErrorBoundary;\n// HOC for wrapping components with error boundaries\nfunction withErrorBoundary(Component) {\n    let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"component\";\n    const WrappedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n            level: level,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 283,\n            columnNumber: 5\n        }, this);\n    WrappedComponent.displayName = \"withErrorBoundary(\".concat(Component.displayName || Component.name, \")\");\n    return WrappedComponent;\n}\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PageErrorBoundary\");\n$RefreshReg$(_c1, \"SectionErrorBoundary\");\n$RefreshReg$(_c2, \"ComponentErrorBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ErrorBoundary.tsx\n"));

/***/ })

});