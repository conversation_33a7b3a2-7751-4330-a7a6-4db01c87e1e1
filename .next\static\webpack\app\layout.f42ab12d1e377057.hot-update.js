"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"257f906cf017\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/NDE5OSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjI1N2Y5MDZjZjAxN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/error-handler.ts":
/*!**********************************!*\
  !*** ./src/lib/error-handler.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorType: function() { return /* binding */ ErrorType; },\n/* harmony export */   classifyError: function() { return /* binding */ classifyError; },\n/* harmony export */   dismissAllToasts: function() { return /* binding */ dismissAllToasts; },\n/* harmony export */   dismissToast: function() { return /* binding */ dismissToast; },\n/* harmony export */   formatValidationErrors: function() { return /* binding */ formatValidationErrors; },\n/* harmony export */   logError: function() { return /* binding */ logError; },\n/* harmony export */   showError: function() { return /* binding */ showError; },\n/* harmony export */   showInfo: function() { return /* binding */ showInfo; },\n/* harmony export */   showLoading: function() { return /* binding */ showLoading; },\n/* harmony export */   showSuccess: function() { return /* binding */ showSuccess; },\n/* harmony export */   withRetry: function() { return /* binding */ withRetry; }\n/* harmony export */ });\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar ErrorType;\n(function(ErrorType) {\n    ErrorType[\"NETWORK\"] = \"NETWORK\";\n    ErrorType[\"VALIDATION\"] = \"VALIDATION\";\n    ErrorType[\"AUTHENTICATION\"] = \"AUTHENTICATION\";\n    ErrorType[\"AUTHORIZATION\"] = \"AUTHORIZATION\";\n    ErrorType[\"NOT_FOUND\"] = \"NOT_FOUND\";\n    ErrorType[\"SERVER\"] = \"SERVER\";\n    ErrorType[\"UNKNOWN\"] = \"UNKNOWN\";\n})(ErrorType || (ErrorType = {}));\n// Error classification\nconst classifyError = (error)=>{\n    const timestamp = new Date();\n    // Handle API errors\n    if (error && typeof error === \"object\" && \"status\" in error) {\n        const apiError = error;\n        switch(apiError.status){\n            case 400:\n                return {\n                    type: \"VALIDATION\",\n                    message: apiError.message || \"Invalid request data\",\n                    code: apiError.code,\n                    details: apiError.details,\n                    timestamp\n                };\n            case 401:\n                return {\n                    type: \"AUTHENTICATION\",\n                    message: \"Please log in to continue\",\n                    code: apiError.code,\n                    timestamp\n                };\n            case 403:\n                return {\n                    type: \"AUTHORIZATION\",\n                    message: \"You do not have permission to perform this action\",\n                    code: apiError.code,\n                    timestamp\n                };\n            case 404:\n                return {\n                    type: \"NOT_FOUND\",\n                    message: \"The requested resource was not found\",\n                    code: apiError.code,\n                    timestamp\n                };\n            case 422:\n                return {\n                    type: \"VALIDATION\",\n                    message: apiError.message || \"Validation failed\",\n                    code: apiError.code,\n                    details: apiError.details,\n                    timestamp\n                };\n            case 500:\n            case 502:\n            case 503:\n            case 504:\n                return {\n                    type: \"SERVER\",\n                    message: \"Server error. Please try again later.\",\n                    code: apiError.code,\n                    timestamp\n                };\n            default:\n                return {\n                    type: \"UNKNOWN\",\n                    message: apiError.message || \"An unexpected error occurred\",\n                    code: apiError.code,\n                    timestamp\n                };\n        }\n    }\n    // Handle network errors\n    if (error && error.code === \"NETWORK_ERROR\") {\n        return {\n            type: \"NETWORK\",\n            message: \"Network error. Please check your connection.\",\n            code: \"NETWORK_ERROR\",\n            timestamp\n        };\n    }\n    // Handle generic errors\n    if (error instanceof Error) {\n        return {\n            type: \"UNKNOWN\",\n            message: error.message || \"An unexpected error occurred\",\n            timestamp\n        };\n    }\n    // Fallback\n    return {\n        type: \"UNKNOWN\",\n        message: \"An unexpected error occurred\",\n        timestamp\n    };\n};\n// Error display functions\nconst showError = (error, customMessage)=>{\n    const appError = classifyError(error);\n    const message = customMessage || appError.message;\n    // Log error in development\n    if (true) {\n        console.error(\"\\uD83D\\uDEA8 Error:\", appError);\n    }\n    // Show toast notification\n    switch(appError.type){\n        case \"VALIDATION\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 6000,\n                icon: \"⚠️\"\n            });\n            break;\n        case \"AUTHENTICATION\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 8000,\n                icon: \"\\uD83D\\uDD10\"\n            });\n            break;\n        case \"AUTHORIZATION\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 6000,\n                icon: \"\\uD83D\\uDEAB\"\n            });\n            break;\n        case \"NOT_FOUND\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 5000,\n                icon: \"\\uD83D\\uDD0D\"\n            });\n            break;\n        case \"NETWORK\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 8000,\n                icon: \"\\uD83D\\uDCE1\"\n            });\n            break;\n        case \"SERVER\":\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 8000,\n                icon: \"\\uD83D\\uDD27\"\n            });\n            break;\n        default:\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message, {\n                duration: 5000,\n                icon: \"❌\"\n            });\n    }\n    return appError;\n};\n// Success notifications\nconst showSuccess = function(message) {\n    let duration = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 4000;\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].success(message, {\n        duration,\n        icon: \"✅\"\n    });\n};\n// Info notifications\nconst showInfo = function(message) {\n    let duration = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 4000;\n    (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(message, {\n        duration,\n        icon: \"ℹ️\"\n    });\n};\n// Loading notifications\nconst showLoading = function() {\n    let message = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"Loading...\";\n    return react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].loading(message, {\n        icon: \"⏳\"\n    });\n};\n// Dismiss specific toast\nconst dismissToast = (toastId)=>{\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].dismiss(toastId);\n};\n// Dismiss all toasts\nconst dismissAllToasts = ()=>{\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].dismiss();\n};\n// Error boundary helper\nconst logError = (error, errorInfo)=>{\n    console.error(\"\\uD83D\\uDEA8 Error Boundary:\", error, errorInfo);\n    // In production, you might want to send this to an error reporting service\n    if (false) {}\n};\n// Validation error helpers\nconst formatValidationErrors = (errors)=>{\n    const messages = Object.entries(errors).map((param)=>{\n        let [field, fieldErrors] = param;\n        const fieldName = field.charAt(0).toUpperCase() + field.slice(1);\n        return \"\".concat(fieldName, \": \").concat(fieldErrors.join(\", \"));\n    });\n    return messages.join(\"\\n\");\n};\n// Retry helper\nconst withRetry = async function(fn) {\n    let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1000;\n    let lastError;\n    for(let i = 0; i <= maxRetries; i++){\n        try {\n            return await fn();\n        } catch (error) {\n            lastError = error;\n            if (i === maxRetries) {\n                throw error;\n            }\n            // Wait before retrying\n            await new Promise((resolve)=>setTimeout(resolve, delay * Math.pow(2, i)));\n        }\n    }\n    throw lastError;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/error-handler.ts\n"));

/***/ })

});