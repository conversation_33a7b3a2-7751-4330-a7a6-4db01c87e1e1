"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AllPropertiesPage.tsx":
/*!**********************************************!*\
  !*** ./src/components/AllPropertiesPage.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AllPropertiesPage: function() { return /* binding */ AllPropertiesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_skeleton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _ui_empty_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/empty-state */ \"(app-pages-browser)/./src/components/ui/empty-state.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _ui_slider__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ui/slider */ \"(app-pages-browser)/./src/components/ui/slider.tsx\");\n/* harmony import */ var _ui_switch__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ruler.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hammer.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bed.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bath.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./figma/ImageWithFallback */ \"(app-pages-browser)/./src/components/figma/ImageWithFallback.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst initialFilters = {\n    priceUnit: \"total\",\n    minArea: 0,\n    maxArea: 5000,\n    developmentStatus: [],\n    unitTypes: [],\n    bedrooms: [],\n    salesStatus: [],\n    completionDate: \"all\",\n    minPrice: 0,\n    maxPrice: ********\n};\nfunction AllPropertiesPage(param) {\n    let { onProjectSelect, onBack, selectedDeveloper } = param;\n    var _completionDateOptions_find;\n    _s();\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"featured\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filtersOpen, setFiltersOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFilterModalOpen, setIsFilterModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFilters);\n    // Local state for properties\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch properties function\n    const fetchProperties = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            console.log(\"\\uD83D\\uDE80 Fetching properties from API...\");\n            const response = await fetch(\"/api/properties\");\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            console.log(\"✅ Properties fetched successfully:\", data);\n            console.log(\"\\uD83D\\uDCCA Data type:\", typeof data);\n            console.log(\"\\uD83D\\uDCCA Data structure:\", Object.keys(data));\n            // Handle the API response structure\n            let propertiesArray = [];\n            if (data.success && data.data) {\n                // Our API wraps the external response in { success: true, data: externalData }\n                const externalData = data.data;\n                if (externalData.items && Array.isArray(externalData.items)) {\n                    // The external API returns { items: [...], pagination: {...} }\n                    propertiesArray = externalData.items;\n                } else if (Array.isArray(externalData)) {\n                    // Fallback: if external data is directly an array\n                    propertiesArray = externalData;\n                }\n            } else if (Array.isArray(data)) {\n                // If data is directly an array\n                propertiesArray = data;\n            } else if (data.data && Array.isArray(data.data)) {\n                // If data is wrapped in a data property\n                propertiesArray = data.data;\n            } else {\n                // Fallback: try to find any array in the response\n                const possibleArrays = Object.values(data).filter(Array.isArray);\n                if (possibleArrays.length > 0) {\n                    propertiesArray = possibleArrays[0];\n                }\n            }\n            console.log(\"\\uD83D\\uDCCB Properties array:\", propertiesArray);\n            console.log(\"\\uD83D\\uDCCA Properties count:\", propertiesArray.length);\n            setProperties(propertiesArray);\n        } catch (err) {\n            console.error(\"❌ Error fetching properties:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch properties\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Fetch properties on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProperties();\n    }, []);\n    // Use real properties data - ensure it's always an array\n    const allProperties = Array.isArray(properties) ? properties : [];\n    // Filter options\n    const developmentStatusOptions = [\n        \"Presale\",\n        \"Under Construction\",\n        \"Completed\"\n    ];\n    const unitTypeOptions = [\n        \"Apartments\",\n        \"Villa\",\n        \"Townhouse\",\n        \"Duplex\",\n        \"Penthouse\"\n    ];\n    const bedroomOptions = [\n        \"Studio\",\n        \"1 BR\",\n        \"2 BR\",\n        \"3 BR\",\n        \"4 BR\",\n        \"5+ BR\"\n    ];\n    const salesStatusOptions = [\n        \"Announced\",\n        \"Presale (EOI)\",\n        \"Start of Sales\",\n        \"On Sale\",\n        \"Out of Stock\"\n    ];\n    const completionDateOptions = [\n        {\n            value: \"all\",\n            label: \"All Projects\"\n        },\n        {\n            value: \"12months\",\n            label: \"Completing in 12 months\"\n        },\n        {\n            value: \"2years\",\n            label: \"Completing in 2 years\"\n        },\n        {\n            value: \"3years\",\n            label: \"Completing in 3 years\"\n        },\n        {\n            value: \"4years\",\n            label: \"Completing in 4 years\"\n        },\n        {\n            value: \"5years\",\n            label: \"Completing in 5+ years\"\n        }\n    ];\n    // Helper functions for filters\n    const toggleArrayFilter = (array, value)=>{\n        return array.includes(value) ? array.filter((item)=>item !== value) : [\n            ...array,\n            value\n        ];\n    };\n    const updateFilters = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    const resetFilters = ()=>{\n        setFilters(initialFilters);\n    };\n    const hasActiveFilters = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return filters.priceUnit !== \"total\" || filters.minArea !== 0 || filters.maxArea !== 5000 || filters.developmentStatus.length > 0 || filters.unitTypes.length > 0 || filters.bedrooms.length > 0 || filters.salesStatus.length > 0 || filters.completionDate !== \"all\" || filters.minPrice !== 0 || filters.maxPrice !== ********;\n    }, [\n        filters\n    ]);\n    const activeFilterCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        let count = 0;\n        if (filters.priceUnit !== \"total\") count++;\n        if (filters.minArea !== 0 || filters.maxArea !== 5000) count++;\n        if (filters.developmentStatus.length > 0) count++;\n        if (filters.unitTypes.length > 0) count++;\n        if (filters.bedrooms.length > 0) count++;\n        if (filters.salesStatus.length > 0) count++;\n        if (filters.completionDate !== \"all\") count++;\n        if (filters.minPrice !== 0 || filters.maxPrice !== ********) count++;\n        return count;\n    }, [\n        filters\n    ]);\n    // Apply filters to properties\n    const filteredProperties = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // Ensure allProperties is an array before filtering\n        if (!Array.isArray(allProperties)) {\n            console.warn(\"⚠️ allProperties is not an array:\", allProperties);\n            return [];\n        }\n        let filtered = [\n            ...allProperties\n        ];\n        // Apply search query\n        if (searchQuery) {\n            filtered = filtered.filter((property)=>{\n                var _property_name, _property_area, _property_developer;\n                return ((_property_name = property.name) === null || _property_name === void 0 ? void 0 : _property_name.toLowerCase().includes(searchQuery.toLowerCase())) || ((_property_area = property.area) === null || _property_area === void 0 ? void 0 : _property_area.toLowerCase().includes(searchQuery.toLowerCase())) || ((_property_developer = property.developer) === null || _property_developer === void 0 ? void 0 : _property_developer.toLowerCase().includes(searchQuery.toLowerCase()));\n            });\n        }\n        // Apply developer filter if specified\n        if (selectedDeveloper) {\n            filtered = filtered.filter((property)=>{\n                var _property_developer;\n                return ((_property_developer = property.developer) === null || _property_developer === void 0 ? void 0 : _property_developer.name) === selectedDeveloper.name;\n            });\n        }\n        // Apply area filters\n        filtered = filtered.filter((property)=>property.area >= filters.minArea && property.area <= filters.maxArea);\n        // Apply price filters\n        const getPrice = (property)=>{\n            const price = property.price || 0;\n            return filters.priceUnit === \"sqft\" ? price / property.area : price;\n        };\n        filtered = filtered.filter((property)=>{\n            const price = getPrice(property);\n            return price >= filters.minPrice && price <= filters.maxPrice;\n        });\n        // Apply development status filter\n        if (filters.developmentStatus.length > 0) {\n            filtered = filtered.filter((property)=>filters.developmentStatus.includes(property.status));\n        }\n        // Apply unit type filter\n        if (filters.unitTypes.length > 0) {\n            filtered = filtered.filter((property)=>filters.unitTypes.includes(property.propertyType));\n        }\n        // Apply bedroom filter\n        if (filters.bedrooms.length > 0) {\n            filtered = filtered.filter((property)=>{\n                const bedrooms = \"\".concat(property.bedrooms, \" BR\");\n                return filters.bedrooms.includes(bedrooms);\n            });\n        }\n        return filtered;\n    }, [\n        allProperties,\n        searchQuery,\n        selectedDeveloper,\n        filters\n    ]);\n    const sortedProperties = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const sorted = [\n            ...filteredProperties\n        ];\n        switch(sortBy){\n            case \"price-low\":\n                return sorted.sort((a, b)=>(a.price || 0) - (b.price || 0));\n            case \"price-high\":\n                return sorted.sort((a, b)=>(b.price || 0) - (a.price || 0));\n            case \"completion\":\n                return sorted.sort((a, b)=>(a.completionDate || \"\").localeCompare(b.completionDate || \"\"));\n            case \"location\":\n                return sorted.sort((a, b)=>{\n                    var _a_location, _b_location;\n                    return (((_a_location = a.location) === null || _a_location === void 0 ? void 0 : _a_location.name) || \"\").localeCompare(((_b_location = b.location) === null || _b_location === void 0 ? void 0 : _b_location.name) || \"\");\n                });\n            case \"featured\":\n            default:\n                return sorted.sort((a, b)=>(b.isFeatured ? 1 : 0) - (a.isFeatured ? 1 : 0));\n        }\n    }, [\n        filteredProperties,\n        sortBy\n    ]);\n    const LuxuryCheckboxGroup = (param)=>{\n        let { options, values, onChange, label, icon: Icon } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n            className: \"border-beige/60 shadow-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                    className: \"pb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                className: \"w-5 h-5 text-gold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 359,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                    className: \"space-y-3\",\n                    children: options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 h-5 rounded-md border-2 flex items-center justify-center cursor-pointer transition-all duration-200 \".concat(values.includes(option) ? \"bg-gold border-gold text-charcoal shadow-sm\" : \"border-soft-brown/30 hover:border-gold hover:bg-gold/5 hover:shadow-sm\"),\n                                    onClick: ()=>onChange(toggleArrayFilter(values, option)),\n                                    children: values.includes(option) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-3 h-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 43\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm text-warm-gray cursor-pointer flex-1 group-hover:text-soft-brown transition-colors\",\n                                    onClick: ()=>onChange(toggleArrayFilter(values, option)),\n                                    children: option\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, option, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n            lineNumber: 358,\n            columnNumber: 5\n        }, this);\n    };\n    const formatPrice = (value)=>{\n        if (filters.priceUnit === \"sqft\") {\n            return \"\".concat(value.toLocaleString());\n        } else {\n            if (value >= 1000000) {\n                return \"\".concat((value / 1000000).toFixed(1), \"M\");\n            } else if (value >= 1000) {\n                return \"\".concat((value / 1000).toFixed(0), \"K\");\n            }\n            return value.toLocaleString();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-ivory\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-beige shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container py-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: onBack,\n                                                className: \"text-warm-gray hover:text-gold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Home\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 text-warm-gray\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-soft-brown\",\n                                                children: \"Properties\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, this),\n                                            selectedDeveloper && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4 text-warm-gray\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gold\",\n                                                        children: selectedDeveloper.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-soft-brown text-[48px] leading-[1.2] py-2\",\n                                        children: selectedDeveloper ? \"\".concat(selectedDeveloper.name) : \"All Developments\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-warm-gray mt-2\",\n                                        children: [\n                                            sortedProperties.length,\n                                            \" properties found\",\n                                            hasActiveFilters && \" (\".concat(activeFilterCount, \" filters applied)\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 max-w-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                placeholder: \"Search properties, locations, developers...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10 bg-white border-beige focus:border-gold rounded-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                                            open: isFilterModalOpen,\n                                            onOpenChange: setIsFilterModalOpen,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"border-soft-brown/30 text-soft-brown hover:bg-soft-brown hover:text-white rounded-xl relative \".concat(hasActiveFilters ? \"bg-gold text-charcoal border-gold\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Filters\",\n                                                            activeFilterCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"ml-2 bg-soft-brown text-white text-xs min-w-[20px] h-5\",\n                                                                children: activeFilterCount\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                                                    className: \"sm:max-w-6xl max-h-[90vh] bg-white flex flex-col overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                                            className: \"flex-shrink-0 pb-6 border-b border-beige\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                                                                className: \"text-2xl text-soft-brown\",\n                                                                                children: \"Advanced Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 485,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogDescription, {\n                                                                                className: \"text-warm-gray mt-2\",\n                                                                                children: \"Refine your property search using the filters below to find properties that match your specific requirements.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 488,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 484,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: resetFilters,\n                                                                                disabled: !hasActiveFilters,\n                                                                                className: \"border-soft-brown/30 transition-all duration-200 \".concat(hasActiveFilters ? \"text-gold border-gold/30 hover:bg-gold/10 hover:border-gold\" : \"text-warm-gray border-beige hover:bg-beige/50\"),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"w-4 h-4 mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 505,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    \"Reset\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 494,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                onClick: ()=>setIsFilterModalOpen(false),\n                                                                                className: \"bg-gold hover:bg-gold/90 text-charcoal\",\n                                                                                children: \"Apply Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 508,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 493,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 overflow-y-auto py-8 space-y-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                    className: \"border-gold/20 bg-gradient-to-r from-light-gold/10 to-beige/30\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                                                className: \"flex items-center space-x-2 text-soft-brown text-xl\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"w-6 h-6 text-gold\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 524,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Price Configuration\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 525,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 523,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 522,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                            className: \"space-y-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between p-4 bg-white rounded-xl border border-beige/50\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                                    className: \"w-5 h-5 text-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 532,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-soft-brown font-medium\",\n                                                                                                            children: \"Price Display Mode\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 534,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"text-xs text-warm-gray mt-1\",\n                                                                                                            children: \"Choose how prices are displayed\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 537,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 533,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 531,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                    className: \"text-sm transition-colors \".concat(filters.priceUnit === \"total\" ? \"text-soft-brown\" : \"text-warm-gray\"),\n                                                                                                    children: \"Total Price\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 543,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_switch__WEBPACK_IMPORTED_MODULE_12__.Switch, {\n                                                                                                    checked: filters.priceUnit === \"sqft\",\n                                                                                                    onCheckedChange: (checked)=>updateFilters(\"priceUnit\", checked ? \"sqft\" : \"total\"),\n                                                                                                    className: \"data-[state=checked]:bg-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 552,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                    className: \"text-sm transition-colors \".concat(filters.priceUnit === \"sqft\" ? \"text-soft-brown\" : \"text-warm-gray\"),\n                                                                                                    children: \"Per Sq Ft\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 562,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 542,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 530,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                                    className: \"w-5 h-5 text-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 577,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                    className: \"text-soft-brown font-medium text-lg\",\n                                                                                                    children: [\n                                                                                                        \"Price Range (\",\n                                                                                                        filters.priceUnit === \"sqft\" ? \"AED per Sq Ft\" : \"AED\",\n                                                                                                        \")\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 578,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 576,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-2 gap-4\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Minimum\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 589,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"relative\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray text-sm\",\n                                                                                                                    children: \"AED\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 593,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                                    type: \"number\",\n                                                                                                                    value: filters.minPrice,\n                                                                                                                    onChange: (e)=>updateFilters(\"minPrice\", parseInt(e.target.value) || 0),\n                                                                                                                    className: \"pl-12 border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                                    placeholder: \"0\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 596,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 592,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 588,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Maximum\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 611,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"relative\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray text-sm\",\n                                                                                                                    children: \"AED\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 615,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                                    type: \"number\",\n                                                                                                                    value: filters.maxPrice,\n                                                                                                                    onChange: (e)=>updateFilters(\"maxPrice\", parseInt(e.target.value) || ********),\n                                                                                                                    className: \"pl-12 border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                                    placeholder: \"10,000,000\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 618,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 614,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 610,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 587,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-3 p-4 bg-beige/30 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex justify-between text-sm text-warm-gray\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"AED \",\n                                                                                                                formatPrice(filters.minPrice)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 636,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"AED \",\n                                                                                                                formatPrice(filters.maxPrice)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 637,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 635,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_slider__WEBPACK_IMPORTED_MODULE_11__.Slider, {\n                                                                                                    value: [\n                                                                                                        filters.minPrice,\n                                                                                                        filters.maxPrice\n                                                                                                    ],\n                                                                                                    onValueChange: (param)=>{\n                                                                                                        let [min, max] = param;\n                                                                                                        updateFilters(\"minPrice\", min);\n                                                                                                        updateFilters(\"maxPrice\", max);\n                                                                                                    },\n                                                                                                    max: filters.priceUnit === \"sqft\" ? 5000 : 20000000,\n                                                                                                    min: 0,\n                                                                                                    step: filters.priceUnit === \"sqft\" ? 50 : 50000,\n                                                                                                    className: \"w-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 639,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 634,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 575,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 528,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                            className: \"border-beige/60\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                                                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                                className: \"w-5 h-5 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 663,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Area Range\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 664,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 662,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 661,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                                    className: \"space-y-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-2 gap-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Min Sq Ft\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 670,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                            type: \"number\",\n                                                                                                            value: filters.minArea,\n                                                                                                            onChange: (e)=>updateFilters(\"minArea\", parseInt(e.target.value) || 0),\n                                                                                                            className: \"border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                            placeholder: \"0\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 673,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 669,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Max Sq Ft\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 687,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                            type: \"number\",\n                                                                                                            value: filters.maxArea,\n                                                                                                            onChange: (e)=>updateFilters(\"maxArea\", parseInt(e.target.value) || 5000),\n                                                                                                            className: \"border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                            placeholder: \"5,000\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 690,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 686,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 668,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-2 p-3 bg-beige/20 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex justify-between text-sm text-warm-gray\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                filters.minArea.toLocaleString(),\n                                                                                                                \" sq ft\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 707,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                filters.maxArea.toLocaleString(),\n                                                                                                                \" sq ft\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 710,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 706,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_slider__WEBPACK_IMPORTED_MODULE_11__.Slider, {\n                                                                                                    value: [\n                                                                                                        filters.minArea,\n                                                                                                        filters.maxArea\n                                                                                                    ],\n                                                                                                    onValueChange: (param)=>{\n                                                                                                        let [min, max] = param;\n                                                                                                        updateFilters(\"minArea\", min);\n                                                                                                        updateFilters(\"maxArea\", max);\n                                                                                                    },\n                                                                                                    max: 8000,\n                                                                                                    min: 0,\n                                                                                                    step: 50,\n                                                                                                    className: \"w-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 714,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 705,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 667,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 660,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                            className: \"border-beige/60\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                                                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                                className: \"w-5 h-5 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 733,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Project Completion\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 734,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 732,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 731,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                children: \"Completion Timeframe\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 739,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                                                                value: filters.completionDate,\n                                                                                                onValueChange: (value)=>updateFilters(\"completionDate\", value),\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                                                        className: \"w-full border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                                                            placeholder: \"Select completion timeframe\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 749,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                        lineNumber: 748,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                                                        children: completionDateOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                                                                value: option.value,\n                                                                                                                children: option.label\n                                                                                                            }, option.value, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                lineNumber: 753,\n                                                                                                                columnNumber: 35\n                                                                                                            }, this))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                        lineNumber: 751,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 742,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 738,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 737,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 730,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mb-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"w-6 h-6 text-gold\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 770,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-xl text-soft-brown\",\n                                                                                    children: \"Property Characteristics\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 771,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 769,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Development Status\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                                                                                    options: developmentStatusOptions,\n                                                                                    values: filters.developmentStatus,\n                                                                                    onChange: (values)=>updateFilters(\"developmentStatus\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 778,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Unit Type\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                                                                                    options: unitTypeOptions,\n                                                                                    values: filters.unitTypes,\n                                                                                    onChange: (values)=>updateFilters(\"unitTypes\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 789,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Bedrooms\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                                                                                    options: bedroomOptions,\n                                                                                    values: filters.bedrooms,\n                                                                                    onChange: (values)=>updateFilters(\"bedrooms\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 800,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Sales Status\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                                                                    options: salesStatusOptions,\n                                                                                    values: filters.salesStatus,\n                                                                                    onChange: (values)=>updateFilters(\"salesStatus\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 811,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 776,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                    className: \"border-gold/30 bg-gradient-to-r from-gold/5 to-light-gold/10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                        className: \"pt-6\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"text-lg text-soft-brown mb-2\",\n                                                                                            children: \"Filter Summary\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 829,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-warm-gray\",\n                                                                                            children: [\n                                                                                                activeFilterCount,\n                                                                                                \" filters applied •\",\n                                                                                                \" \",\n                                                                                                sortedProperties.length,\n                                                                                                \" properties match your criteria\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 832,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 828,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    onClick: resetFilters,\n                                                                                    className: \"border-gold text-gold hover:bg-gold hover:text-charcoal\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 843,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Reset All\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 838,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 827,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 826,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                            value: sortBy,\n                                            onValueChange: setSortBy,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                    className: \"w-40 border-soft-brown/30 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                        placeholder: \"Sort by\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 856,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"featured\",\n                                                            children: \"Featured\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 860,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"price-low\",\n                                                            children: \"Price: Low to High\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 861,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"price-high\",\n                                                            children: \"Price: High to Low\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 862,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"completion\",\n                                                            children: \"Completion Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 863,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"location\",\n                                                            children: \"Location\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 859,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 855,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-soft-brown/30 rounded-xl overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: viewMode === \"grid\" ? \"default\" : \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setViewMode(\"grid\"),\n                                                    className: \"rounded-none \".concat(viewMode === \"grid\" ? \"bg-soft-brown text-white\" : \"text-soft-brown hover:bg-soft-brown/10\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 870,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: viewMode === \"list\" ? \"default\" : \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setViewMode(\"list\"),\n                                                    className: \"rounded-none \".concat(viewMode === \"list\" ? \"bg-soft-brown text-white\" : \"text-soft-brown hover:bg-soft-brown/10\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 882,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 869,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 11\n                        }, this),\n                        hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex flex-wrap items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-warm-gray mr-2\",\n                                    children: \"Active filters:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 15\n                                }, this),\n                                filters.priceUnit !== \"total\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        \"Price per Sq Ft\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>updateFilters(\"priceUnit\", \"total\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 910,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 17\n                                }, this),\n                                (filters.minArea !== 0 || filters.maxArea !== 5000) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        \"Area: \",\n                                        filters.minArea,\n                                        \"-\",\n                                        filters.maxArea,\n                                        \" sq ft\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>{\n                                                updateFilters(\"minArea\", 0);\n                                                updateFilters(\"maxArea\", 5000);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 922,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 917,\n                                    columnNumber: 17\n                                }, this),\n                                filters.developmentStatus.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            status,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"developmentStatus\", filters.developmentStatus.filter((s)=>s !== status))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 938,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, status, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.unitTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            type,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"unitTypes\", filters.unitTypes.filter((t)=>t !== type))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 956,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, type, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 950,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.bedrooms.map((bedroom)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            bedroom,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"bedrooms\", filters.bedrooms.filter((b)=>b !== bedroom))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 974,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, bedroom, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 968,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.salesStatus.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            status,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"salesStatus\", filters.salesStatus.filter((s)=>s !== status))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 992,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, status, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 986,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.completionDate !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        (_completionDateOptions_find = completionDateOptions.find((opt)=>opt.value === filters.completionDate)) === null || _completionDateOptions_find === void 0 ? void 0 : _completionDateOptions_find.label,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>updateFilters(\"completionDate\", \"all\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 1013,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 1004,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: resetFilters,\n                                    className: \"text-gold hover:bg-gold/10 text-xs\",\n                                    children: \"Clear all\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 1019,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 900,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 407,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                lineNumber: 406,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container py-8\",\n                children: [\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: Array.from({\n                            length: 6\n                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_2__.PropertyCardSkeleton, {}, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1038,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 1036,\n                        columnNumber: 11\n                    }, this),\n                    error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_empty_state__WEBPACK_IMPORTED_MODULE_3__.ErrorState, {\n                        onRetry: ()=>fetchProperties(),\n                        message: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 1045,\n                        columnNumber: 11\n                    }, this),\n                    !loading && !error && sortedProperties.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                className: \"w-16 h-16 text-warm-gray mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1051,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl text-soft-brown mb-2\",\n                                children: \"No properties found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1052,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-warm-gray mb-6\",\n                                children: \"Try adjusting your filters or search criteria to find more properties.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1055,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: resetFilters,\n                                className: \"bg-gold hover:bg-gold/90 text-charcoal\",\n                                children: \"Reset Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1059,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 1050,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            viewMode === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: sortedProperties.map((property)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        className: \"group cursor-pointer border border-beige hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden rounded-xl\",\n                                        onClick: ()=>onProjectSelect(property),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative aspect-[4/3] overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_13__.ImageWithFallback, {\n                                                        src: property.cover_image_url ? JSON.parse(property.cover_image_url).url : \"/placeholder-property.jpg\",\n                                                        alt: property.name || \"Property\",\n                                                        className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1078,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    property.is_partner_project && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: \"absolute top-4 left-4 bg-gold text-charcoal\",\n                                                        children: \"Featured\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1088,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: \"absolute top-4 right-4 bg-black/70 text-white border-0\",\n                                                        children: property.status || \"Available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1092,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1077,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl text-soft-brown group-hover:text-gold transition-colors\",\n                                                                    children: property.name || \"Property Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1099,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-warm-gray mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1103,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: property.area || \"Location\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1104,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1102,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1098,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl text-gold\",\n                                                                    children: property.min_price ? \"\".concat(property.price_currency || \"AED\", \" \").concat(property.min_price.toLocaleString()) : \"Price on Request\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1111,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs border-gold/30 text-gold\",\n                                                                    children: property.sale_status || \"Available\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1118,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1110,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-4 text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            className: \"w-4 h-4 mx-auto text-gold\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1128,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-warm-gray\",\n                                                                            children: [\n                                                                                property.bedrooms || 0,\n                                                                                \" BR\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1129,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1127,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                            className: \"w-4 h-4 mx-auto text-gold\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1134,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-warm-gray\",\n                                                                            children: [\n                                                                                property.bathrooms || 0,\n                                                                                \" Bath\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1135,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1133,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                            className: \"w-4 h-4 mx-auto text-gold\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1140,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-warm-gray\",\n                                                                            children: property.area_unit || \"sqft\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1141,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1139,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1126,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between pt-4 border-t border-beige\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-warm-gray\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1149,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        property.completionDate || \"TBD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1148,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-warm-gray\",\n                                                                    children: property.developer || \"Developer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1152,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1147,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 1097,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1096,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, property.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 1072,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1070,\n                                columnNumber: 15\n                            }, this),\n                            viewMode === \"list\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: sortedProperties.map((property)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        className: \"group cursor-pointer border border-beige hover:shadow-lg transition-all duration-300 overflow-hidden rounded-xl\",\n                                        onClick: ()=>onProjectSelect(property),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                            className: \"p-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-full md:w-80 aspect-[4/3] md:aspect-auto md:h-48 overflow-hidden\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_13__.ImageWithFallback, {\n                                                                src: property.cover_image_url ? JSON.parse(property.cover_image_url).url : \"/placeholder-property.jpg\",\n                                                                alt: property.name || \"Property\",\n                                                                className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1175,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            property.is_partner_project && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"absolute top-4 left-4 bg-gold text-charcoal\",\n                                                                children: \"Featured\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1185,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1174,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"text-xl text-soft-brown group-hover:text-gold transition-colors mb-2\",\n                                                                                children: property.name || \"Property Title\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1193,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center text-warm-gray mb-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                                        className: \"w-4 h-4 mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1197,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: property.area || \"Location\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1198,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mx-2\",\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1199,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: property.developer || \"Developer\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1200,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1196,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-4 text-sm text-warm-gray\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                                className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 1204,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            property.bedrooms || 0,\n                                                                                            \" BR\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1203,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                                className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 1208,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            property.bathrooms || 0,\n                                                                                            \" Bath\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1207,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                                className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 1212,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            property.area_unit || \"sqft\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1211,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1202,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1192,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-2xl text-gold mb-2\",\n                                                                                children: property.min_price ? \"\".concat(property.price_currency || \"AED\", \" \").concat(property.min_price.toLocaleString()) : \"Price on Request\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1218,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                className: \"border-gold/30 text-gold\",\n                                                                                children: property.sale_status || \"Available\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1225,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1217,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1191,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between pt-4 border-t border-beige\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-6 text-sm text-warm-gray\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 1236,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                property.completionDate || \"TBD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1235,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: \"bg-beige text-soft-brown\",\n                                                                            children: property.status || \"Available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1239,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: property.developer || \"Developer\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1242,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1234,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1233,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1190,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1173,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 1172,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, property.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 1167,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1165,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                lineNumber: 1033,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n        lineNumber: 404,\n        columnNumber: 5\n    }, this);\n}\n_s(AllPropertiesPage, \"RLWQKBGSbsSD5mMFmqXAAW0BhnI=\");\n_c = AllPropertiesPage;\nvar _c;\n$RefreshReg$(_c, \"AllPropertiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AllPropertiesPage.tsx\n"));

/***/ })

});