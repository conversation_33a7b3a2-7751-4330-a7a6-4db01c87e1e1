"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AllPropertiesPage.tsx":
/*!**********************************************!*\
  !*** ./src/components/AllPropertiesPage.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AllPropertiesPage: function() { return /* binding */ AllPropertiesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_skeleton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _ui_empty_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/empty-state */ \"(app-pages-browser)/./src/components/ui/empty-state.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _ui_slider__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ui/slider */ \"(app-pages-browser)/./src/components/ui/slider.tsx\");\n/* harmony import */ var _ui_switch__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ruler.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hammer.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bed.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bath.js\");\n/* harmony import */ var _figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./figma/ImageWithFallback */ \"(app-pages-browser)/./src/components/figma/ImageWithFallback.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst initialFilters = {\n    priceUnit: \"total\",\n    minArea: 0,\n    maxArea: 5000,\n    developmentStatus: [],\n    unitTypes: [],\n    bedrooms: [],\n    salesStatus: [],\n    completionDate: \"all\",\n    minPrice: 0,\n    maxPrice: ********\n};\nfunction AllPropertiesPage(param) {\n    let { onProjectSelect, onBack, selectedDeveloper } = param;\n    var _completionDateOptions_find;\n    _s();\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"featured\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filtersOpen, setFiltersOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFilterModalOpen, setIsFilterModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFilters);\n    // Local state for properties\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch properties function\n    const fetchProperties = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            console.log(\"\\uD83D\\uDE80 Fetching properties from API...\");\n            const response = await fetch(\"/api/properties\");\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            console.log(\"✅ Properties fetched successfully:\", data);\n            console.log(\"\\uD83D\\uDCCA Data type:\", typeof data);\n            console.log(\"\\uD83D\\uDCCA Data structure:\", Object.keys(data));\n            // Handle the API response structure\n            let propertiesArray = [];\n            if (data.success && data.data) {\n                // Our API wraps the external response in { success: true, data: externalData }\n                const externalData = data.data;\n                if (externalData.items && Array.isArray(externalData.items)) {\n                    // The external API returns { items: [...], pagination: {...} }\n                    propertiesArray = externalData.items;\n                } else if (Array.isArray(externalData)) {\n                    // Fallback: if external data is directly an array\n                    propertiesArray = externalData;\n                }\n            } else if (Array.isArray(data)) {\n                // If data is directly an array\n                propertiesArray = data;\n            } else if (data.data && Array.isArray(data.data)) {\n                // If data is wrapped in a data property\n                propertiesArray = data.data;\n            } else {\n                // Fallback: try to find any array in the response\n                const possibleArrays = Object.values(data).filter(Array.isArray);\n                if (possibleArrays.length > 0) {\n                    propertiesArray = possibleArrays[0];\n                }\n            }\n            console.log(\"\\uD83D\\uDCCB Properties array:\", propertiesArray);\n            console.log(\"\\uD83D\\uDCCA Properties count:\", propertiesArray.length);\n            setProperties(propertiesArray);\n        } catch (err) {\n            console.error(\"❌ Error fetching properties:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch properties\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Fetch properties on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProperties();\n    }, []);\n    // Use real properties data - ensure it's always an array\n    const allProperties = Array.isArray(properties) ? properties : [];\n    // Filter options\n    const developmentStatusOptions = [\n        \"Presale\",\n        \"Under Construction\",\n        \"Completed\"\n    ];\n    const unitTypeOptions = [\n        \"Apartments\",\n        \"Villa\",\n        \"Townhouse\",\n        \"Duplex\",\n        \"Penthouse\"\n    ];\n    const bedroomOptions = [\n        \"Studio\",\n        \"1 BR\",\n        \"2 BR\",\n        \"3 BR\",\n        \"4 BR\",\n        \"5+ BR\"\n    ];\n    const salesStatusOptions = [\n        \"Announced\",\n        \"Presale (EOI)\",\n        \"Start of Sales\",\n        \"On Sale\",\n        \"Out of Stock\"\n    ];\n    const completionDateOptions = [\n        {\n            value: \"all\",\n            label: \"All Projects\"\n        },\n        {\n            value: \"12months\",\n            label: \"Completing in 12 months\"\n        },\n        {\n            value: \"2years\",\n            label: \"Completing in 2 years\"\n        },\n        {\n            value: \"3years\",\n            label: \"Completing in 3 years\"\n        },\n        {\n            value: \"4years\",\n            label: \"Completing in 4 years\"\n        },\n        {\n            value: \"5years\",\n            label: \"Completing in 5+ years\"\n        }\n    ];\n    // Helper functions for filters\n    const toggleArrayFilter = (array, value)=>{\n        return array.includes(value) ? array.filter((item)=>item !== value) : [\n            ...array,\n            value\n        ];\n    };\n    const updateFilters = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    const resetFilters = ()=>{\n        setFilters(initialFilters);\n    };\n    const hasActiveFilters = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return filters.priceUnit !== \"total\" || filters.minArea !== 0 || filters.maxArea !== 5000 || filters.developmentStatus.length > 0 || filters.unitTypes.length > 0 || filters.bedrooms.length > 0 || filters.salesStatus.length > 0 || filters.completionDate !== \"all\" || filters.minPrice !== 0 || filters.maxPrice !== ********;\n    }, [\n        filters\n    ]);\n    const activeFilterCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        let count = 0;\n        if (filters.priceUnit !== \"total\") count++;\n        if (filters.minArea !== 0 || filters.maxArea !== 5000) count++;\n        if (filters.developmentStatus.length > 0) count++;\n        if (filters.unitTypes.length > 0) count++;\n        if (filters.bedrooms.length > 0) count++;\n        if (filters.salesStatus.length > 0) count++;\n        if (filters.completionDate !== \"all\") count++;\n        if (filters.minPrice !== 0 || filters.maxPrice !== ********) count++;\n        return count;\n    }, [\n        filters\n    ]);\n    // Apply filters to properties\n    const filteredProperties = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // Ensure allProperties is an array before filtering\n        if (!Array.isArray(allProperties)) {\n            console.warn(\"⚠️ allProperties is not an array:\", allProperties);\n            return [];\n        }\n        let filtered = [\n            ...allProperties\n        ];\n        // Apply search query\n        if (searchQuery) {\n            filtered = filtered.filter((property)=>{\n                var _property_name, _property_area, _property_developer;\n                return ((_property_name = property.name) === null || _property_name === void 0 ? void 0 : _property_name.toLowerCase().includes(searchQuery.toLowerCase())) || ((_property_area = property.area) === null || _property_area === void 0 ? void 0 : _property_area.toLowerCase().includes(searchQuery.toLowerCase())) || ((_property_developer = property.developer) === null || _property_developer === void 0 ? void 0 : _property_developer.toLowerCase().includes(searchQuery.toLowerCase()));\n            });\n        }\n        // Apply developer filter if specified\n        if (selectedDeveloper) {\n            filtered = filtered.filter((property)=>{\n                var _property_developer;\n                return ((_property_developer = property.developer) === null || _property_developer === void 0 ? void 0 : _property_developer.name) === selectedDeveloper.name;\n            });\n        }\n        // Apply area filters\n        filtered = filtered.filter((property)=>property.area >= filters.minArea && property.area <= filters.maxArea);\n        // Apply price filters\n        const getPrice = (property)=>{\n            const price = property.price || 0;\n            return filters.priceUnit === \"sqft\" ? price / property.area : price;\n        };\n        filtered = filtered.filter((property)=>{\n            const price = getPrice(property);\n            return price >= filters.minPrice && price <= filters.maxPrice;\n        });\n        // Apply development status filter\n        if (filters.developmentStatus.length > 0) {\n            filtered = filtered.filter((property)=>filters.developmentStatus.includes(property.status));\n        }\n        // Apply unit type filter\n        if (filters.unitTypes.length > 0) {\n            filtered = filtered.filter((property)=>filters.unitTypes.includes(property.propertyType));\n        }\n        // Apply bedroom filter\n        if (filters.bedrooms.length > 0) {\n            filtered = filtered.filter((property)=>{\n                const bedrooms = \"\".concat(property.bedrooms, \" BR\");\n                return filters.bedrooms.includes(bedrooms);\n            });\n        }\n        return filtered;\n    }, [\n        allProperties,\n        searchQuery,\n        selectedDeveloper,\n        filters\n    ]);\n    const sortedProperties = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const sorted = [\n            ...filteredProperties\n        ];\n        switch(sortBy){\n            case \"price-low\":\n                return sorted.sort((a, b)=>(a.price || 0) - (b.price || 0));\n            case \"price-high\":\n                return sorted.sort((a, b)=>(b.price || 0) - (a.price || 0));\n            case \"completion\":\n                return sorted.sort((a, b)=>(a.completionDate || \"\").localeCompare(b.completionDate || \"\"));\n            case \"location\":\n                return sorted.sort((a, b)=>{\n                    var _a_location, _b_location;\n                    return (((_a_location = a.location) === null || _a_location === void 0 ? void 0 : _a_location.name) || \"\").localeCompare(((_b_location = b.location) === null || _b_location === void 0 ? void 0 : _b_location.name) || \"\");\n                });\n            case \"featured\":\n            default:\n                return sorted.sort((a, b)=>(b.isFeatured ? 1 : 0) - (a.isFeatured ? 1 : 0));\n        }\n    }, [\n        filteredProperties,\n        sortBy\n    ]);\n    const LuxuryCheckboxGroup = (param)=>{\n        let { options, values, onChange, label, icon: Icon } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n            className: \"border-beige/60 shadow-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                    className: \"pb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                className: \"w-5 h-5 text-gold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 359,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                    className: \"space-y-3\",\n                    children: options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 h-5 rounded-md border-2 flex items-center justify-center cursor-pointer transition-all duration-200 \".concat(values.includes(option) ? \"bg-gold border-gold text-charcoal shadow-sm\" : \"border-soft-brown/30 hover:border-gold hover:bg-gold/5 hover:shadow-sm\"),\n                                    onClick: ()=>onChange(toggleArrayFilter(values, option)),\n                                    children: values.includes(option) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-3 h-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 43\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm text-warm-gray cursor-pointer flex-1 group-hover:text-soft-brown transition-colors\",\n                                    onClick: ()=>onChange(toggleArrayFilter(values, option)),\n                                    children: option\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, option, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n            lineNumber: 358,\n            columnNumber: 5\n        }, this);\n    };\n    const formatPrice = (value)=>{\n        if (filters.priceUnit === \"sqft\") {\n            return \"\".concat(value.toLocaleString());\n        } else {\n            if (value >= 1000000) {\n                return \"\".concat((value / 1000000).toFixed(1), \"M\");\n            } else if (value >= 1000) {\n                return \"\".concat((value / 1000).toFixed(0), \"K\");\n            }\n            return value.toLocaleString();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-ivory\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-beige shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container py-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: onBack,\n                                                className: \"text-warm-gray hover:text-gold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Home\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 text-warm-gray\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-soft-brown\",\n                                                children: \"Properties\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, this),\n                                            selectedDeveloper && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4 text-warm-gray\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gold\",\n                                                        children: selectedDeveloper.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-soft-brown text-[48px] leading-[1.2] py-2\",\n                                        children: selectedDeveloper ? \"\".concat(selectedDeveloper.name) : \"All Developments\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-warm-gray mt-2\",\n                                        children: [\n                                            sortedProperties.length,\n                                            \" properties found\",\n                                            hasActiveFilters && \" (\".concat(activeFilterCount, \" filters applied)\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 max-w-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                placeholder: \"Search properties, locations, developers...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10 bg-white border-beige focus:border-gold rounded-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                                            open: isFilterModalOpen,\n                                            onOpenChange: setIsFilterModalOpen,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"border-soft-brown/30 text-soft-brown hover:bg-soft-brown hover:text-white rounded-xl relative \".concat(hasActiveFilters ? \"bg-gold text-charcoal border-gold\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Filters\",\n                                                            activeFilterCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"ml-2 bg-soft-brown text-white text-xs min-w-[20px] h-5\",\n                                                                children: activeFilterCount\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                                                    className: \"sm:max-w-6xl max-h-[90vh] bg-white flex flex-col overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                                            className: \"flex-shrink-0 pb-6 border-b border-beige\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                                                                className: \"text-2xl text-soft-brown\",\n                                                                                children: \"Advanced Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 485,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogDescription, {\n                                                                                className: \"text-warm-gray mt-2\",\n                                                                                children: \"Refine your property search using the filters below to find properties that match your specific requirements.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 488,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 484,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: resetFilters,\n                                                                                disabled: !hasActiveFilters,\n                                                                                className: \"border-soft-brown/30 transition-all duration-200 \".concat(hasActiveFilters ? \"text-gold border-gold/30 hover:bg-gold/10 hover:border-gold\" : \"text-warm-gray border-beige hover:bg-beige/50\"),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"w-4 h-4 mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 505,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    \"Reset\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 494,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                onClick: ()=>setIsFilterModalOpen(false),\n                                                                                className: \"bg-gold hover:bg-gold/90 text-charcoal\",\n                                                                                children: \"Apply Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 508,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 493,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 overflow-y-auto py-8 space-y-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                    className: \"border-gold/20 bg-gradient-to-r from-light-gold/10 to-beige/30\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                                                className: \"flex items-center space-x-2 text-soft-brown text-xl\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"w-6 h-6 text-gold\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 524,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Price Configuration\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 525,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 523,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 522,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                            className: \"space-y-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between p-4 bg-white rounded-xl border border-beige/50\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                                    className: \"w-5 h-5 text-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 532,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-soft-brown font-medium\",\n                                                                                                            children: \"Price Display Mode\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 534,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"text-xs text-warm-gray mt-1\",\n                                                                                                            children: \"Choose how prices are displayed\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 537,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 533,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 531,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                    className: \"text-sm transition-colors \".concat(filters.priceUnit === \"total\" ? \"text-soft-brown\" : \"text-warm-gray\"),\n                                                                                                    children: \"Total Price\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 543,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_switch__WEBPACK_IMPORTED_MODULE_12__.Switch, {\n                                                                                                    checked: filters.priceUnit === \"sqft\",\n                                                                                                    onCheckedChange: (checked)=>updateFilters(\"priceUnit\", checked ? \"sqft\" : \"total\"),\n                                                                                                    className: \"data-[state=checked]:bg-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 552,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                    className: \"text-sm transition-colors \".concat(filters.priceUnit === \"sqft\" ? \"text-soft-brown\" : \"text-warm-gray\"),\n                                                                                                    children: \"Per Sq Ft\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 562,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 542,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 530,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                                    className: \"w-5 h-5 text-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 577,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                    className: \"text-soft-brown font-medium text-lg\",\n                                                                                                    children: [\n                                                                                                        \"Price Range (\",\n                                                                                                        filters.priceUnit === \"sqft\" ? \"AED per Sq Ft\" : \"AED\",\n                                                                                                        \")\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 578,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 576,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-2 gap-4\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Minimum\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 589,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"relative\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray text-sm\",\n                                                                                                                    children: \"AED\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 593,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                                    type: \"number\",\n                                                                                                                    value: filters.minPrice,\n                                                                                                                    onChange: (e)=>updateFilters(\"minPrice\", parseInt(e.target.value) || 0),\n                                                                                                                    className: \"pl-12 border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                                    placeholder: \"0\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 596,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 592,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 588,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Maximum\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 611,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"relative\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray text-sm\",\n                                                                                                                    children: \"AED\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 615,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                                    type: \"number\",\n                                                                                                                    value: filters.maxPrice,\n                                                                                                                    onChange: (e)=>updateFilters(\"maxPrice\", parseInt(e.target.value) || ********),\n                                                                                                                    className: \"pl-12 border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                                    placeholder: \"10,000,000\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 618,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 614,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 610,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 587,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-3 p-4 bg-beige/30 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex justify-between text-sm text-warm-gray\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"AED \",\n                                                                                                                formatPrice(filters.minPrice)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 636,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"AED \",\n                                                                                                                formatPrice(filters.maxPrice)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 637,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 635,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_slider__WEBPACK_IMPORTED_MODULE_11__.Slider, {\n                                                                                                    value: [\n                                                                                                        filters.minPrice,\n                                                                                                        filters.maxPrice\n                                                                                                    ],\n                                                                                                    onValueChange: (param)=>{\n                                                                                                        let [min, max] = param;\n                                                                                                        updateFilters(\"minPrice\", min);\n                                                                                                        updateFilters(\"maxPrice\", max);\n                                                                                                    },\n                                                                                                    max: filters.priceUnit === \"sqft\" ? 5000 : 20000000,\n                                                                                                    min: 0,\n                                                                                                    step: filters.priceUnit === \"sqft\" ? 50 : 50000,\n                                                                                                    className: \"w-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 639,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 634,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 575,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 528,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                            className: \"border-beige/60\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                                                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                                className: \"w-5 h-5 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 663,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Area Range\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 664,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 662,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 661,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                                    className: \"space-y-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-2 gap-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Min Sq Ft\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 670,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                            type: \"number\",\n                                                                                                            value: filters.minArea,\n                                                                                                            onChange: (e)=>updateFilters(\"minArea\", parseInt(e.target.value) || 0),\n                                                                                                            className: \"border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                            placeholder: \"0\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 673,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 669,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Max Sq Ft\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 687,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                            type: \"number\",\n                                                                                                            value: filters.maxArea,\n                                                                                                            onChange: (e)=>updateFilters(\"maxArea\", parseInt(e.target.value) || 5000),\n                                                                                                            className: \"border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                            placeholder: \"5,000\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 690,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 686,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 668,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-2 p-3 bg-beige/20 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex justify-between text-sm text-warm-gray\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                filters.minArea.toLocaleString(),\n                                                                                                                \" sq ft\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 707,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                filters.maxArea.toLocaleString(),\n                                                                                                                \" sq ft\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 710,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 706,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_slider__WEBPACK_IMPORTED_MODULE_11__.Slider, {\n                                                                                                    value: [\n                                                                                                        filters.minArea,\n                                                                                                        filters.maxArea\n                                                                                                    ],\n                                                                                                    onValueChange: (param)=>{\n                                                                                                        let [min, max] = param;\n                                                                                                        updateFilters(\"minArea\", min);\n                                                                                                        updateFilters(\"maxArea\", max);\n                                                                                                    },\n                                                                                                    max: 8000,\n                                                                                                    min: 0,\n                                                                                                    step: 50,\n                                                                                                    className: \"w-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 714,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 705,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 667,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 660,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                            className: \"border-beige/60\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                                                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                                className: \"w-5 h-5 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 733,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Project Completion\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 734,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 732,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 731,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                children: \"Completion Timeframe\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 739,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                                                                value: filters.completionDate,\n                                                                                                onValueChange: (value)=>updateFilters(\"completionDate\", value),\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                                                        className: \"w-full border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                                                            placeholder: \"Select completion timeframe\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 749,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                        lineNumber: 748,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                                                        children: completionDateOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                                                                value: option.value,\n                                                                                                                children: option.label\n                                                                                                            }, option.value, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                lineNumber: 753,\n                                                                                                                columnNumber: 35\n                                                                                                            }, this))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                        lineNumber: 751,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 742,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 738,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 737,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 730,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mb-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"w-6 h-6 text-gold\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 770,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-xl text-soft-brown\",\n                                                                                    children: \"Property Characteristics\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 771,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 769,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Development Status\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                                                                                    options: developmentStatusOptions,\n                                                                                    values: filters.developmentStatus,\n                                                                                    onChange: (values)=>updateFilters(\"developmentStatus\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 778,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Unit Type\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                                                                                    options: unitTypeOptions,\n                                                                                    values: filters.unitTypes,\n                                                                                    onChange: (values)=>updateFilters(\"unitTypes\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 789,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Bedrooms\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                                                                                    options: bedroomOptions,\n                                                                                    values: filters.bedrooms,\n                                                                                    onChange: (values)=>updateFilters(\"bedrooms\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 800,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Sales Status\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                                                                    options: salesStatusOptions,\n                                                                                    values: filters.salesStatus,\n                                                                                    onChange: (values)=>updateFilters(\"salesStatus\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 811,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 776,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                    className: \"border-gold/30 bg-gradient-to-r from-gold/5 to-light-gold/10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                        className: \"pt-6\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"text-lg text-soft-brown mb-2\",\n                                                                                            children: \"Filter Summary\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 829,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-warm-gray\",\n                                                                                            children: [\n                                                                                                activeFilterCount,\n                                                                                                \" filters applied •\",\n                                                                                                \" \",\n                                                                                                sortedProperties.length,\n                                                                                                \" properties match your criteria\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 832,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 828,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    onClick: resetFilters,\n                                                                                    className: \"border-gold text-gold hover:bg-gold hover:text-charcoal\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 843,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Reset All\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 838,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 827,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 826,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                            value: sortBy,\n                                            onValueChange: setSortBy,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                    className: \"w-40 border-soft-brown/30 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                        placeholder: \"Sort by\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 856,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"featured\",\n                                                            children: \"Featured\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 860,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"price-low\",\n                                                            children: \"Price: Low to High\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 861,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"price-high\",\n                                                            children: \"Price: High to Low\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 862,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"completion\",\n                                                            children: \"Completion Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 863,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"location\",\n                                                            children: \"Location\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 859,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 855,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-soft-brown/30 rounded-xl overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: viewMode === \"grid\" ? \"default\" : \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setViewMode(\"grid\"),\n                                                    className: \"rounded-none \".concat(viewMode === \"grid\" ? \"bg-soft-brown text-white\" : \"text-soft-brown hover:bg-soft-brown/10\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 870,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: viewMode === \"list\" ? \"default\" : \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setViewMode(\"list\"),\n                                                    className: \"rounded-none \".concat(viewMode === \"list\" ? \"bg-soft-brown text-white\" : \"text-soft-brown hover:bg-soft-brown/10\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 882,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 869,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 11\n                        }, this),\n                        hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex flex-wrap items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-warm-gray mr-2\",\n                                    children: \"Active filters:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 15\n                                }, this),\n                                filters.priceUnit !== \"total\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        \"Price per Sq Ft\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>updateFilters(\"priceUnit\", \"total\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 910,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 17\n                                }, this),\n                                (filters.minArea !== 0 || filters.maxArea !== 5000) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        \"Area: \",\n                                        filters.minArea,\n                                        \"-\",\n                                        filters.maxArea,\n                                        \" sq ft\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>{\n                                                updateFilters(\"minArea\", 0);\n                                                updateFilters(\"maxArea\", 5000);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 922,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 917,\n                                    columnNumber: 17\n                                }, this),\n                                filters.developmentStatus.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            status,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"developmentStatus\", filters.developmentStatus.filter((s)=>s !== status))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 938,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, status, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.unitTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            type,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"unitTypes\", filters.unitTypes.filter((t)=>t !== type))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 956,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, type, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 950,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.bedrooms.map((bedroom)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            bedroom,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"bedrooms\", filters.bedrooms.filter((b)=>b !== bedroom))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 974,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, bedroom, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 968,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.salesStatus.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            status,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"salesStatus\", filters.salesStatus.filter((s)=>s !== status))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 992,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, status, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 986,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.completionDate !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        (_completionDateOptions_find = completionDateOptions.find((opt)=>opt.value === filters.completionDate)) === null || _completionDateOptions_find === void 0 ? void 0 : _completionDateOptions_find.label,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>updateFilters(\"completionDate\", \"all\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 1013,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 1004,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: resetFilters,\n                                    className: \"text-gold hover:bg-gold/10 text-xs\",\n                                    children: \"Clear all\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 1019,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 900,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 407,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                lineNumber: 406,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container py-8\",\n                children: [\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: Array.from({\n                            length: 6\n                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_2__.PropertyCardSkeleton, {}, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1038,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 1036,\n                        columnNumber: 11\n                    }, this),\n                    error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_empty_state__WEBPACK_IMPORTED_MODULE_3__.ErrorState, {\n                        onRetry: ()=>fetchProperties(),\n                        message: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 1045,\n                        columnNumber: 11\n                    }, this),\n                    !loading && !error && sortedProperties.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                className: \"w-16 h-16 text-warm-gray mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1051,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl text-soft-brown mb-2\",\n                                children: \"No properties found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1052,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-warm-gray mb-6\",\n                                children: \"Try adjusting your filters or search criteria to find more properties.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1055,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: resetFilters,\n                                className: \"bg-gold hover:bg-gold/90 text-charcoal\",\n                                children: \"Reset Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1059,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 1050,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            viewMode === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: sortedProperties.map((property)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        className: \"group cursor-pointer border border-beige hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden rounded-xl\",\n                                        onClick: ()=>onProjectSelect(property),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative aspect-[4/3] overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_13__.ImageWithFallback, {\n                                                        src: property.cover_image_url ? (()=>{\n                                                            try {\n                                                                return JSON.parse(property.cover_image_url).url;\n                                                            } catch (e) {\n                                                                return \"/placeholder-property.jpg\";\n                                                            }\n                                                        })() : \"/placeholder-property.jpg\",\n                                                        alt: property.name || \"Property\",\n                                                        className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1078,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    property.is_partner_project && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: \"absolute top-4 left-4 bg-gold text-charcoal\",\n                                                        children: \"Featured\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1095,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: \"absolute top-4 right-4 bg-black/70 text-white border-0\",\n                                                        children: property.status || \"Available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1099,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1077,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl text-soft-brown group-hover:text-gold transition-colors\",\n                                                                    children: property.name || \"Property Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1106,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-warm-gray mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1110,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: property.area || \"Location\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1111,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1109,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1105,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl text-gold\",\n                                                                    children: property.min_price && property.max_price ? property.min_price === property.max_price ? \"\".concat(property.price_currency || \"AED\", \" \").concat(property.min_price.toLocaleString()) : \"\".concat(property.price_currency || \"AED\", \" \").concat(property.min_price.toLocaleString(), \" - \").concat(property.max_price.toLocaleString()) : property.min_price ? \"From \".concat(property.price_currency || \"AED\", \" \").concat(property.min_price.toLocaleString()) : property.max_price ? \"Up to \".concat(property.price_currency || \"AED\", \" \").concat(property.max_price.toLocaleString()) : \"Price on Request\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1118,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs border-gold/30 text-gold\",\n                                                                    children: property.sale_status || \"Available\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1137,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1117,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-4 text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                            className: \"w-4 h-4 mx-auto text-gold\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1147,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-warm-gray\",\n                                                                            children: property.area_unit || \"sqft\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1148,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1146,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                            className: \"w-4 h-4 mx-auto text-gold\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1153,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-warm-gray\",\n                                                                            children: property.status || \"Available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1154,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1152,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: \"w-4 h-4 mx-auto text-gold\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1159,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-warm-gray\",\n                                                                            children: property.has_escrow ? \"Escrow\" : \"Direct\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1160,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1158,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1145,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between pt-4 border-t border-beige\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-warm-gray\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1168,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        property.completion_datetime ? new Date(property.completion_datetime).toLocaleDateString() : property.post_handover ? \"Post Handover\" : \"TBD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1167,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-warm-gray\",\n                                                                    children: property.developer || \"Developer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1177,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1166,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 1104,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1103,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, property.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 1072,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1070,\n                                columnNumber: 15\n                            }, this),\n                            viewMode === \"list\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: sortedProperties.map((property)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        className: \"group cursor-pointer border border-beige hover:shadow-lg transition-all duration-300 overflow-hidden rounded-xl\",\n                                        onClick: ()=>onProjectSelect(property),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                            className: \"p-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-full md:w-80 aspect-[4/3] md:aspect-auto md:h-48 overflow-hidden\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_13__.ImageWithFallback, {\n                                                                src: property.cover_image_url ? JSON.parse(property.cover_image_url).url : \"/placeholder-property.jpg\",\n                                                                alt: property.name || \"Property\",\n                                                                className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1200,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            property.is_partner_project && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"absolute top-4 left-4 bg-gold text-charcoal\",\n                                                                children: \"Featured\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1210,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1199,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"text-xl text-soft-brown group-hover:text-gold transition-colors mb-2\",\n                                                                                children: property.name || \"Property Title\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1218,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center text-warm-gray mb-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                                        className: \"w-4 h-4 mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1222,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: property.area || \"Location\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1223,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mx-2\",\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1224,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: property.developer || \"Developer\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1225,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1221,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-4 text-sm text-warm-gray\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                                className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 1229,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            property.bedrooms || 0,\n                                                                                            \" BR\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1228,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                                                className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 1233,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            property.bathrooms || 0,\n                                                                                            \" Bath\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1232,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                                className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 1237,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            property.area_unit || \"sqft\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1236,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1227,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1217,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-2xl text-gold mb-2\",\n                                                                                children: property.min_price ? \"\".concat(property.price_currency || \"AED\", \" \").concat(property.min_price.toLocaleString()) : \"Price on Request\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1243,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                className: \"border-gold/30 text-gold\",\n                                                                                children: property.sale_status || \"Available\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1250,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1242,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1216,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between pt-4 border-t border-beige\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-6 text-sm text-warm-gray\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 1261,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                property.completionDate || \"TBD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1260,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: \"bg-beige text-soft-brown\",\n                                                                            children: property.status || \"Available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1264,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: property.developer || \"Developer\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1267,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1259,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1258,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1215,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1198,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 1197,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, property.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 1192,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1190,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                lineNumber: 1033,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n        lineNumber: 404,\n        columnNumber: 5\n    }, this);\n}\n_s(AllPropertiesPage, \"RLWQKBGSbsSD5mMFmqXAAW0BhnI=\");\n_c = AllPropertiesPage;\nvar _c;\n$RefreshReg$(_c, \"AllPropertiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AllPropertiesPage.tsx\n"));

/***/ })

});