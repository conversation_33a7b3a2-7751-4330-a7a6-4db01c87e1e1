"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AllPropertiesPage.tsx":
/*!**********************************************!*\
  !*** ./src/components/AllPropertiesPage.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AllPropertiesPage: function() { return /* binding */ AllPropertiesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_skeleton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _ui_empty_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/empty-state */ \"(app-pages-browser)/./src/components/ui/empty-state.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _ui_slider__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ui/slider */ \"(app-pages-browser)/./src/components/ui/slider.tsx\");\n/* harmony import */ var _ui_switch__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ruler.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hammer.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bed.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bath.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./figma/ImageWithFallback */ \"(app-pages-browser)/./src/components/figma/ImageWithFallback.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst initialFilters = {\n    priceUnit: \"total\",\n    minArea: 0,\n    maxArea: 5000,\n    developmentStatus: [],\n    unitTypes: [],\n    bedrooms: [],\n    salesStatus: [],\n    completionDate: \"all\",\n    minPrice: 0,\n    maxPrice: ********\n};\nfunction AllPropertiesPage(param) {\n    let { onProjectSelect, onBack, selectedDeveloper } = param;\n    var _completionDateOptions_find;\n    _s();\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"featured\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filtersOpen, setFiltersOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFilterModalOpen, setIsFilterModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFilters);\n    // Local state for properties\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch properties function\n    const fetchProperties = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            console.log(\"\\uD83D\\uDE80 Fetching properties from API...\");\n            const response = await fetch(\"/api/properties\");\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            console.log(\"✅ Properties fetched successfully:\", data);\n            console.log(\"\\uD83D\\uDCCA Data type:\", typeof data);\n            console.log(\"\\uD83D\\uDCCA Data structure:\", Object.keys(data));\n            // Handle the API response structure\n            let propertiesArray = [];\n            if (data.success && data.data) {\n                // Our API wraps the external response in { success: true, data: externalData }\n                const externalData = data.data;\n                if (externalData.items && Array.isArray(externalData.items)) {\n                    // The external API returns { items: [...], pagination: {...} }\n                    propertiesArray = externalData.items;\n                } else if (Array.isArray(externalData)) {\n                    // Fallback: if external data is directly an array\n                    propertiesArray = externalData;\n                }\n            } else if (Array.isArray(data)) {\n                // If data is directly an array\n                propertiesArray = data;\n            } else if (data.data && Array.isArray(data.data)) {\n                // If data is wrapped in a data property\n                propertiesArray = data.data;\n            } else {\n                // Fallback: try to find any array in the response\n                const possibleArrays = Object.values(data).filter(Array.isArray);\n                if (possibleArrays.length > 0) {\n                    propertiesArray = possibleArrays[0];\n                }\n            }\n            console.log(\"\\uD83D\\uDCCB Properties array:\", propertiesArray);\n            console.log(\"\\uD83D\\uDCCA Properties count:\", propertiesArray.length);\n            setProperties(propertiesArray);\n        } catch (err) {\n            console.error(\"❌ Error fetching properties:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch properties\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Fetch properties on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProperties();\n    }, []);\n    // Use real properties data - ensure it's always an array\n    const allProperties = Array.isArray(properties) ? properties : [];\n    // Filter options\n    const developmentStatusOptions = [\n        \"Presale\",\n        \"Under Construction\",\n        \"Completed\"\n    ];\n    const unitTypeOptions = [\n        \"Apartments\",\n        \"Villa\",\n        \"Townhouse\",\n        \"Duplex\",\n        \"Penthouse\"\n    ];\n    const bedroomOptions = [\n        \"Studio\",\n        \"1 BR\",\n        \"2 BR\",\n        \"3 BR\",\n        \"4 BR\",\n        \"5+ BR\"\n    ];\n    const salesStatusOptions = [\n        \"Announced\",\n        \"Presale (EOI)\",\n        \"Start of Sales\",\n        \"On Sale\",\n        \"Out of Stock\"\n    ];\n    const completionDateOptions = [\n        {\n            value: \"all\",\n            label: \"All Projects\"\n        },\n        {\n            value: \"12months\",\n            label: \"Completing in 12 months\"\n        },\n        {\n            value: \"2years\",\n            label: \"Completing in 2 years\"\n        },\n        {\n            value: \"3years\",\n            label: \"Completing in 3 years\"\n        },\n        {\n            value: \"4years\",\n            label: \"Completing in 4 years\"\n        },\n        {\n            value: \"5years\",\n            label: \"Completing in 5+ years\"\n        }\n    ];\n    // Helper functions for filters\n    const toggleArrayFilter = (array, value)=>{\n        return array.includes(value) ? array.filter((item)=>item !== value) : [\n            ...array,\n            value\n        ];\n    };\n    const updateFilters = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    const resetFilters = ()=>{\n        setFilters(initialFilters);\n    };\n    const hasActiveFilters = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return filters.priceUnit !== \"total\" || filters.minArea !== 0 || filters.maxArea !== 5000 || filters.developmentStatus.length > 0 || filters.unitTypes.length > 0 || filters.bedrooms.length > 0 || filters.salesStatus.length > 0 || filters.completionDate !== \"all\" || filters.minPrice !== 0 || filters.maxPrice !== ********;\n    }, [\n        filters\n    ]);\n    const activeFilterCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        let count = 0;\n        if (filters.priceUnit !== \"total\") count++;\n        if (filters.minArea !== 0 || filters.maxArea !== 5000) count++;\n        if (filters.developmentStatus.length > 0) count++;\n        if (filters.unitTypes.length > 0) count++;\n        if (filters.bedrooms.length > 0) count++;\n        if (filters.salesStatus.length > 0) count++;\n        if (filters.completionDate !== \"all\") count++;\n        if (filters.minPrice !== 0 || filters.maxPrice !== ********) count++;\n        return count;\n    }, [\n        filters\n    ]);\n    // Apply filters to properties\n    const filteredProperties = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // Ensure allProperties is an array before filtering\n        if (!Array.isArray(allProperties)) {\n            console.warn(\"⚠️ allProperties is not an array:\", allProperties);\n            return [];\n        }\n        let filtered = [\n            ...allProperties\n        ];\n        // Apply search query\n        if (searchQuery) {\n            filtered = filtered.filter((property)=>{\n                var _property_title, _property_location_name, _property_location, _property_developer_name, _property_developer;\n                return ((_property_title = property.title) === null || _property_title === void 0 ? void 0 : _property_title.toLowerCase().includes(searchQuery.toLowerCase())) || ((_property_location = property.location) === null || _property_location === void 0 ? void 0 : (_property_location_name = _property_location.name) === null || _property_location_name === void 0 ? void 0 : _property_location_name.toLowerCase().includes(searchQuery.toLowerCase())) || ((_property_developer = property.developer) === null || _property_developer === void 0 ? void 0 : (_property_developer_name = _property_developer.name) === null || _property_developer_name === void 0 ? void 0 : _property_developer_name.toLowerCase().includes(searchQuery.toLowerCase()));\n            });\n        }\n        // Apply developer filter if specified\n        if (selectedDeveloper) {\n            filtered = filtered.filter((property)=>{\n                var _property_developer;\n                return ((_property_developer = property.developer) === null || _property_developer === void 0 ? void 0 : _property_developer.name) === selectedDeveloper.name;\n            });\n        }\n        // Apply area filters\n        filtered = filtered.filter((property)=>property.area >= filters.minArea && property.area <= filters.maxArea);\n        // Apply price filters\n        const getPrice = (property)=>{\n            const price = property.price || 0;\n            return filters.priceUnit === \"sqft\" ? price / property.area : price;\n        };\n        filtered = filtered.filter((property)=>{\n            const price = getPrice(property);\n            return price >= filters.minPrice && price <= filters.maxPrice;\n        });\n        // Apply development status filter\n        if (filters.developmentStatus.length > 0) {\n            filtered = filtered.filter((property)=>filters.developmentStatus.includes(property.status));\n        }\n        // Apply unit type filter\n        if (filters.unitTypes.length > 0) {\n            filtered = filtered.filter((property)=>filters.unitTypes.includes(property.propertyType));\n        }\n        // Apply bedroom filter\n        if (filters.bedrooms.length > 0) {\n            filtered = filtered.filter((property)=>{\n                const bedrooms = \"\".concat(property.bedrooms, \" BR\");\n                return filters.bedrooms.includes(bedrooms);\n            });\n        }\n        return filtered;\n    }, [\n        allProperties,\n        searchQuery,\n        selectedDeveloper,\n        filters\n    ]);\n    const sortedProperties = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const sorted = [\n            ...filteredProperties\n        ];\n        switch(sortBy){\n            case \"price-low\":\n                return sorted.sort((a, b)=>(a.price || 0) - (b.price || 0));\n            case \"price-high\":\n                return sorted.sort((a, b)=>(b.price || 0) - (a.price || 0));\n            case \"completion\":\n                return sorted.sort((a, b)=>(a.completionDate || \"\").localeCompare(b.completionDate || \"\"));\n            case \"location\":\n                return sorted.sort((a, b)=>{\n                    var _a_location, _b_location;\n                    return (((_a_location = a.location) === null || _a_location === void 0 ? void 0 : _a_location.name) || \"\").localeCompare(((_b_location = b.location) === null || _b_location === void 0 ? void 0 : _b_location.name) || \"\");\n                });\n            case \"featured\":\n            default:\n                return sorted.sort((a, b)=>(b.isFeatured ? 1 : 0) - (a.isFeatured ? 1 : 0));\n        }\n    }, [\n        filteredProperties,\n        sortBy\n    ]);\n    const LuxuryCheckboxGroup = (param)=>{\n        let { options, values, onChange, label, icon: Icon } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n            className: \"border-beige/60 shadow-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                    className: \"pb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                className: \"w-5 h-5 text-gold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                    className: \"space-y-3\",\n                    children: options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 h-5 rounded-md border-2 flex items-center justify-center cursor-pointer transition-all duration-200 \".concat(values.includes(option) ? \"bg-gold border-gold text-charcoal shadow-sm\" : \"border-soft-brown/30 hover:border-gold hover:bg-gold/5 hover:shadow-sm\"),\n                                    onClick: ()=>onChange(toggleArrayFilter(values, option)),\n                                    children: values.includes(option) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-3 h-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 43\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm text-warm-gray cursor-pointer flex-1 group-hover:text-soft-brown transition-colors\",\n                                    onClick: ()=>onChange(toggleArrayFilter(values, option)),\n                                    children: option\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, option, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n            lineNumber: 362,\n            columnNumber: 5\n        }, this);\n    };\n    const formatPrice = (value)=>{\n        if (filters.priceUnit === \"sqft\") {\n            return \"\".concat(value.toLocaleString());\n        } else {\n            if (value >= 1000000) {\n                return \"\".concat((value / 1000000).toFixed(1), \"M\");\n            } else if (value >= 1000) {\n                return \"\".concat((value / 1000).toFixed(0), \"K\");\n            }\n            return value.toLocaleString();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-ivory\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-beige shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container py-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: onBack,\n                                                className: \"text-warm-gray hover:text-gold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Home\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 text-warm-gray\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-soft-brown\",\n                                                children: \"Properties\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this),\n                                            selectedDeveloper && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4 text-warm-gray\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gold\",\n                                                        children: selectedDeveloper.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-soft-brown text-[48px] leading-[1.2] py-2\",\n                                        children: selectedDeveloper ? \"\".concat(selectedDeveloper.name) : \"All Developments\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-warm-gray mt-2\",\n                                        children: [\n                                            sortedProperties.length,\n                                            \" properties found\",\n                                            hasActiveFilters && \" (\".concat(activeFilterCount, \" filters applied)\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 max-w-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                placeholder: \"Search properties, locations, developers...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10 bg-white border-beige focus:border-gold rounded-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                                            open: isFilterModalOpen,\n                                            onOpenChange: setIsFilterModalOpen,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"border-soft-brown/30 text-soft-brown hover:bg-soft-brown hover:text-white rounded-xl relative \".concat(hasActiveFilters ? \"bg-gold text-charcoal border-gold\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Filters\",\n                                                            activeFilterCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"ml-2 bg-soft-brown text-white text-xs min-w-[20px] h-5\",\n                                                                children: activeFilterCount\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                                                    className: \"sm:max-w-6xl max-h-[90vh] bg-white flex flex-col overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                                            className: \"flex-shrink-0 pb-6 border-b border-beige\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                                                                className: \"text-2xl text-soft-brown\",\n                                                                                children: \"Advanced Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 489,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogDescription, {\n                                                                                className: \"text-warm-gray mt-2\",\n                                                                                children: \"Refine your property search using the filters below to find properties that match your specific requirements.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 492,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 488,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: resetFilters,\n                                                                                disabled: !hasActiveFilters,\n                                                                                className: \"border-soft-brown/30 transition-all duration-200 \".concat(hasActiveFilters ? \"text-gold border-gold/30 hover:bg-gold/10 hover:border-gold\" : \"text-warm-gray border-beige hover:bg-beige/50\"),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"w-4 h-4 mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 509,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    \"Reset\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 498,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                onClick: ()=>setIsFilterModalOpen(false),\n                                                                                className: \"bg-gold hover:bg-gold/90 text-charcoal\",\n                                                                                children: \"Apply Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 512,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 overflow-y-auto py-8 space-y-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                    className: \"border-gold/20 bg-gradient-to-r from-light-gold/10 to-beige/30\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                                                className: \"flex items-center space-x-2 text-soft-brown text-xl\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"w-6 h-6 text-gold\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 528,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Price Configuration\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 529,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 527,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 526,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                            className: \"space-y-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between p-4 bg-white rounded-xl border border-beige/50\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                                    className: \"w-5 h-5 text-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 536,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-soft-brown font-medium\",\n                                                                                                            children: \"Price Display Mode\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 538,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"text-xs text-warm-gray mt-1\",\n                                                                                                            children: \"Choose how prices are displayed\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 541,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 537,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 535,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                    className: \"text-sm transition-colors \".concat(filters.priceUnit === \"total\" ? \"text-soft-brown\" : \"text-warm-gray\"),\n                                                                                                    children: \"Total Price\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 547,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_switch__WEBPACK_IMPORTED_MODULE_12__.Switch, {\n                                                                                                    checked: filters.priceUnit === \"sqft\",\n                                                                                                    onCheckedChange: (checked)=>updateFilters(\"priceUnit\", checked ? \"sqft\" : \"total\"),\n                                                                                                    className: \"data-[state=checked]:bg-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 556,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                    className: \"text-sm transition-colors \".concat(filters.priceUnit === \"sqft\" ? \"text-soft-brown\" : \"text-warm-gray\"),\n                                                                                                    children: \"Per Sq Ft\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 566,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 546,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 534,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                                    className: \"w-5 h-5 text-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 581,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                    className: \"text-soft-brown font-medium text-lg\",\n                                                                                                    children: [\n                                                                                                        \"Price Range (\",\n                                                                                                        filters.priceUnit === \"sqft\" ? \"AED per Sq Ft\" : \"AED\",\n                                                                                                        \")\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 582,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 580,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-2 gap-4\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Minimum\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 593,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"relative\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray text-sm\",\n                                                                                                                    children: \"AED\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 597,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                                    type: \"number\",\n                                                                                                                    value: filters.minPrice,\n                                                                                                                    onChange: (e)=>updateFilters(\"minPrice\", parseInt(e.target.value) || 0),\n                                                                                                                    className: \"pl-12 border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                                    placeholder: \"0\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 600,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 596,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 592,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Maximum\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 615,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"relative\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray text-sm\",\n                                                                                                                    children: \"AED\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 619,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                                    type: \"number\",\n                                                                                                                    value: filters.maxPrice,\n                                                                                                                    onChange: (e)=>updateFilters(\"maxPrice\", parseInt(e.target.value) || ********),\n                                                                                                                    className: \"pl-12 border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                                    placeholder: \"10,000,000\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 622,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 618,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 614,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 591,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-3 p-4 bg-beige/30 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex justify-between text-sm text-warm-gray\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"AED \",\n                                                                                                                formatPrice(filters.minPrice)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 640,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"AED \",\n                                                                                                                formatPrice(filters.maxPrice)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 641,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 639,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_slider__WEBPACK_IMPORTED_MODULE_11__.Slider, {\n                                                                                                    value: [\n                                                                                                        filters.minPrice,\n                                                                                                        filters.maxPrice\n                                                                                                    ],\n                                                                                                    onValueChange: (param)=>{\n                                                                                                        let [min, max] = param;\n                                                                                                        updateFilters(\"minPrice\", min);\n                                                                                                        updateFilters(\"maxPrice\", max);\n                                                                                                    },\n                                                                                                    max: filters.priceUnit === \"sqft\" ? 5000 : 20000000,\n                                                                                                    min: 0,\n                                                                                                    step: filters.priceUnit === \"sqft\" ? 50 : 50000,\n                                                                                                    className: \"w-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 643,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 638,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 579,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                            className: \"border-beige/60\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                                                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                                className: \"w-5 h-5 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 667,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Area Range\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 668,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 666,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 665,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                                    className: \"space-y-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-2 gap-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Min Sq Ft\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 674,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                            type: \"number\",\n                                                                                                            value: filters.minArea,\n                                                                                                            onChange: (e)=>updateFilters(\"minArea\", parseInt(e.target.value) || 0),\n                                                                                                            className: \"border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                            placeholder: \"0\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 677,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 673,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Max Sq Ft\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 691,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                            type: \"number\",\n                                                                                                            value: filters.maxArea,\n                                                                                                            onChange: (e)=>updateFilters(\"maxArea\", parseInt(e.target.value) || 5000),\n                                                                                                            className: \"border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                            placeholder: \"5,000\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 694,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 690,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 672,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-2 p-3 bg-beige/20 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex justify-between text-sm text-warm-gray\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                filters.minArea.toLocaleString(),\n                                                                                                                \" sq ft\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 711,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                filters.maxArea.toLocaleString(),\n                                                                                                                \" sq ft\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 714,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 710,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_slider__WEBPACK_IMPORTED_MODULE_11__.Slider, {\n                                                                                                    value: [\n                                                                                                        filters.minArea,\n                                                                                                        filters.maxArea\n                                                                                                    ],\n                                                                                                    onValueChange: (param)=>{\n                                                                                                        let [min, max] = param;\n                                                                                                        updateFilters(\"minArea\", min);\n                                                                                                        updateFilters(\"maxArea\", max);\n                                                                                                    },\n                                                                                                    max: 8000,\n                                                                                                    min: 0,\n                                                                                                    step: 50,\n                                                                                                    className: \"w-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 718,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 709,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 671,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 664,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                            className: \"border-beige/60\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                                                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                                className: \"w-5 h-5 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 737,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Project Completion\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 738,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 736,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 735,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                children: \"Completion Timeframe\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 743,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                                                                value: filters.completionDate,\n                                                                                                onValueChange: (value)=>updateFilters(\"completionDate\", value),\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                                                        className: \"w-full border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                                                            placeholder: \"Select completion timeframe\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 753,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                        lineNumber: 752,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                                                        children: completionDateOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                                                                value: option.value,\n                                                                                                                children: option.label\n                                                                                                            }, option.value, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                lineNumber: 757,\n                                                                                                                columnNumber: 35\n                                                                                                            }, this))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                        lineNumber: 755,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 746,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 742,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 741,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 734,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mb-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"w-6 h-6 text-gold\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 774,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-xl text-soft-brown\",\n                                                                                    children: \"Property Characteristics\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 775,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 773,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Development Status\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                                                                                    options: developmentStatusOptions,\n                                                                                    values: filters.developmentStatus,\n                                                                                    onChange: (values)=>updateFilters(\"developmentStatus\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 782,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Unit Type\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                                                                                    options: unitTypeOptions,\n                                                                                    values: filters.unitTypes,\n                                                                                    onChange: (values)=>updateFilters(\"unitTypes\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 793,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Bedrooms\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                                                                                    options: bedroomOptions,\n                                                                                    values: filters.bedrooms,\n                                                                                    onChange: (values)=>updateFilters(\"bedrooms\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 804,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Sales Status\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                                                                    options: salesStatusOptions,\n                                                                                    values: filters.salesStatus,\n                                                                                    onChange: (values)=>updateFilters(\"salesStatus\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 815,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 780,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 772,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                    className: \"border-gold/30 bg-gradient-to-r from-gold/5 to-light-gold/10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                        className: \"pt-6\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"text-lg text-soft-brown mb-2\",\n                                                                                            children: \"Filter Summary\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 833,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-warm-gray\",\n                                                                                            children: [\n                                                                                                activeFilterCount,\n                                                                                                \" filters applied •\",\n                                                                                                \" \",\n                                                                                                sortedProperties.length,\n                                                                                                \" properties match your criteria\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 836,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 832,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    onClick: resetFilters,\n                                                                                    className: \"border-gold text-gold hover:bg-gold hover:text-charcoal\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 847,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Reset All\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 842,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 831,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 830,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                            value: sortBy,\n                                            onValueChange: setSortBy,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                    className: \"w-40 border-soft-brown/30 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                        placeholder: \"Sort by\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 861,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 860,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"featured\",\n                                                            children: \"Featured\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"price-low\",\n                                                            children: \"Price: Low to High\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 865,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"price-high\",\n                                                            children: \"Price: High to Low\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 866,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"completion\",\n                                                            children: \"Completion Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 867,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"location\",\n                                                            children: \"Location\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 868,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 863,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 859,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-soft-brown/30 rounded-xl overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: viewMode === \"grid\" ? \"default\" : \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setViewMode(\"grid\"),\n                                                    className: \"rounded-none \".concat(viewMode === \"grid\" ? \"bg-soft-brown text-white\" : \"text-soft-brown hover:bg-soft-brown/10\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 874,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: viewMode === \"list\" ? \"default\" : \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setViewMode(\"list\"),\n                                                    className: \"rounded-none \".concat(viewMode === \"list\" ? \"bg-soft-brown text-white\" : \"text-soft-brown hover:bg-soft-brown/10\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 873,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this),\n                        hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex flex-wrap items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-warm-gray mr-2\",\n                                    children: \"Active filters:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 15\n                                }, this),\n                                filters.priceUnit !== \"total\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        \"Price per Sq Ft\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>updateFilters(\"priceUnit\", \"total\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 909,\n                                    columnNumber: 17\n                                }, this),\n                                (filters.minArea !== 0 || filters.maxArea !== 5000) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        \"Area: \",\n                                        filters.minArea,\n                                        \"-\",\n                                        filters.maxArea,\n                                        \" sq ft\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>{\n                                                updateFilters(\"minArea\", 0);\n                                                updateFilters(\"maxArea\", 5000);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 926,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 921,\n                                    columnNumber: 17\n                                }, this),\n                                filters.developmentStatus.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            status,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"developmentStatus\", filters.developmentStatus.filter((s)=>s !== status))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 942,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, status, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 936,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.unitTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            type,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"unitTypes\", filters.unitTypes.filter((t)=>t !== type))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 960,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, type, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 954,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.bedrooms.map((bedroom)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            bedroom,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"bedrooms\", filters.bedrooms.filter((b)=>b !== bedroom))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 978,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, bedroom, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 972,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.salesStatus.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            status,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"salesStatus\", filters.salesStatus.filter((s)=>s !== status))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 996,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, status, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 990,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.completionDate !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        (_completionDateOptions_find = completionDateOptions.find((opt)=>opt.value === filters.completionDate)) === null || _completionDateOptions_find === void 0 ? void 0 : _completionDateOptions_find.label,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>updateFilters(\"completionDate\", \"all\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 1017,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 1008,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: resetFilters,\n                                    className: \"text-gold hover:bg-gold/10 text-xs\",\n                                    children: \"Clear all\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 1023,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 904,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                lineNumber: 410,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container py-8\",\n                children: [\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: Array.from({\n                            length: 6\n                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_2__.PropertyCardSkeleton, {}, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1042,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 1040,\n                        columnNumber: 11\n                    }, this),\n                    error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_empty_state__WEBPACK_IMPORTED_MODULE_3__.ErrorState, {\n                        onRetry: ()=>fetchProperties(),\n                        message: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 1049,\n                        columnNumber: 11\n                    }, this),\n                    !loading && !error && sortedProperties.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                className: \"w-16 h-16 text-warm-gray mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1055,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl text-soft-brown mb-2\",\n                                children: \"No properties found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1056,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-warm-gray mb-6\",\n                                children: \"Try adjusting your filters or search criteria to find more properties.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1059,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: resetFilters,\n                                className: \"bg-gold hover:bg-gold/90 text-charcoal\",\n                                children: \"Reset Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1063,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 1054,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            viewMode === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: sortedProperties.map((property)=>{\n                                    var _property_developer;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        className: \"group cursor-pointer border border-beige hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden rounded-xl\",\n                                        onClick: ()=>onProjectSelect(property),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative aspect-[4/3] overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_13__.ImageWithFallback, {\n                                                        src: property.cover_image_url ? JSON.parse(property.cover_image_url).url : \"/placeholder-property.jpg\",\n                                                        alt: property.name || \"Property\",\n                                                        className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1082,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    property.is_partner_project && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: \"absolute top-4 left-4 bg-gold text-charcoal\",\n                                                        children: \"Featured\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1092,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: \"absolute top-4 right-4 bg-black/70 text-white border-0\",\n                                                        children: property.status || \"Available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1096,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1081,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl text-soft-brown group-hover:text-gold transition-colors\",\n                                                                    children: property.name || \"Property Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1103,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-warm-gray mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1107,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: property.area || \"Location\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1108,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1106,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1102,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl text-gold\",\n                                                                    children: filters.priceUnit === \"sqft\" ? \"\".concat(property.currency || \"AED\", \" \").concat(Math.round((property.price || 0) / (property.area || 1)).toLocaleString(), \"/sq ft\") : \"\".concat(property.currency || \"AED\", \" \").concat((property.price || 0).toLocaleString())\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1115,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs border-gold/30 text-gold\",\n                                                                    children: property.propertyType || \"Property\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1124,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1114,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-4 text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            className: \"w-4 h-4 mx-auto text-gold\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1134,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-warm-gray\",\n                                                                            children: [\n                                                                                property.bedrooms || 0,\n                                                                                \" BR\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1135,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1133,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                            className: \"w-4 h-4 mx-auto text-gold\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1140,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-warm-gray\",\n                                                                            children: [\n                                                                                property.bathrooms || 0,\n                                                                                \" Bath\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1141,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1139,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                            className: \"w-4 h-4 mx-auto text-gold\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1146,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-warm-gray\",\n                                                                            children: [\n                                                                                property.area || 0,\n                                                                                \" \",\n                                                                                property.areaUnit || \"sqft\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1147,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1145,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1132,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between pt-4 border-t border-beige\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-warm-gray\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1155,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        property.completionDate || \"TBD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1154,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-warm-gray\",\n                                                                    children: ((_property_developer = property.developer) === null || _property_developer === void 0 ? void 0 : _property_developer.name) || \"Developer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1158,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1153,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 1101,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1100,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, property.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 1076,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1074,\n                                columnNumber: 15\n                            }, this),\n                            viewMode === \"list\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: sortedProperties.map((property)=>{\n                                    var _property_images_, _property_images, _property_location, _property_location1, _property_developer, _property_developer1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        className: \"group cursor-pointer border border-beige hover:shadow-lg transition-all duration-300 overflow-hidden rounded-xl\",\n                                        onClick: ()=>onProjectSelect(property),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                            className: \"p-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-full md:w-80 aspect-[4/3] md:aspect-auto md:h-48 overflow-hidden\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_13__.ImageWithFallback, {\n                                                                src: ((_property_images = property.images) === null || _property_images === void 0 ? void 0 : (_property_images_ = _property_images[0]) === null || _property_images_ === void 0 ? void 0 : _property_images_.url) || \"/placeholder-property.jpg\",\n                                                                alt: property.title || \"Property\",\n                                                                className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1181,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            property.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"absolute top-4 left-4 bg-gold text-charcoal\",\n                                                                children: \"Featured\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1190,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1180,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"text-xl text-soft-brown group-hover:text-gold transition-colors mb-2\",\n                                                                                children: property.title || \"Property Title\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1198,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center text-warm-gray mb-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                                        className: \"w-4 h-4 mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1202,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: ((_property_location = property.location) === null || _property_location === void 0 ? void 0 : _property_location.name) || ((_property_location1 = property.location) === null || _property_location1 === void 0 ? void 0 : _property_location1.city) || \"Location\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1203,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mx-2\",\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1208,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: ((_property_developer = property.developer) === null || _property_developer === void 0 ? void 0 : _property_developer.name) || \"Developer\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1209,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1201,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-4 text-sm text-warm-gray\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                                className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 1215,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            property.bedrooms || 0,\n                                                                                            \" BR\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1214,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                                className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 1219,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            property.bathrooms || 0,\n                                                                                            \" Bath\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1218,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                                className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 1223,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            property.area || 0,\n                                                                                            \" \",\n                                                                                            property.areaUnit || \"sqft\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1222,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1213,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1197,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-2xl text-gold mb-2\",\n                                                                                children: filters.priceUnit === \"sqft\" ? \"\".concat(property.currency || \"AED\", \" \").concat(Math.round((property.price || 0) / (property.area || 1)).toLocaleString(), \"/sq ft\") : \"\".concat(property.currency || \"AED\", \" \").concat((property.price || 0).toLocaleString())\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1230,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                className: \"border-gold/30 text-gold\",\n                                                                                children: property.propertyType || \"Property\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1240,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1229,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1196,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between pt-4 border-t border-beige\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-6 text-sm text-warm-gray\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 1251,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                property.completionDate || \"TBD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1250,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: \"bg-beige text-soft-brown\",\n                                                                            children: property.status || \"Available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1254,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: ((_property_developer1 = property.developer) === null || _property_developer1 === void 0 ? void 0 : _property_developer1.name) || \"Developer\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1257,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1249,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1248,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1195,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1179,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 1178,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, property.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 1173,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1171,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                lineNumber: 1037,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n        lineNumber: 408,\n        columnNumber: 5\n    }, this);\n}\n_s(AllPropertiesPage, \"RLWQKBGSbsSD5mMFmqXAAW0BhnI=\");\n_c = AllPropertiesPage;\nvar _c;\n$RefreshReg$(_c, \"AllPropertiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AllPropertiesPage.tsx\n"));

/***/ })

});