"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AllPropertiesPage.tsx":
/*!**********************************************!*\
  !*** ./src/components/AllPropertiesPage.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AllPropertiesPage: function() { return /* binding */ AllPropertiesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _ui_slider__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/slider */ \"(app-pages-browser)/./src/components/ui/slider.tsx\");\n/* harmony import */ var _ui_switch__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ruler.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hammer.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bed.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bath.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./figma/ImageWithFallback */ \"(app-pages-browser)/./src/components/figma/ImageWithFallback.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst initialFilters = {\n    priceUnit: \"total\",\n    minArea: 0,\n    maxArea: 5000,\n    developmentStatus: [],\n    unitTypes: [],\n    bedrooms: [],\n    salesStatus: [],\n    completionDate: \"all\",\n    minPrice: 0,\n    maxPrice: ********\n};\nfunction AllPropertiesPage(param) {\n    let { onProjectSelect, onBack, selectedDeveloper } = param;\n    var _completionDateOptions_find;\n    _s();\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"featured\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filtersOpen, setFiltersOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFilterModalOpen, setIsFilterModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFilters);\n    // Mock data for properties\n    const allProperties = [\n        {\n            id: 1,\n            name: \"Marina Vista Towers\",\n            location: \"Dubai Marina\",\n            developer: \"Emaar Properties\",\n            image: \"https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80\",\n            price: \"AED 1,200,000\",\n            pricePerSqft: 1200,\n            area: 1000,\n            bedrooms: \"2 BR\",\n            bathrooms: 2,\n            size: \"1,000 sq ft\",\n            completion: \"Q4 2025\",\n            status: \"Under Construction\",\n            salesStatus: \"On Sale\",\n            unitType: \"Apartments\",\n            featured: true,\n            paymentPlan: \"60/40\",\n            roi: \"8.5%\"\n        },\n        {\n            id: 2,\n            name: \"Downtown Heights\",\n            location: \"Downtown Dubai\",\n            developer: \"Dubai Properties\",\n            image: \"https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80\",\n            price: \"AED 2,500,000\",\n            pricePerSqft: 1500,\n            area: 1667,\n            bedrooms: \"3 BR\",\n            bathrooms: 3,\n            size: \"1,667 sq ft\",\n            completion: \"Q2 2026\",\n            status: \"Presale\",\n            salesStatus: \"Presale (EOI)\",\n            unitType: \"Apartments\",\n            featured: true,\n            paymentPlan: \"70/30\",\n            roi: \"9.2%\"\n        },\n        {\n            id: 3,\n            name: \"Palm Luxury Villas\",\n            location: \"Palm Jumeirah\",\n            developer: \"Nakheel\",\n            image: \"https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80\",\n            price: \"AED 8,500,000\",\n            pricePerSqft: 2125,\n            area: 4000,\n            bedrooms: \"5+ BR\",\n            bathrooms: 6,\n            size: \"4,000 sq ft\",\n            completion: \"Q1 2025\",\n            status: \"Completed\",\n            salesStatus: \"On Sale\",\n            unitType: \"Villa\",\n            featured: false,\n            paymentPlan: \"Cash\",\n            roi: \"7.8%\"\n        },\n        {\n            id: 4,\n            name: \"Business Bay Towers\",\n            location: \"Business Bay\",\n            developer: \"Damac Properties\",\n            image: \"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80\",\n            price: \"AED 950,000\",\n            pricePerSqft: 1350,\n            area: 704,\n            bedrooms: \"1 BR\",\n            bathrooms: 1,\n            size: \"704 sq ft\",\n            completion: \"Q3 2025\",\n            status: \"Under Construction\",\n            salesStatus: \"Start of Sales\",\n            unitType: \"Apartments\",\n            featured: false,\n            paymentPlan: \"80/20\",\n            roi: \"8.8%\"\n        },\n        {\n            id: 5,\n            name: \"Creek Harbour Residences\",\n            location: \"Dubai Creek Harbour\",\n            developer: \"Emaar Properties\",\n            image: \"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80\",\n            price: \"AED 1,800,000\",\n            pricePerSqft: 1286,\n            area: 1400,\n            bedrooms: \"2 BR\",\n            bathrooms: 2,\n            size: \"1,400 sq ft\",\n            completion: \"Q1 2027\",\n            status: \"Presale\",\n            salesStatus: \"Announced\",\n            unitType: \"Apartments\",\n            featured: true,\n            paymentPlan: \"60/40\",\n            roi: \"9.5%\"\n        },\n        {\n            id: 6,\n            name: \"DIFC Penthouses\",\n            location: \"DIFC\",\n            developer: \"Omniyat\",\n            image: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80\",\n            price: \"AED 15,000,000\",\n            pricePerSqft: 3750,\n            area: 4000,\n            bedrooms: \"4 BR\",\n            bathrooms: 5,\n            size: \"4,000 sq ft\",\n            completion: \"Q2 2026\",\n            status: \"Under Construction\",\n            salesStatus: \"On Sale\",\n            unitType: \"Penthouse\",\n            featured: true,\n            paymentPlan: \"50/50\",\n            roi: \"6.5%\"\n        }\n    ];\n    // Filter options\n    const developmentStatusOptions = [\n        \"Presale\",\n        \"Under Construction\",\n        \"Completed\"\n    ];\n    const unitTypeOptions = [\n        \"Apartments\",\n        \"Villa\",\n        \"Townhouse\",\n        \"Duplex\",\n        \"Penthouse\"\n    ];\n    const bedroomOptions = [\n        \"Studio\",\n        \"1 BR\",\n        \"2 BR\",\n        \"3 BR\",\n        \"4 BR\",\n        \"5+ BR\"\n    ];\n    const salesStatusOptions = [\n        \"Announced\",\n        \"Presale (EOI)\",\n        \"Start of Sales\",\n        \"On Sale\",\n        \"Out of Stock\"\n    ];\n    const completionDateOptions = [\n        {\n            value: \"all\",\n            label: \"All Projects\"\n        },\n        {\n            value: \"12months\",\n            label: \"Completing in 12 months\"\n        },\n        {\n            value: \"2years\",\n            label: \"Completing in 2 years\"\n        },\n        {\n            value: \"3years\",\n            label: \"Completing in 3 years\"\n        },\n        {\n            value: \"4years\",\n            label: \"Completing in 4 years\"\n        },\n        {\n            value: \"5years\",\n            label: \"Completing in 5+ years\"\n        }\n    ];\n    // Helper functions for filters\n    const toggleArrayFilter = (array, value)=>{\n        return array.includes(value) ? array.filter((item)=>item !== value) : [\n            ...array,\n            value\n        ];\n    };\n    const updateFilters = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    const resetFilters = ()=>{\n        setFilters(initialFilters);\n    };\n    const hasActiveFilters = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return filters.priceUnit !== \"total\" || filters.minArea !== 0 || filters.maxArea !== 5000 || filters.developmentStatus.length > 0 || filters.unitTypes.length > 0 || filters.bedrooms.length > 0 || filters.salesStatus.length > 0 || filters.completionDate !== \"all\" || filters.minPrice !== 0 || filters.maxPrice !== ********;\n    }, [\n        filters\n    ]);\n    const activeFilterCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        let count = 0;\n        if (filters.priceUnit !== \"total\") count++;\n        if (filters.minArea !== 0 || filters.maxArea !== 5000) count++;\n        if (filters.developmentStatus.length > 0) count++;\n        if (filters.unitTypes.length > 0) count++;\n        if (filters.bedrooms.length > 0) count++;\n        if (filters.salesStatus.length > 0) count++;\n        if (filters.completionDate !== \"all\") count++;\n        if (filters.minPrice !== 0 || filters.maxPrice !== ********) count++;\n        return count;\n    }, [\n        filters\n    ]);\n    // Apply filters to properties\n    const filteredProperties = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        let filtered = [\n            ...allProperties\n        ];\n        // Apply search query\n        if (searchQuery) {\n            filtered = filtered.filter((property)=>property.name.toLowerCase().includes(searchQuery.toLowerCase()) || property.location.toLowerCase().includes(searchQuery.toLowerCase()) || property.developer.toLowerCase().includes(searchQuery.toLowerCase()));\n        }\n        // Apply developer filter if specified\n        if (selectedDeveloper) {\n            filtered = filtered.filter((property)=>property.developer === selectedDeveloper.name);\n        }\n        // Apply area filters\n        filtered = filtered.filter((property)=>property.area >= filters.minArea && property.area <= filters.maxArea);\n        // Apply price filters\n        const getPrice = (property)=>{\n            const price = parseInt(property.price.replace(/[^0-9]/g, \"\"));\n            return filters.priceUnit === \"sqft\" ? property.pricePerSqft : price;\n        };\n        filtered = filtered.filter((property)=>{\n            const price = getPrice(property);\n            return price >= filters.minPrice && price <= filters.maxPrice;\n        });\n        // Apply development status filter\n        if (filters.developmentStatus.length > 0) {\n            filtered = filtered.filter((property)=>filters.developmentStatus.includes(property.status));\n        }\n        // Apply unit type filter\n        if (filters.unitTypes.length > 0) {\n            filtered = filtered.filter((property)=>filters.unitTypes.includes(property.unitType));\n        }\n        // Apply bedroom filter\n        if (filters.bedrooms.length > 0) {\n            filtered = filtered.filter((property)=>filters.bedrooms.includes(property.bedrooms));\n        }\n        // Apply sales status filter\n        if (filters.salesStatus.length > 0) {\n            filtered = filtered.filter((property)=>filters.salesStatus.includes(property.salesStatus));\n        }\n        // Apply completion date filter\n        if (filters.completionDate !== \"all\") {\n        // This would need more complex logic based on actual completion dates\n        // For now, just return filtered results\n        }\n        return filtered;\n    }, [\n        allProperties,\n        searchQuery,\n        selectedDeveloper,\n        filters\n    ]);\n    const sortedProperties = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const sorted = [\n            ...filteredProperties\n        ];\n        switch(sortBy){\n            case \"price-low\":\n                return sorted.sort((a, b)=>{\n                    const priceA = parseInt(a.price.replace(/[^0-9]/g, \"\"));\n                    const priceB = parseInt(b.price.replace(/[^0-9]/g, \"\"));\n                    return priceA - priceB;\n                });\n            case \"price-high\":\n                return sorted.sort((a, b)=>{\n                    const priceA = parseInt(a.price.replace(/[^0-9]/g, \"\"));\n                    const priceB = parseInt(b.price.replace(/[^0-9]/g, \"\"));\n                    return priceB - priceA;\n                });\n            case \"completion\":\n                return sorted.sort((a, b)=>a.completion.localeCompare(b.completion));\n            case \"location\":\n                return sorted.sort((a, b)=>a.location.localeCompare(b.location));\n            case \"featured\":\n            default:\n                return sorted.sort((a, b)=>(b.featured ? 1 : 0) - (a.featured ? 1 : 0));\n        }\n    }, [\n        filteredProperties,\n        sortBy\n    ]);\n    const LuxuryCheckboxGroup = (param)=>{\n        let { options, values, onChange, label, icon: Icon } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"border-beige/60 shadow-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    className: \"pb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                className: \"w-5 h-5 text-gold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 423,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"space-y-3\",\n                    children: options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 h-5 rounded-md border-2 flex items-center justify-center cursor-pointer transition-all duration-200 \".concat(values.includes(option) ? \"bg-gold border-gold text-charcoal shadow-sm\" : \"border-soft-brown/30 hover:border-gold hover:bg-gold/5 hover:shadow-sm\"),\n                                    onClick: ()=>onChange(toggleArrayFilter(values, option)),\n                                    children: values.includes(option) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-3 h-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 43\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm text-warm-gray cursor-pointer flex-1 group-hover:text-soft-brown transition-colors\",\n                                    onClick: ()=>onChange(toggleArrayFilter(values, option)),\n                                    children: option\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, option, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n            lineNumber: 422,\n            columnNumber: 5\n        }, this);\n    };\n    const formatPrice = (value)=>{\n        if (filters.priceUnit === \"sqft\") {\n            return \"\".concat(value.toLocaleString());\n        } else {\n            if (value >= 1000000) {\n                return \"\".concat((value / 1000000).toFixed(1), \"M\");\n            } else if (value >= 1000) {\n                return \"\".concat((value / 1000).toFixed(0), \"K\");\n            }\n            return value.toLocaleString();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-ivory\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-beige shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container py-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: onBack,\n                                                className: \"text-warm-gray hover:text-gold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Home\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4 text-warm-gray\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-soft-brown\",\n                                                children: \"Properties\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this),\n                                            selectedDeveloper && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-4 h-4 text-warm-gray\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gold\",\n                                                        children: selectedDeveloper.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-soft-brown text-[48px] leading-[1.2] py-2\",\n                                        children: selectedDeveloper ? \"\".concat(selectedDeveloper.name) : \"All Developments\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-warm-gray mt-2\",\n                                        children: [\n                                            sortedProperties.length,\n                                            \" properties found\",\n                                            hasActiveFilters && \" (\".concat(activeFilterCount, \" filters applied)\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 max-w-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                placeholder: \"Search properties, locations, developers...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10 bg-white border-beige focus:border-gold rounded-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.Dialog, {\n                                            open: isFilterModalOpen,\n                                            onOpenChange: setIsFilterModalOpen,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"border-soft-brown/30 text-soft-brown hover:bg-soft-brown hover:text-white rounded-xl relative \".concat(hasActiveFilters ? \"bg-gold text-charcoal border-gold\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Filters\",\n                                                            activeFilterCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                className: \"ml-2 bg-soft-brown text-white text-xs min-w-[20px] h-5\",\n                                                                children: activeFilterCount\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogContent, {\n                                                    className: \"sm:max-w-6xl max-h-[90vh] bg-white flex flex-col overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogHeader, {\n                                                            className: \"flex-shrink-0 pb-6 border-b border-beige\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogTitle, {\n                                                                                className: \"text-2xl text-soft-brown\",\n                                                                                children: \"Advanced Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 549,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogDescription, {\n                                                                                className: \"text-warm-gray mt-2\",\n                                                                                children: \"Refine your property search using the filters below to find properties that match your specific requirements.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 552,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 548,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: resetFilters,\n                                                                                disabled: !hasActiveFilters,\n                                                                                className: \"border-soft-brown/30 transition-all duration-200 \".concat(hasActiveFilters ? \"text-gold border-gold/30 hover:bg-gold/10 hover:border-gold\" : \"text-warm-gray border-beige hover:bg-beige/50\"),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"w-4 h-4 mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 569,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    \"Reset\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 558,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                onClick: ()=>setIsFilterModalOpen(false),\n                                                                                className: \"bg-gold hover:bg-gold/90 text-charcoal\",\n                                                                                children: \"Apply Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 572,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 overflow-y-auto py-8 space-y-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                    className: \"border-gold/20 bg-gradient-to-r from-light-gold/10 to-beige/30\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                                className: \"flex items-center space-x-2 text-soft-brown text-xl\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"w-6 h-6 text-gold\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 588,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Price Configuration\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 589,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 587,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 586,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                                            className: \"space-y-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between p-4 bg-white rounded-xl border border-beige/50\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                                    className: \"w-5 h-5 text-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 596,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                                            className: \"text-soft-brown font-medium\",\n                                                                                                            children: \"Price Display Mode\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 598,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"text-xs text-warm-gray mt-1\",\n                                                                                                            children: \"Choose how prices are displayed\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 601,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 597,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 595,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                                    className: \"text-sm transition-colors \".concat(filters.priceUnit === \"total\" ? \"text-soft-brown\" : \"text-warm-gray\"),\n                                                                                                    children: \"Total Price\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 607,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_switch__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                                                                    checked: filters.priceUnit === \"sqft\",\n                                                                                                    onCheckedChange: (checked)=>updateFilters(\"priceUnit\", checked ? \"sqft\" : \"total\"),\n                                                                                                    className: \"data-[state=checked]:bg-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 616,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                                    className: \"text-sm transition-colors \".concat(filters.priceUnit === \"sqft\" ? \"text-soft-brown\" : \"text-warm-gray\"),\n                                                                                                    children: \"Per Sq Ft\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 626,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 606,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 594,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                                    className: \"w-5 h-5 text-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 641,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                                    className: \"text-soft-brown font-medium text-lg\",\n                                                                                                    children: [\n                                                                                                        \"Price Range (\",\n                                                                                                        filters.priceUnit === \"sqft\" ? \"AED per Sq Ft\" : \"AED\",\n                                                                                                        \")\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 642,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 640,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-2 gap-4\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Minimum\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 653,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"relative\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray text-sm\",\n                                                                                                                    children: \"AED\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 657,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                                                    type: \"number\",\n                                                                                                                    value: filters.minPrice,\n                                                                                                                    onChange: (e)=>updateFilters(\"minPrice\", parseInt(e.target.value) || 0),\n                                                                                                                    className: \"pl-12 border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                                    placeholder: \"0\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 660,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 656,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 652,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Maximum\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 675,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"relative\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray text-sm\",\n                                                                                                                    children: \"AED\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 679,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                                                    type: \"number\",\n                                                                                                                    value: filters.maxPrice,\n                                                                                                                    onChange: (e)=>updateFilters(\"maxPrice\", parseInt(e.target.value) || ********),\n                                                                                                                    className: \"pl-12 border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                                    placeholder: \"10,000,000\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 682,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 678,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 674,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 651,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-3 p-4 bg-beige/30 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex justify-between text-sm text-warm-gray\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"AED \",\n                                                                                                                formatPrice(filters.minPrice)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 700,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"AED \",\n                                                                                                                formatPrice(filters.maxPrice)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 701,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 699,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_slider__WEBPACK_IMPORTED_MODULE_9__.Slider, {\n                                                                                                    value: [\n                                                                                                        filters.minPrice,\n                                                                                                        filters.maxPrice\n                                                                                                    ],\n                                                                                                    onValueChange: (param)=>{\n                                                                                                        let [min, max] = param;\n                                                                                                        updateFilters(\"minPrice\", min);\n                                                                                                        updateFilters(\"maxPrice\", max);\n                                                                                                    },\n                                                                                                    max: filters.priceUnit === \"sqft\" ? 5000 : 20000000,\n                                                                                                    min: 0,\n                                                                                                    step: filters.priceUnit === \"sqft\" ? 50 : 50000,\n                                                                                                    className: \"w-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 703,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 698,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 639,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 592,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 585,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                            className: \"border-beige/60\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                                className: \"w-5 h-5 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 727,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Area Range\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 728,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 726,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 725,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                                                    className: \"space-y-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-2 gap-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Min Sq Ft\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 734,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                                            type: \"number\",\n                                                                                                            value: filters.minArea,\n                                                                                                            onChange: (e)=>updateFilters(\"minArea\", parseInt(e.target.value) || 0),\n                                                                                                            className: \"border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                            placeholder: \"0\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 737,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 733,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Max Sq Ft\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 751,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                                            type: \"number\",\n                                                                                                            value: filters.maxArea,\n                                                                                                            onChange: (e)=>updateFilters(\"maxArea\", parseInt(e.target.value) || 5000),\n                                                                                                            className: \"border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                            placeholder: \"5,000\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 754,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 750,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 732,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-2 p-3 bg-beige/20 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex justify-between text-sm text-warm-gray\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                filters.minArea.toLocaleString(),\n                                                                                                                \" sq ft\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 771,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                filters.maxArea.toLocaleString(),\n                                                                                                                \" sq ft\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 774,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 770,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_slider__WEBPACK_IMPORTED_MODULE_9__.Slider, {\n                                                                                                    value: [\n                                                                                                        filters.minArea,\n                                                                                                        filters.maxArea\n                                                                                                    ],\n                                                                                                    onValueChange: (param)=>{\n                                                                                                        let [min, max] = param;\n                                                                                                        updateFilters(\"minArea\", min);\n                                                                                                        updateFilters(\"maxArea\", max);\n                                                                                                    },\n                                                                                                    max: 8000,\n                                                                                                    min: 0,\n                                                                                                    step: 50,\n                                                                                                    className: \"w-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 778,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 769,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 731,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 724,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                            className: \"border-beige/60\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                                className: \"w-5 h-5 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 797,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Project Completion\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 798,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 796,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 795,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                                className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                children: \"Completion Timeframe\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 803,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                                                value: filters.completionDate,\n                                                                                                onValueChange: (value)=>updateFilters(\"completionDate\", value),\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                                                        className: \"w-full border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                                                            placeholder: \"Select completion timeframe\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 813,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                        lineNumber: 812,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                                                        children: completionDateOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                                                value: option.value,\n                                                                                                                children: option.label\n                                                                                                            }, option.value, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                lineNumber: 817,\n                                                                                                                columnNumber: 35\n                                                                                                            }, this))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                        lineNumber: 815,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 806,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 802,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 801,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 794,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 722,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mb-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"w-6 h-6 text-gold\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 834,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-xl text-soft-brown\",\n                                                                                    children: \"Property Characteristics\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 835,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 833,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Development Status\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                                                                                    options: developmentStatusOptions,\n                                                                                    values: filters.developmentStatus,\n                                                                                    onChange: (values)=>updateFilters(\"developmentStatus\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 842,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Unit Type\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                                                                                    options: unitTypeOptions,\n                                                                                    values: filters.unitTypes,\n                                                                                    onChange: (values)=>updateFilters(\"unitTypes\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 853,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Bedrooms\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                                                                                    options: bedroomOptions,\n                                                                                    values: filters.bedrooms,\n                                                                                    onChange: (values)=>updateFilters(\"bedrooms\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 864,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Sales Status\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                                                                                    options: salesStatusOptions,\n                                                                                    values: filters.salesStatus,\n                                                                                    onChange: (values)=>updateFilters(\"salesStatus\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 875,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 840,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 832,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                    className: \"border-gold/30 bg-gradient-to-r from-gold/5 to-light-gold/10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                                        className: \"pt-6\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"text-lg text-soft-brown mb-2\",\n                                                                                            children: \"Filter Summary\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 893,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-warm-gray\",\n                                                                                            children: [\n                                                                                                activeFilterCount,\n                                                                                                \" filters applied •\",\n                                                                                                \" \",\n                                                                                                sortedProperties.length,\n                                                                                                \" properties match your criteria\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 896,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 892,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    onClick: resetFilters,\n                                                                                    className: \"border-gold text-gold hover:bg-gold hover:text-charcoal\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 907,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Reset All\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 902,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 891,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 890,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 889,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                            value: sortBy,\n                                            onValueChange: setSortBy,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                    className: \"w-40 border-soft-brown/30 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                        placeholder: \"Sort by\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 921,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 920,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                            value: \"featured\",\n                                                            children: \"Featured\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 924,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                            value: \"price-low\",\n                                                            children: \"Price: Low to High\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 925,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                            value: \"price-high\",\n                                                            children: \"Price: High to Low\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 926,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                            value: \"completion\",\n                                                            children: \"Completion Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 927,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                            value: \"location\",\n                                                            children: \"Location\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 928,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 923,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 919,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-soft-brown/30 rounded-xl overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: viewMode === \"grid\" ? \"default\" : \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setViewMode(\"grid\"),\n                                                    className: \"rounded-none \".concat(viewMode === \"grid\" ? \"bg-soft-brown text-white\" : \"text-soft-brown hover:bg-soft-brown/10\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 944,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: viewMode === \"list\" ? \"default\" : \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setViewMode(\"list\"),\n                                                    className: \"rounded-none \".concat(viewMode === \"list\" ? \"bg-soft-brown text-white\" : \"text-soft-brown hover:bg-soft-brown/10\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 956,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 946,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 11\n                        }, this),\n                        hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex flex-wrap items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-warm-gray mr-2\",\n                                    children: \"Active filters:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 965,\n                                    columnNumber: 15\n                                }, this),\n                                filters.priceUnit !== \"total\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        \"Price per Sq Ft\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>updateFilters(\"priceUnit\", \"total\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 974,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 969,\n                                    columnNumber: 17\n                                }, this),\n                                (filters.minArea !== 0 || filters.maxArea !== 5000) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        \"Area: \",\n                                        filters.minArea,\n                                        \"-\",\n                                        filters.maxArea,\n                                        \" sq ft\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>{\n                                                updateFilters(\"minArea\", 0);\n                                                updateFilters(\"maxArea\", 5000);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 986,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 981,\n                                    columnNumber: 17\n                                }, this),\n                                filters.developmentStatus.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            status,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"developmentStatus\", filters.developmentStatus.filter((s)=>s !== status))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1002,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, status, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 996,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.unitTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            type,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"unitTypes\", filters.unitTypes.filter((t)=>t !== type))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1020,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, type, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 1014,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.bedrooms.map((bedroom)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            bedroom,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"bedrooms\", filters.bedrooms.filter((b)=>b !== bedroom))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, bedroom, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.salesStatus.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            status,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"salesStatus\", filters.salesStatus.filter((s)=>s !== status))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1056,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, status, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 1050,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.completionDate !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        (_completionDateOptions_find = completionDateOptions.find((opt)=>opt.value === filters.completionDate)) === null || _completionDateOptions_find === void 0 ? void 0 : _completionDateOptions_find.label,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>updateFilters(\"completionDate\", \"all\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 1077,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 1068,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: resetFilters,\n                                    className: \"text-gold hover:bg-gold/10 text-xs\",\n                                    children: \"Clear all\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 1083,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 964,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 471,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                lineNumber: 470,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container py-8\",\n                children: sortedProperties.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            className: \"w-16 h-16 text-warm-gray mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 1100,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl text-soft-brown mb-2\",\n                            children: \"No properties found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 1101,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-warm-gray mb-6\",\n                            children: \"Try adjusting your filters or search criteria to find more properties.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 1104,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: resetFilters,\n                            className: \"bg-gold hover:bg-gold/90 text-charcoal\",\n                            children: \"Reset Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 1108,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 1099,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        viewMode === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: sortedProperties.map((property)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"group cursor-pointer border border-beige hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden rounded-xl\",\n                                    onClick: ()=>onProjectSelect(property),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative aspect-[4/3] overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_11__.ImageWithFallback, {\n                                                    src: property.image,\n                                                    alt: property.name,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 1127,\n                                                    columnNumber: 23\n                                                }, this),\n                                                property.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    className: \"absolute top-4 left-4 bg-gold text-charcoal\",\n                                                    children: \"Featured\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 1133,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    className: \"absolute top-4 right-4 bg-black/70 text-white border-0\",\n                                                    children: property.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 1137,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 1126,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl text-soft-brown group-hover:text-gold transition-colors\",\n                                                                children: property.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1144,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-warm-gray mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1148,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: property.location\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1149,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1147,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1143,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl text-gold\",\n                                                                children: filters.priceUnit === \"sqft\" ? \"AED \".concat(property.pricePerSqft.toLocaleString(), \"/sq ft\") : property.price\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1154,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs border-gold/30 text-gold\",\n                                                                children: [\n                                                                    \"ROI \",\n                                                                    property.roi\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1159,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1153,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"w-4 h-4 mx-auto text-gold\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1169,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-warm-gray\",\n                                                                        children: property.bedrooms\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1170,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1168,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                        className: \"w-4 h-4 mx-auto text-gold\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1175,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-warm-gray\",\n                                                                        children: [\n                                                                            property.bathrooms,\n                                                                            \" Bath\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1176,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1174,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                        className: \"w-4 h-4 mx-auto text-gold\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1181,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-warm-gray\",\n                                                                        children: property.size\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1182,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1180,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1167,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between pt-4 border-t border-beige\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-warm-gray\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                        className: \"w-4 h-4 inline mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1190,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    property.completion\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1189,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-warm-gray\",\n                                                                children: property.paymentPlan\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1193,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1188,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1142,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 1141,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, property.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 1121,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 1119,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === \"list\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: sortedProperties.map((property)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"group cursor-pointer border border-beige hover:shadow-lg transition-all duration-300 overflow-hidden rounded-xl\",\n                                    onClick: ()=>onProjectSelect(property),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col md:flex-row\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-full md:w-80 aspect-[4/3] md:aspect-auto md:h-48 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_11__.ImageWithFallback, {\n                                                            src: property.image,\n                                                            alt: property.name,\n                                                            className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1216,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        property.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                            className: \"absolute top-4 left-4 bg-gold text-charcoal\",\n                                                            children: \"Featured\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1222,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 1215,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl text-soft-brown group-hover:text-gold transition-colors mb-2\",\n                                                                            children: property.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1230,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center text-warm-gray mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 1234,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: property.location\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 1235,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"mx-2\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 1236,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: property.developer\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 1237,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1233,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-4 text-sm text-warm-gray\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 1241,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        property.bedrooms\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 1240,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 1245,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        property.bathrooms,\n                                                                                        \" Bath\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 1244,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 1249,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        property.size\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 1248,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1239,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1229,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-2xl text-gold mb-2\",\n                                                                            children: filters.priceUnit === \"sqft\" ? \"AED \".concat(property.pricePerSqft.toLocaleString(), \"/sq ft\") : property.price\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1255,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"border-gold/30 text-gold\",\n                                                                            children: [\n                                                                                \"ROI \",\n                                                                                property.roi\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1260,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1254,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1228,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between pt-4 border-t border-beige\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-6 text-sm text-warm-gray\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                className: \"w-4 h-4 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1271,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            property.completion\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1270,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                        className: \"bg-beige text-soft-brown\",\n                                                                        children: property.status\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1274,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: property.paymentPlan\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1277,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1269,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1268,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 1227,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 1214,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 1213,\n                                        columnNumber: 21\n                                    }, this)\n                                }, property.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 1208,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 1206,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                lineNumber: 1097,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n        lineNumber: 468,\n        columnNumber: 5\n    }, this);\n}\n_s(AllPropertiesPage, \"kl0KH9rW+0y37mFZhmd+LW7nbTw=\");\n_c = AllPropertiesPage;\nvar _c;\n$RefreshReg$(_c, \"AllPropertiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AllPropertiesPage.tsx\n"));

/***/ })

});