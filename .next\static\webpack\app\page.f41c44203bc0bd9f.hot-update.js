"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AllPropertiesPage.tsx":
/*!**********************************************!*\
  !*** ./src/components/AllPropertiesPage.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AllPropertiesPage: function() { return /* binding */ AllPropertiesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_skeleton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _ui_empty_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/empty-state */ \"(app-pages-browser)/./src/components/ui/empty-state.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _ui_slider__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ui/slider */ \"(app-pages-browser)/./src/components/ui/slider.tsx\");\n/* harmony import */ var _ui_switch__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ruler.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hammer.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bed.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,Shield,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./figma/ImageWithFallback */ \"(app-pages-browser)/./src/components/figma/ImageWithFallback.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst initialFilters = {\n    priceUnit: \"total\",\n    minArea: 0,\n    maxArea: 5000,\n    developmentStatus: [],\n    unitTypes: [],\n    bedrooms: [],\n    salesStatus: [],\n    completionDate: \"all\",\n    minPrice: 0,\n    maxPrice: ********\n};\nfunction AllPropertiesPage(param) {\n    let { onProjectSelect, onBack, selectedDeveloper } = param;\n    var _completionDateOptions_find;\n    _s();\n    console.log(\"\\uD83C\\uDFAF AllPropertiesPage component rendered\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"featured\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filtersOpen, setFiltersOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFilterModalOpen, setIsFilterModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFilters);\n    // Local state for properties\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    console.log(\"\\uD83D\\uDD0D Current properties state:\", properties.length, \"items\");\n    // Use useEffect to fetch properties on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (properties.length === 0 && !loading && !error) {\n            console.log(\"\\uD83D\\uDE80 useEffect: Triggering fetch since properties is empty\");\n            setLoading(true);\n            fetch(\"/api/properties\").then((response)=>response.json()).then((data)=>{\n                console.log(\"✅ useEffect fetch successful:\", data);\n                if (data.success && data.data && data.data.items) {\n                    setProperties(data.data.items);\n                    console.log(\"✅ Properties set:\", data.data.items.length, \"items\");\n                }\n                setLoading(false);\n            }).catch((err)=>{\n                console.error(\"❌ useEffect fetch error:\", err);\n                setError(err.message);\n                setLoading(false);\n            });\n        }\n    }, [\n        properties.length,\n        loading,\n        error\n    ]);\n    // Properties are fetched directly in the component body above\n    // Use real properties data - ensure it's always an array\n    const allProperties = Array.isArray(properties) ? properties : [];\n    console.log(\"\\uD83D\\uDCCA allProperties derived from state:\", allProperties.length, \"items\");\n    // Filter options\n    const developmentStatusOptions = [\n        \"Presale\",\n        \"Under Construction\",\n        \"Completed\"\n    ];\n    const unitTypeOptions = [\n        \"Apartments\",\n        \"Villa\",\n        \"Townhouse\",\n        \"Duplex\",\n        \"Penthouse\"\n    ];\n    const bedroomOptions = [\n        \"Studio\",\n        \"1 BR\",\n        \"2 BR\",\n        \"3 BR\",\n        \"4 BR\",\n        \"5+ BR\"\n    ];\n    const salesStatusOptions = [\n        \"Announced\",\n        \"Presale (EOI)\",\n        \"Start of Sales\",\n        \"On Sale\",\n        \"Out of Stock\"\n    ];\n    const completionDateOptions = [\n        {\n            value: \"all\",\n            label: \"All Projects\"\n        },\n        {\n            value: \"12months\",\n            label: \"Completing in 12 months\"\n        },\n        {\n            value: \"2years\",\n            label: \"Completing in 2 years\"\n        },\n        {\n            value: \"3years\",\n            label: \"Completing in 3 years\"\n        },\n        {\n            value: \"4years\",\n            label: \"Completing in 4 years\"\n        },\n        {\n            value: \"5years\",\n            label: \"Completing in 5+ years\"\n        }\n    ];\n    // Helper functions for filters\n    const toggleArrayFilter = (array, value)=>{\n        return array.includes(value) ? array.filter((item)=>item !== value) : [\n            ...array,\n            value\n        ];\n    };\n    const updateFilters = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    const resetFilters = ()=>{\n        setFilters(initialFilters);\n    };\n    const hasActiveFilters = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return filters.priceUnit !== \"total\" || filters.minArea !== 0 || filters.maxArea !== 5000 || filters.developmentStatus.length > 0 || filters.unitTypes.length > 0 || filters.bedrooms.length > 0 || filters.salesStatus.length > 0 || filters.completionDate !== \"all\" || filters.minPrice !== 0 || filters.maxPrice !== ********;\n    }, [\n        filters\n    ]);\n    const activeFilterCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        let count = 0;\n        if (filters.priceUnit !== \"total\") count++;\n        if (filters.minArea !== 0 || filters.maxArea !== 5000) count++;\n        if (filters.developmentStatus.length > 0) count++;\n        if (filters.unitTypes.length > 0) count++;\n        if (filters.bedrooms.length > 0) count++;\n        if (filters.salesStatus.length > 0) count++;\n        if (filters.completionDate !== \"all\") count++;\n        if (filters.minPrice !== 0 || filters.maxPrice !== ********) count++;\n        return count;\n    }, [\n        filters\n    ]);\n    // Apply filters to properties\n    const filteredProperties = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D Starting filtering with allProperties:\", allProperties.length, \"items\");\n        // Ensure allProperties is an array before filtering\n        if (!Array.isArray(allProperties)) {\n            console.warn(\"⚠️ allProperties is not an array:\", allProperties);\n            return [];\n        }\n        let filtered = [\n            ...allProperties\n        ];\n        console.log(\"\\uD83D\\uDD0D Initial filtered count:\", filtered.length);\n        // Apply search query\n        if (searchQuery) {\n            filtered = filtered.filter((property)=>{\n                var _property_name, _property_area, _property_developer;\n                return ((_property_name = property.name) === null || _property_name === void 0 ? void 0 : _property_name.toLowerCase().includes(searchQuery.toLowerCase())) || ((_property_area = property.area) === null || _property_area === void 0 ? void 0 : _property_area.toLowerCase().includes(searchQuery.toLowerCase())) || ((_property_developer = property.developer) === null || _property_developer === void 0 ? void 0 : _property_developer.toLowerCase().includes(searchQuery.toLowerCase()));\n            });\n        }\n        // Apply developer filter if specified\n        if (selectedDeveloper) {\n            filtered = filtered.filter((property)=>property.developer === selectedDeveloper.name);\n        }\n        // Skip area filters for now since API doesn't have numeric area\n        // The 'area' field in API is location name, not size\n        // Skip price filters for now since most properties have null prices\n        // We'll show all properties regardless of price filters\n        // Apply development status filter\n        if (filters.developmentStatus.length > 0) {\n            filtered = filtered.filter((property)=>filters.developmentStatus.includes(property.status));\n        }\n        // Skip unit type filter since API doesn't have propertyType field\n        // Skip bedroom filter since API doesn't have bedrooms field\n        console.log(\"\\uD83D\\uDD0D Final filtered count:\", filtered.length);\n        if (filtered.length === 0) {\n            console.warn(\"⚠️ No properties after filtering! Check filter logic.\");\n        }\n        return filtered;\n    }, [\n        allProperties,\n        searchQuery,\n        selectedDeveloper,\n        filters\n    ]);\n    const sortedProperties = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const sorted = [\n            ...filteredProperties\n        ];\n        switch(sortBy){\n            case \"price-low\":\n                return sorted.sort((a, b)=>(a.min_price || 0) - (b.min_price || 0));\n            case \"price-high\":\n                return sorted.sort((a, b)=>(b.min_price || 0) - (a.min_price || 0));\n            case \"completion\":\n                return sorted.sort((a, b)=>(a.completion_datetime || \"\").localeCompare(b.completion_datetime || \"\"));\n            case \"location\":\n                return sorted.sort((a, b)=>(a.area || \"\").localeCompare(b.area || \"\"));\n            case \"featured\":\n            default:\n                return sorted.sort((a, b)=>(b.is_partner_project ? 1 : 0) - (a.is_partner_project ? 1 : 0));\n        }\n    }, [\n        filteredProperties,\n        sortBy\n    ]);\n    const LuxuryCheckboxGroup = (param)=>{\n        let { options, values, onChange, label, icon: Icon } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n            className: \"border-beige/60 shadow-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                    className: \"pb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                className: \"w-5 h-5 text-gold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                    className: \"space-y-3\",\n                    children: options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 h-5 rounded-md border-2 flex items-center justify-center cursor-pointer transition-all duration-200 \".concat(values.includes(option) ? \"bg-gold border-gold text-charcoal shadow-sm\" : \"border-soft-brown/30 hover:border-gold hover:bg-gold/5 hover:shadow-sm\"),\n                                    onClick: ()=>onChange(toggleArrayFilter(values, option)),\n                                    children: values.includes(option) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-3 h-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 43\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm text-warm-gray cursor-pointer flex-1 group-hover:text-soft-brown transition-colors\",\n                                    onClick: ()=>onChange(toggleArrayFilter(values, option)),\n                                    children: option\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, option, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n            lineNumber: 321,\n            columnNumber: 5\n        }, this);\n    };\n    const formatPrice = (value)=>{\n        if (filters.priceUnit === \"sqft\") {\n            return \"\".concat(value.toLocaleString());\n        } else {\n            if (value >= 1000000) {\n                return \"\".concat((value / 1000000).toFixed(1), \"M\");\n            } else if (value >= 1000) {\n                return \"\".concat((value / 1000).toFixed(0), \"K\");\n            }\n            return value.toLocaleString();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-ivory\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-beige shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container py-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: onBack,\n                                                className: \"text-warm-gray hover:text-gold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Home\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 text-warm-gray\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-soft-brown\",\n                                                children: \"Properties\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            selectedDeveloper && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4 text-warm-gray\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gold\",\n                                                        children: selectedDeveloper.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-soft-brown text-[48px] leading-[1.2] py-2\",\n                                        children: selectedDeveloper ? \"\".concat(selectedDeveloper.name) : \"All Developments\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-warm-gray mt-2\",\n                                        children: [\n                                            sortedProperties.length,\n                                            \" properties found\",\n                                            hasActiveFilters && \" (\".concat(activeFilterCount, \" filters applied)\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 max-w-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                placeholder: \"Search properties, locations, developers...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10 bg-white border-beige focus:border-gold rounded-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                                            open: isFilterModalOpen,\n                                            onOpenChange: setIsFilterModalOpen,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"border-soft-brown/30 text-soft-brown hover:bg-soft-brown hover:text-white rounded-xl relative \".concat(hasActiveFilters ? \"bg-gold text-charcoal border-gold\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Filters\",\n                                                            activeFilterCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"ml-2 bg-soft-brown text-white text-xs min-w-[20px] h-5\",\n                                                                children: activeFilterCount\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                                                    className: \"sm:max-w-6xl max-h-[90vh] bg-white flex flex-col overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                                            className: \"flex-shrink-0 pb-6 border-b border-beige\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                                                                className: \"text-2xl text-soft-brown\",\n                                                                                children: \"Advanced Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 448,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogDescription, {\n                                                                                className: \"text-warm-gray mt-2\",\n                                                                                children: \"Refine your property search using the filters below to find properties that match your specific requirements.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 451,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 447,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: resetFilters,\n                                                                                disabled: !hasActiveFilters,\n                                                                                className: \"border-soft-brown/30 transition-all duration-200 \".concat(hasActiveFilters ? \"text-gold border-gold/30 hover:bg-gold/10 hover:border-gold\" : \"text-warm-gray border-beige hover:bg-beige/50\"),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"w-4 h-4 mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 468,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    \"Reset\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 457,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                onClick: ()=>setIsFilterModalOpen(false),\n                                                                                className: \"bg-gold hover:bg-gold/90 text-charcoal\",\n                                                                                children: \"Apply Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 471,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 456,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 overflow-y-auto py-8 space-y-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                    className: \"border-gold/20 bg-gradient-to-r from-light-gold/10 to-beige/30\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                                                className: \"flex items-center space-x-2 text-soft-brown text-xl\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"w-6 h-6 text-gold\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 487,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Price Configuration\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 488,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 486,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                            className: \"space-y-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between p-4 bg-white rounded-xl border border-beige/50\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                                    className: \"w-5 h-5 text-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 495,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-soft-brown font-medium\",\n                                                                                                            children: \"Price Display Mode\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 497,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"text-xs text-warm-gray mt-1\",\n                                                                                                            children: \"Choose how prices are displayed\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 500,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 496,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 494,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                    className: \"text-sm transition-colors \".concat(filters.priceUnit === \"total\" ? \"text-soft-brown\" : \"text-warm-gray\"),\n                                                                                                    children: \"Total Price\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 506,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_switch__WEBPACK_IMPORTED_MODULE_12__.Switch, {\n                                                                                                    checked: filters.priceUnit === \"sqft\",\n                                                                                                    onCheckedChange: (checked)=>updateFilters(\"priceUnit\", checked ? \"sqft\" : \"total\"),\n                                                                                                    className: \"data-[state=checked]:bg-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 515,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                    className: \"text-sm transition-colors \".concat(filters.priceUnit === \"sqft\" ? \"text-soft-brown\" : \"text-warm-gray\"),\n                                                                                                    children: \"Per Sq Ft\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 525,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 505,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 493,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                                    className: \"w-5 h-5 text-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 540,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                    className: \"text-soft-brown font-medium text-lg\",\n                                                                                                    children: [\n                                                                                                        \"Price Range (\",\n                                                                                                        filters.priceUnit === \"sqft\" ? \"AED per Sq Ft\" : \"AED\",\n                                                                                                        \")\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 541,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 539,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-2 gap-4\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Minimum\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 552,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"relative\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray text-sm\",\n                                                                                                                    children: \"AED\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 556,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                                    type: \"number\",\n                                                                                                                    value: filters.minPrice,\n                                                                                                                    onChange: (e)=>updateFilters(\"minPrice\", parseInt(e.target.value) || 0),\n                                                                                                                    className: \"pl-12 border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                                    placeholder: \"0\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 559,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 555,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 551,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Maximum\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 574,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"relative\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray text-sm\",\n                                                                                                                    children: \"AED\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 578,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                                    type: \"number\",\n                                                                                                                    value: filters.maxPrice,\n                                                                                                                    onChange: (e)=>updateFilters(\"maxPrice\", parseInt(e.target.value) || ********),\n                                                                                                                    className: \"pl-12 border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                                    placeholder: \"10,000,000\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 581,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 577,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 573,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 550,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-3 p-4 bg-beige/30 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex justify-between text-sm text-warm-gray\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"AED \",\n                                                                                                                formatPrice(filters.minPrice)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 599,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"AED \",\n                                                                                                                formatPrice(filters.maxPrice)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 600,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 598,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_slider__WEBPACK_IMPORTED_MODULE_11__.Slider, {\n                                                                                                    value: [\n                                                                                                        filters.minPrice,\n                                                                                                        filters.maxPrice\n                                                                                                    ],\n                                                                                                    onValueChange: (param)=>{\n                                                                                                        let [min, max] = param;\n                                                                                                        updateFilters(\"minPrice\", min);\n                                                                                                        updateFilters(\"maxPrice\", max);\n                                                                                                    },\n                                                                                                    max: filters.priceUnit === \"sqft\" ? 5000 : 20000000,\n                                                                                                    min: 0,\n                                                                                                    step: filters.priceUnit === \"sqft\" ? 50 : 50000,\n                                                                                                    className: \"w-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 602,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 597,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 538,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                            className: \"border-beige/60\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                                                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                                className: \"w-5 h-5 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 626,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Area Range\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 627,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 625,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 624,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                                    className: \"space-y-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-2 gap-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Min Sq Ft\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 633,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                            type: \"number\",\n                                                                                                            value: filters.minArea,\n                                                                                                            onChange: (e)=>updateFilters(\"minArea\", parseInt(e.target.value) || 0),\n                                                                                                            className: \"border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                            placeholder: \"0\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 636,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 632,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Max Sq Ft\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 650,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                            type: \"number\",\n                                                                                                            value: filters.maxArea,\n                                                                                                            onChange: (e)=>updateFilters(\"maxArea\", parseInt(e.target.value) || 5000),\n                                                                                                            className: \"border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                            placeholder: \"5,000\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 653,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 649,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 631,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-2 p-3 bg-beige/20 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex justify-between text-sm text-warm-gray\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                filters.minArea.toLocaleString(),\n                                                                                                                \" sq ft\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 670,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                filters.maxArea.toLocaleString(),\n                                                                                                                \" sq ft\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 673,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 669,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_slider__WEBPACK_IMPORTED_MODULE_11__.Slider, {\n                                                                                                    value: [\n                                                                                                        filters.minArea,\n                                                                                                        filters.maxArea\n                                                                                                    ],\n                                                                                                    onValueChange: (param)=>{\n                                                                                                        let [min, max] = param;\n                                                                                                        updateFilters(\"minArea\", min);\n                                                                                                        updateFilters(\"maxArea\", max);\n                                                                                                    },\n                                                                                                    max: 8000,\n                                                                                                    min: 0,\n                                                                                                    step: 50,\n                                                                                                    className: \"w-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 677,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 668,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 630,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 623,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                            className: \"border-beige/60\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                                                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                                className: \"w-5 h-5 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 696,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Project Completion\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 697,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 695,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 694,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                children: \"Completion Timeframe\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 702,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                                                                value: filters.completionDate,\n                                                                                                onValueChange: (value)=>updateFilters(\"completionDate\", value),\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                                                        className: \"w-full border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                                                            placeholder: \"Select completion timeframe\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 712,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                        lineNumber: 711,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                                                        children: completionDateOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                                                                value: option.value,\n                                                                                                                children: option.label\n                                                                                                            }, option.value, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                lineNumber: 716,\n                                                                                                                columnNumber: 35\n                                                                                                            }, this))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                        lineNumber: 714,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 705,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 701,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 700,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 693,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 621,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mb-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"w-6 h-6 text-gold\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 733,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-xl text-soft-brown\",\n                                                                                    children: \"Property Characteristics\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 734,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 732,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Development Status\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                                                                                    options: developmentStatusOptions,\n                                                                                    values: filters.developmentStatus,\n                                                                                    onChange: (values)=>updateFilters(\"developmentStatus\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 741,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Unit Type\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                                                                                    options: unitTypeOptions,\n                                                                                    values: filters.unitTypes,\n                                                                                    onChange: (values)=>updateFilters(\"unitTypes\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 752,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Bedrooms\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                                                                                    options: bedroomOptions,\n                                                                                    values: filters.bedrooms,\n                                                                                    onChange: (values)=>updateFilters(\"bedrooms\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 763,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Sales Status\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                                                                    options: salesStatusOptions,\n                                                                                    values: filters.salesStatus,\n                                                                                    onChange: (values)=>updateFilters(\"salesStatus\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 774,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 739,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                    className: \"border-gold/30 bg-gradient-to-r from-gold/5 to-light-gold/10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                        className: \"pt-6\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"text-lg text-soft-brown mb-2\",\n                                                                                            children: \"Filter Summary\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 792,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-warm-gray\",\n                                                                                            children: [\n                                                                                                activeFilterCount,\n                                                                                                \" filters applied •\",\n                                                                                                \" \",\n                                                                                                sortedProperties.length,\n                                                                                                \" properties match your criteria\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 795,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 791,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    onClick: resetFilters,\n                                                                                    className: \"border-gold text-gold hover:bg-gold hover:text-charcoal\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 806,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Reset All\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 801,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 790,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 789,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 788,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                            value: sortBy,\n                                            onValueChange: setSortBy,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                    className: \"w-40 border-soft-brown/30 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                        placeholder: \"Sort by\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 819,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"featured\",\n                                                            children: \"Featured\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 823,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"price-low\",\n                                                            children: \"Price: Low to High\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 824,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"price-high\",\n                                                            children: \"Price: High to Low\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 825,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"completion\",\n                                                            children: \"Completion Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 826,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"location\",\n                                                            children: \"Location\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 827,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 822,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 818,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-soft-brown/30 rounded-xl overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: viewMode === \"grid\" ? \"default\" : \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setViewMode(\"grid\"),\n                                                    className: \"rounded-none \".concat(viewMode === \"grid\" ? \"bg-soft-brown text-white\" : \"text-soft-brown hover:bg-soft-brown/10\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: viewMode === \"list\" ? \"default\" : \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setViewMode(\"list\"),\n                                                    className: \"rounded-none \".concat(viewMode === \"list\" ? \"bg-soft-brown text-white\" : \"text-soft-brown hover:bg-soft-brown/10\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 855,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 832,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 11\n                        }, this),\n                        hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex flex-wrap items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-warm-gray mr-2\",\n                                    children: \"Active filters:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 864,\n                                    columnNumber: 15\n                                }, this),\n                                filters.priceUnit !== \"total\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        \"Price per Sq Ft\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>updateFilters(\"priceUnit\", \"total\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 873,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 868,\n                                    columnNumber: 17\n                                }, this),\n                                (filters.minArea !== 0 || filters.maxArea !== 5000) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        \"Area: \",\n                                        filters.minArea,\n                                        \"-\",\n                                        filters.maxArea,\n                                        \" sq ft\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>{\n                                                updateFilters(\"minArea\", 0);\n                                                updateFilters(\"maxArea\", 5000);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 885,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 880,\n                                    columnNumber: 17\n                                }, this),\n                                filters.developmentStatus.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            status,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"developmentStatus\", filters.developmentStatus.filter((s)=>s !== status))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 901,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, status, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 895,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.unitTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            type,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"unitTypes\", filters.unitTypes.filter((t)=>t !== type))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, type, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.bedrooms.map((bedroom)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            bedroom,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"bedrooms\", filters.bedrooms.filter((b)=>b !== bedroom))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 937,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, bedroom, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 931,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.salesStatus.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            status,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"salesStatus\", filters.salesStatus.filter((s)=>s !== status))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 955,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, status, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 949,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.completionDate !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        (_completionDateOptions_find = completionDateOptions.find((opt)=>opt.value === filters.completionDate)) === null || _completionDateOptions_find === void 0 ? void 0 : _completionDateOptions_find.label,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>updateFilters(\"completionDate\", \"all\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 976,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 967,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: resetFilters,\n                                    className: \"text-gold hover:bg-gold/10 text-xs\",\n                                    children: \"Clear all\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 982,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 863,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container py-8\",\n                children: [\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: Array.from({\n                            length: 6\n                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_2__.PropertyCardSkeleton, {}, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1001,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 999,\n                        columnNumber: 11\n                    }, this),\n                    error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_empty_state__WEBPACK_IMPORTED_MODULE_3__.ErrorState, {\n                        onRetry: ()=>fetchProperties(),\n                        message: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 1008,\n                        columnNumber: 11\n                    }, this),\n                    !loading && !error && sortedProperties.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                className: \"w-16 h-16 text-warm-gray mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1014,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl text-soft-brown mb-2\",\n                                children: \"No properties found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1015,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-warm-gray mb-6\",\n                                children: \"Try adjusting your filters or search criteria to find more properties.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1018,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: resetFilters,\n                                className: \"bg-gold hover:bg-gold/90 text-charcoal\",\n                                children: \"Reset Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1022,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 1013,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            viewMode === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: sortedProperties.map((property)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        className: \"group cursor-pointer border border-beige hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden rounded-xl\",\n                                        onClick: ()=>onProjectSelect(property),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative aspect-[4/3] overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_13__.ImageWithFallback, {\n                                                        src: property.cover_image_url ? (()=>{\n                                                            try {\n                                                                return JSON.parse(property.cover_image_url).url;\n                                                            } catch (e) {\n                                                                return \"/placeholder-property.jpg\";\n                                                            }\n                                                        })() : \"/placeholder-property.jpg\",\n                                                        alt: property.name || \"Property\",\n                                                        className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1041,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    property.is_partner_project && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: \"absolute top-4 left-4 bg-gold text-charcoal\",\n                                                        children: \"Featured\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1058,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: \"absolute top-4 right-4 bg-black/70 text-white border-0\",\n                                                        children: property.status || \"Available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1062,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1040,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl text-soft-brown group-hover:text-gold transition-colors\",\n                                                                    children: property.name || \"Property Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1069,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-warm-gray mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1073,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: property.area || \"Location\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1074,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1072,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1068,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl text-gold\",\n                                                                    children: property.min_price && property.max_price ? property.min_price === property.max_price ? \"\".concat(property.price_currency || \"AED\", \" \").concat(property.min_price.toLocaleString()) : \"\".concat(property.price_currency || \"AED\", \" \").concat(property.min_price.toLocaleString(), \" - \").concat(property.max_price.toLocaleString()) : property.min_price ? \"From \".concat(property.price_currency || \"AED\", \" \").concat(property.min_price.toLocaleString()) : property.max_price ? \"Up to \".concat(property.price_currency || \"AED\", \" \").concat(property.max_price.toLocaleString()) : \"Price on Request\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1081,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs border-gold/30 text-gold\",\n                                                                    children: property.sale_status || \"Available\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1100,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1080,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-4 text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                            className: \"w-4 h-4 mx-auto text-gold\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1110,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-warm-gray\",\n                                                                            children: property.area_unit || \"sqft\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1111,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1109,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                            className: \"w-4 h-4 mx-auto text-gold\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1116,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-warm-gray\",\n                                                                            children: property.status || \"Available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1117,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1115,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                            className: \"w-4 h-4 mx-auto text-gold\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1122,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-warm-gray\",\n                                                                            children: property.has_escrow ? \"Escrow\" : \"Direct\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1123,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1121,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1108,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between pt-4 border-t border-beige\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-warm-gray\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1131,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        property.completion_datetime ? new Date(property.completion_datetime).toLocaleDateString() : property.post_handover ? \"Post Handover\" : \"TBD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1130,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-warm-gray\",\n                                                                    children: property.developer || \"Developer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1140,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1129,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 1067,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1066,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, property.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 1035,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1033,\n                                columnNumber: 15\n                            }, this),\n                            viewMode === \"list\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: sortedProperties.map((property)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        className: \"group cursor-pointer border border-beige hover:shadow-lg transition-all duration-300 overflow-hidden rounded-xl\",\n                                        onClick: ()=>onProjectSelect(property),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                            className: \"p-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-full md:w-80 aspect-[4/3] md:aspect-auto md:h-48 overflow-hidden\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_13__.ImageWithFallback, {\n                                                                src: property.cover_image_url ? (()=>{\n                                                                    try {\n                                                                        return JSON.parse(property.cover_image_url).url;\n                                                                    } catch (e) {\n                                                                        return \"/placeholder-property.jpg\";\n                                                                    }\n                                                                })() : \"/placeholder-property.jpg\",\n                                                                alt: property.name || \"Property\",\n                                                                className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1163,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            property.is_partner_project && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"absolute top-4 left-4 bg-gold text-charcoal\",\n                                                                children: \"Featured\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1181,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1162,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"text-xl text-soft-brown group-hover:text-gold transition-colors mb-2\",\n                                                                                children: property.name || \"Property Title\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1189,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center text-warm-gray mb-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                                        className: \"w-4 h-4 mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1193,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: property.area || \"Location\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1194,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mx-2\",\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1195,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: property.developer || \"Developer\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1196,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1192,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-4 text-sm text-warm-gray\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                                className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 1200,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            property.area_unit || \"sqft\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1199,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                                className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 1204,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            property.status || \"Available\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1203,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                                                className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 1208,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            property.has_escrow ? \"Escrow\" : \"Direct\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1207,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1198,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1188,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-2xl text-gold mb-2\",\n                                                                                children: property.min_price && property.max_price ? property.min_price === property.max_price ? \"\".concat(property.price_currency || \"AED\", \" \").concat(property.min_price.toLocaleString()) : \"\".concat(property.price_currency || \"AED\", \" \").concat(property.min_price.toLocaleString(), \" - \").concat(property.max_price.toLocaleString()) : property.min_price ? \"From \".concat(property.price_currency || \"AED\", \" \").concat(property.min_price.toLocaleString()) : property.max_price ? \"Up to \".concat(property.price_currency || \"AED\", \" \").concat(property.max_price.toLocaleString()) : \"Price on Request\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1214,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                className: \"border-gold/30 text-gold\",\n                                                                                children: property.sale_status || \"Available\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1233,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1213,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1187,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between pt-4 border-t border-beige\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-6 text-sm text-warm-gray\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_Shield_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 1244,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                property.completion_datetime ? new Date(property.completion_datetime).toLocaleDateString() : property.post_handover ? \"Post Handover\" : \"TBD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1243,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: \"bg-beige text-soft-brown\",\n                                                                            children: property.status || \"Available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1253,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: property.developer || \"Developer\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1256,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1242,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1241,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1186,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1161,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 1160,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, property.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 1155,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1153,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                lineNumber: 996,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n        lineNumber: 367,\n        columnNumber: 5\n    }, this);\n}\n_s(AllPropertiesPage, \"RLWQKBGSbsSD5mMFmqXAAW0BhnI=\");\n_c = AllPropertiesPage;\nvar _c;\n$RefreshReg$(_c, \"AllPropertiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0FsbFByb3BlcnRpZXNQYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWtFO0FBRWI7QUFDUDtBQUlUO0FBQ0Y7QUFDa0M7QUFRaEQ7QUFDYztBQUNBO0FBT2Q7QUFDZ0I7QUFFQTtBQThCZjtBQUN3QztBQXFCOUQsTUFBTWtELGlCQUE4QjtJQUNsQ0MsV0FBVztJQUNYQyxTQUFTO0lBQ1RDLFNBQVM7SUFDVEMsbUJBQW1CLEVBQUU7SUFDckJDLFdBQVcsRUFBRTtJQUNiQyxVQUFVLEVBQUU7SUFDWkMsYUFBYSxFQUFFO0lBQ2ZDLGdCQUFnQjtJQUNoQkMsVUFBVTtJQUNWQyxVQUFVO0FBQ1o7QUFFTyxTQUFTQyxrQkFBa0IsS0FJVDtRQUpTLEVBQ2hDQyxlQUFlLEVBQ2ZDLE1BQU0sRUFDTkMsaUJBQWlCLEVBQ00sR0FKUztRQTYyQmRDOztJQXgyQmxCQyxRQUFRQyxHQUFHLENBQUM7SUFFWixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR3JFLCtDQUFRQSxDQUFrQjtJQUMxRCxNQUFNLENBQUNzRSxRQUFRQyxVQUFVLEdBQUd2RSwrQ0FBUUEsQ0FBQztJQUNyQyxNQUFNLENBQUN3RSxhQUFhQyxlQUFlLEdBQUd6RSwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUMwRSxhQUFhQyxlQUFlLEdBQUczRSwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUM0RSxtQkFBbUJDLHFCQUFxQixHQUFHN0UsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDOEUsU0FBU0MsV0FBVyxHQUFHL0UsK0NBQVFBLENBQWNrRDtJQUVwRCw2QkFBNkI7SUFDN0IsTUFBTSxDQUFDOEIsWUFBWUMsY0FBYyxHQUFHakYsK0NBQVFBLENBQVEsRUFBRTtJQUN0RCxNQUFNLENBQUNrRixTQUFTQyxXQUFXLEdBQUduRiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNvRixPQUFPQyxTQUFTLEdBQUdyRiwrQ0FBUUEsQ0FBZ0I7SUFFbERrRSxRQUFRQyxHQUFHLENBQUMsMENBQWdDYSxXQUFXTSxNQUFNLEVBQUU7SUFFL0QsNkNBQTZDO0lBQzdDcEYsZ0RBQVNBLENBQUM7UUFDUixJQUFJOEUsV0FBV00sTUFBTSxLQUFLLEtBQUssQ0FBQ0osV0FBVyxDQUFDRSxPQUFPO1lBQ2pEbEIsUUFBUUMsR0FBRyxDQUFDO1lBQ1pnQixXQUFXO1lBQ1hJLE1BQU0sbUJBQ0hDLElBQUksQ0FBQyxDQUFDQyxXQUFhQSxTQUFTQyxJQUFJLElBQ2hDRixJQUFJLENBQUMsQ0FBQ0c7Z0JBQ0x6QixRQUFRQyxHQUFHLENBQUMsaUNBQWlDd0I7Z0JBQzdDLElBQUlBLEtBQUtDLE9BQU8sSUFBSUQsS0FBS0EsSUFBSSxJQUFJQSxLQUFLQSxJQUFJLENBQUNFLEtBQUssRUFBRTtvQkFDaERaLGNBQWNVLEtBQUtBLElBQUksQ0FBQ0UsS0FBSztvQkFDN0IzQixRQUFRQyxHQUFHLENBQUMscUJBQXFCd0IsS0FBS0EsSUFBSSxDQUFDRSxLQUFLLENBQUNQLE1BQU0sRUFBRTtnQkFDM0Q7Z0JBQ0FILFdBQVc7WUFDYixHQUNDVyxLQUFLLENBQUMsQ0FBQ0M7Z0JBQ043QixRQUFRa0IsS0FBSyxDQUFDLDRCQUE0Qlc7Z0JBQzFDVixTQUFTVSxJQUFJQyxPQUFPO2dCQUNwQmIsV0FBVztZQUNiO1FBQ0o7SUFDRixHQUFHO1FBQUNILFdBQVdNLE1BQU07UUFBRUo7UUFBU0U7S0FBTTtJQUV0Qyw4REFBOEQ7SUFFOUQseURBQXlEO0lBQ3pELE1BQU1hLGdCQUFnQkMsTUFBTUMsT0FBTyxDQUFDbkIsY0FBY0EsYUFBYSxFQUFFO0lBQ2pFZCxRQUFRQyxHQUFHLENBQ1Qsa0RBQ0E4QixjQUFjWCxNQUFNLEVBQ3BCO0lBR0YsaUJBQWlCO0lBQ2pCLE1BQU1jLDJCQUEyQjtRQUMvQjtRQUNBO1FBQ0E7S0FDRDtJQUNELE1BQU1DLGtCQUFrQjtRQUN0QjtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFDRCxNQUFNQyxpQkFBaUI7UUFBQztRQUFVO1FBQVE7UUFBUTtRQUFRO1FBQVE7S0FBUTtJQUMxRSxNQUFNQyxxQkFBcUI7UUFDekI7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0lBQ0QsTUFBTXRDLHdCQUF3QjtRQUM1QjtZQUFFdUMsT0FBTztZQUFPQyxPQUFPO1FBQWU7UUFDdEM7WUFBRUQsT0FBTztZQUFZQyxPQUFPO1FBQTBCO1FBQ3REO1lBQUVELE9BQU87WUFBVUMsT0FBTztRQUF3QjtRQUNsRDtZQUFFRCxPQUFPO1lBQVVDLE9BQU87UUFBd0I7UUFDbEQ7WUFBRUQsT0FBTztZQUFVQyxPQUFPO1FBQXdCO1FBQ2xEO1lBQUVELE9BQU87WUFBVUMsT0FBTztRQUF5QjtLQUNwRDtJQUVELCtCQUErQjtJQUMvQixNQUFNQyxvQkFBb0IsQ0FBQ0MsT0FBaUJIO1FBQzFDLE9BQU9HLE1BQU1DLFFBQVEsQ0FBQ0osU0FDbEJHLE1BQU1FLE1BQU0sQ0FBQyxDQUFDQyxPQUFTQSxTQUFTTixTQUNoQztlQUFJRztZQUFPSDtTQUFNO0lBQ3ZCO0lBRUEsTUFBTU8sZ0JBQWdCLENBQUNDLEtBQXdCUjtRQUM3Q3pCLFdBQVcsQ0FBQ2tDLE9BQVU7Z0JBQUUsR0FBR0EsSUFBSTtnQkFBRSxDQUFDRCxJQUFJLEVBQUVSO1lBQU07SUFDaEQ7SUFFQSxNQUFNVSxlQUFlO1FBQ25CbkMsV0FBVzdCO0lBQ2I7SUFFQSxNQUFNaUUsbUJBQW1CbEgsOENBQU9BLENBQUM7UUFDL0IsT0FDRTZFLFFBQVEzQixTQUFTLEtBQUssV0FDdEIyQixRQUFRMUIsT0FBTyxLQUFLLEtBQ3BCMEIsUUFBUXpCLE9BQU8sS0FBSyxRQUNwQnlCLFFBQVF4QixpQkFBaUIsQ0FBQ2dDLE1BQU0sR0FBRyxLQUNuQ1IsUUFBUXZCLFNBQVMsQ0FBQytCLE1BQU0sR0FBRyxLQUMzQlIsUUFBUXRCLFFBQVEsQ0FBQzhCLE1BQU0sR0FBRyxLQUMxQlIsUUFBUXJCLFdBQVcsQ0FBQzZCLE1BQU0sR0FBRyxLQUM3QlIsUUFBUXBCLGNBQWMsS0FBSyxTQUMzQm9CLFFBQVFuQixRQUFRLEtBQUssS0FDckJtQixRQUFRbEIsUUFBUSxLQUFLO0lBRXpCLEdBQUc7UUFBQ2tCO0tBQVE7SUFFWixNQUFNc0Msb0JBQW9CbkgsOENBQU9BLENBQUM7UUFDaEMsSUFBSW9ILFFBQVE7UUFDWixJQUFJdkMsUUFBUTNCLFNBQVMsS0FBSyxTQUFTa0U7UUFDbkMsSUFBSXZDLFFBQVExQixPQUFPLEtBQUssS0FBSzBCLFFBQVF6QixPQUFPLEtBQUssTUFBTWdFO1FBQ3ZELElBQUl2QyxRQUFReEIsaUJBQWlCLENBQUNnQyxNQUFNLEdBQUcsR0FBRytCO1FBQzFDLElBQUl2QyxRQUFRdkIsU0FBUyxDQUFDK0IsTUFBTSxHQUFHLEdBQUcrQjtRQUNsQyxJQUFJdkMsUUFBUXRCLFFBQVEsQ0FBQzhCLE1BQU0sR0FBRyxHQUFHK0I7UUFDakMsSUFBSXZDLFFBQVFyQixXQUFXLENBQUM2QixNQUFNLEdBQUcsR0FBRytCO1FBQ3BDLElBQUl2QyxRQUFRcEIsY0FBYyxLQUFLLE9BQU8yRDtRQUN0QyxJQUFJdkMsUUFBUW5CLFFBQVEsS0FBSyxLQUFLbUIsUUFBUWxCLFFBQVEsS0FBSyxVQUFVeUQ7UUFDN0QsT0FBT0E7SUFDVCxHQUFHO1FBQUN2QztLQUFRO0lBRVosOEJBQThCO0lBQzlCLE1BQU13QyxxQkFBcUJySCw4Q0FBT0EsQ0FBQztRQUNqQ2lFLFFBQVFDLEdBQUcsQ0FDVCx1REFDQThCLGNBQWNYLE1BQU0sRUFDcEI7UUFHRixvREFBb0Q7UUFDcEQsSUFBSSxDQUFDWSxNQUFNQyxPQUFPLENBQUNGLGdCQUFnQjtZQUNqQy9CLFFBQVFxRCxJQUFJLENBQUMscUNBQXFDdEI7WUFDbEQsT0FBTyxFQUFFO1FBQ1g7UUFFQSxJQUFJdUIsV0FBVztlQUFJdkI7U0FBYztRQUNqQy9CLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBOEJxRCxTQUFTbEMsTUFBTTtRQUV6RCxxQkFBcUI7UUFDckIsSUFBSWQsYUFBYTtZQUNmZ0QsV0FBV0EsU0FBU1gsTUFBTSxDQUN4QixDQUFDWTtvQkFDQ0EsZ0JBQ0FBLGdCQUNBQTt1QkFGQUEsRUFBQUEsaUJBQUFBLFNBQVNDLElBQUksY0FBYkQscUNBQUFBLGVBQWVFLFdBQVcsR0FBR2YsUUFBUSxDQUFDcEMsWUFBWW1ELFdBQVcsVUFDN0RGLGlCQUFBQSxTQUFTRyxJQUFJLGNBQWJILHFDQUFBQSxlQUFlRSxXQUFXLEdBQUdmLFFBQVEsQ0FBQ3BDLFlBQVltRCxXQUFXLFVBQzdERixzQkFBQUEsU0FBU0ksU0FBUyxjQUFsQkosMENBQUFBLG9CQUFvQkUsV0FBVyxHQUFHZixRQUFRLENBQUNwQyxZQUFZbUQsV0FBVzs7UUFFeEU7UUFFQSxzQ0FBc0M7UUFDdEMsSUFBSTNELG1CQUFtQjtZQUNyQndELFdBQVdBLFNBQVNYLE1BQU0sQ0FDeEIsQ0FBQ1ksV0FBYUEsU0FBU0ksU0FBUyxLQUFLN0Qsa0JBQWtCMEQsSUFBSTtRQUUvRDtRQUVBLGdFQUFnRTtRQUNoRSxxREFBcUQ7UUFFckQsb0VBQW9FO1FBQ3BFLHdEQUF3RDtRQUV4RCxrQ0FBa0M7UUFDbEMsSUFBSTVDLFFBQVF4QixpQkFBaUIsQ0FBQ2dDLE1BQU0sR0FBRyxHQUFHO1lBQ3hDa0MsV0FBV0EsU0FBU1gsTUFBTSxDQUFDLENBQUNZLFdBQzFCM0MsUUFBUXhCLGlCQUFpQixDQUFDc0QsUUFBUSxDQUFDYSxTQUFTSyxNQUFNO1FBRXREO1FBRUEsa0VBQWtFO1FBRWxFLDREQUE0RDtRQUU1RDVELFFBQVFDLEdBQUcsQ0FBQyxzQ0FBNEJxRCxTQUFTbEMsTUFBTTtRQUN2RCxJQUFJa0MsU0FBU2xDLE1BQU0sS0FBSyxHQUFHO1lBQ3pCcEIsUUFBUXFELElBQUksQ0FBQztRQUNmO1FBRUEsT0FBT0M7SUFDVCxHQUFHO1FBQUN2QjtRQUFlekI7UUFBYVI7UUFBbUJjO0tBQVE7SUFFM0QsTUFBTWlELG1CQUFtQjlILDhDQUFPQSxDQUFDO1FBQy9CLE1BQU0rSCxTQUFTO2VBQUlWO1NBQW1CO1FBRXRDLE9BQVFoRDtZQUNOLEtBQUs7Z0JBQ0gsT0FBTzBELE9BQU9DLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNLENBQUNELEVBQUVFLFNBQVMsSUFBSSxLQUFNRCxDQUFBQSxFQUFFQyxTQUFTLElBQUk7WUFDcEUsS0FBSztnQkFDSCxPQUFPSixPQUFPQyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTSxDQUFDQSxFQUFFQyxTQUFTLElBQUksS0FBTUYsQ0FBQUEsRUFBRUUsU0FBUyxJQUFJO1lBQ3BFLEtBQUs7Z0JBQ0gsT0FBT0osT0FBT0MsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQ3JCLENBQUNELEVBQUVHLG1CQUFtQixJQUFJLEVBQUMsRUFBR0MsYUFBYSxDQUN6Q0gsRUFBRUUsbUJBQW1CLElBQUk7WUFHL0IsS0FBSztnQkFDSCxPQUFPTCxPQUFPQyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFDckIsQ0FBQ0QsRUFBRU4sSUFBSSxJQUFJLEVBQUMsRUFBR1UsYUFBYSxDQUFDSCxFQUFFUCxJQUFJLElBQUk7WUFFM0MsS0FBSztZQUNMO2dCQUNFLE9BQU9JLE9BQU9DLElBQUksQ0FDaEIsQ0FBQ0MsR0FBR0MsSUFDRixDQUFDQSxFQUFFSSxrQkFBa0IsR0FBRyxJQUFJLEtBQU1MLENBQUFBLEVBQUVLLGtCQUFrQixHQUFHLElBQUk7UUFFckU7SUFDRixHQUFHO1FBQUNqQjtRQUFvQmhEO0tBQU87SUFFL0IsTUFBTWtFLHNCQUFzQjtZQUFDLEVBQzNCQyxPQUFPLEVBQ1BDLE1BQU0sRUFDTkMsUUFBUSxFQUNSbEMsS0FBSyxFQUNMbUMsTUFBTUMsSUFBSSxFQU9YOzZCQUNDLDhEQUFDdEksMENBQUlBO1lBQUN1SSxXQUFVOzs4QkFDZCw4REFBQ3JJLGdEQUFVQTtvQkFBQ3FJLFdBQVU7OEJBQ3BCLDRFQUFDcEksK0NBQVNBO3dCQUFDb0ksV0FBVTs7MENBQ25CLDhEQUFDRDtnQ0FBS0MsV0FBVTs7Ozs7OzBDQUNoQiw4REFBQ0M7MENBQU10Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBR1gsOERBQUNqRyxpREFBV0E7b0JBQUNzSSxXQUFVOzhCQUNwQkwsUUFBUU8sR0FBRyxDQUFDLENBQUNDLHVCQUNaLDhEQUFDQzs0QkFBaUJKLFdBQVU7OzhDQUMxQiw4REFBQ0k7b0NBQ0NKLFdBQVcsMkdBSVYsT0FIQ0osT0FBTzlCLFFBQVEsQ0FBQ3FDLFVBQ1osZ0RBQ0E7b0NBRU5FLFNBQVMsSUFBTVIsU0FBU2pDLGtCQUFrQmdDLFFBQVFPOzhDQUVqRFAsT0FBTzlCLFFBQVEsQ0FBQ3FDLHlCQUFXLDhEQUFDeEcsaVFBQUtBO3dDQUFDcUcsV0FBVTs7Ozs7Ozs7Ozs7OENBRS9DLDhEQUFDckM7b0NBQ0NxQyxXQUFVO29DQUNWSyxTQUFTLElBQU1SLFNBQVNqQyxrQkFBa0JnQyxRQUFRTzs4Q0FFakRBOzs7Ozs7OzJCQWZLQTs7Ozs7Ozs7Ozs7Ozs7Ozs7SUF1QmxCLE1BQU1HLGNBQWMsQ0FBQzVDO1FBQ25CLElBQUkxQixRQUFRM0IsU0FBUyxLQUFLLFFBQVE7WUFDaEMsT0FBTyxHQUEwQixPQUF2QnFELE1BQU02QyxjQUFjO1FBQ2hDLE9BQU87WUFDTCxJQUFJN0MsU0FBUyxTQUFTO2dCQUNwQixPQUFPLEdBQWdDLE9BQTdCLENBQUNBLFFBQVEsT0FBTSxFQUFHOEMsT0FBTyxDQUFDLElBQUc7WUFDekMsT0FBTyxJQUFJOUMsU0FBUyxNQUFNO2dCQUN4QixPQUFPLEdBQTZCLE9BQTFCLENBQUNBLFFBQVEsSUFBRyxFQUFHOEMsT0FBTyxDQUFDLElBQUc7WUFDdEM7WUFDQSxPQUFPOUMsTUFBTTZDLGNBQWM7UUFDN0I7SUFDRjtJQUVBLHFCQUNFLDhEQUFDSDtRQUFJSixXQUFVOzswQkFFYiw4REFBQ1M7Z0JBQU9ULFdBQVU7MEJBQ2hCLDRFQUFDSTtvQkFBSUosV0FBVTs7c0NBQ2IsOERBQUNJOzRCQUFJSixXQUFVO3NDQUNiLDRFQUFDSTs7a0RBQ0MsOERBQUNBO3dDQUFJSixXQUFVOzswREFDYiw4REFBQ3pJLDhDQUFNQTtnREFDTG1KLFNBQVE7Z0RBQ1JDLE1BQUs7Z0RBQ0xOLFNBQVNwRjtnREFDVCtFLFdBQVU7O2tFQUVWLDhEQUFDcEgsaVFBQUlBO3dEQUFDb0gsV0FBVTs7Ozs7O29EQUFpQjs7Ozs7OzswREFHbkMsOERBQUNuSCxpUUFBWUE7Z0RBQUNtSCxXQUFVOzs7Ozs7MERBQ3hCLDhEQUFDQztnREFBS0QsV0FBVTswREFBa0I7Ozs7Ozs0Q0FDakM5RSxtQ0FDQzs7a0VBQ0UsOERBQUNyQyxpUUFBWUE7d0RBQUNtSCxXQUFVOzs7Ozs7a0VBQ3hCLDhEQUFDQzt3REFBS0QsV0FBVTtrRUFBYTlFLGtCQUFrQjBELElBQUk7Ozs7Ozs7Ozs7Ozs7O2tEQUl6RCw4REFBQ2dDO3dDQUFHWixXQUFVO2tEQUNYOUUsb0JBQ0csR0FBMEIsT0FBdkJBLGtCQUFrQjBELElBQUksSUFDekI7Ozs7OztrREFFTiw4REFBQ2lDO3dDQUFFYixXQUFVOzs0Q0FDVmYsaUJBQWlCekMsTUFBTTs0Q0FBQzs0Q0FDeEI2QixvQkFBb0IsS0FBdUIsT0FBbEJDLG1CQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1sRCw4REFBQzhCOzRCQUFJSixXQUFVOzs4Q0FDYiw4REFBQ0k7b0NBQUlKLFdBQVU7OENBQ2IsNEVBQUNJO3dDQUFJSixXQUFVOzswREFDYiw4REFBQzNHLGlRQUFNQTtnREFBQzJHLFdBQVU7Ozs7OzswREFDbEIsOERBQUM3SCw0Q0FBS0E7Z0RBQ0oySSxhQUFZO2dEQUNacEQsT0FBT2hDO2dEQUNQbUUsVUFBVSxDQUFDa0IsSUFBTXBGLGVBQWVvRixFQUFFQyxNQUFNLENBQUN0RCxLQUFLO2dEQUM5Q3NDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUtoQiw4REFBQ0k7b0NBQUlKLFdBQVU7O3NEQUViLDhEQUFDbkksOENBQU1BOzRDQUNMb0osTUFBTW5GOzRDQUNOb0YsY0FBY25GOzs4REFFZCw4REFBQzdELHFEQUFhQTtvREFBQ2lKLE9BQU87OERBQ3BCLDRFQUFDNUosOENBQU1BO3dEQUNMbUosU0FBUTt3REFDUkMsTUFBSzt3REFDTFgsV0FBVyxpR0FJVixPQUhDM0IsbUJBQ0ksc0NBQ0E7OzBFQUdOLDhEQUFDL0UsaVFBQWlCQTtnRUFBQzBHLFdBQVU7Ozs7Ozs0REFBaUI7NERBRTdDMUIsb0JBQW9CLG1CQUNuQiw4REFBQzlHLDRDQUFLQTtnRUFBQ3dJLFdBQVU7MEVBQ2QxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBS1QsOERBQUN4RyxxREFBYUE7b0RBQUNrSSxXQUFVOztzRUFFdkIsOERBQUNoSSxvREFBWUE7NERBQUNnSSxXQUFVO3NFQUN0Qiw0RUFBQ0k7Z0VBQUlKLFdBQVU7O2tGQUNiLDhEQUFDSTs7MEZBQ0MsOERBQUNuSSxtREFBV0E7Z0ZBQUMrSCxXQUFVOzBGQUEyQjs7Ozs7OzBGQUdsRCw4REFBQ2pJLHlEQUFpQkE7Z0ZBQUNpSSxXQUFVOzBGQUFzQjs7Ozs7Ozs7Ozs7O2tGQUtyRCw4REFBQ0k7d0VBQUlKLFdBQVU7OzBGQUNiLDhEQUFDekksOENBQU1BO2dGQUNMbUosU0FBUTtnRkFDUkMsTUFBSztnRkFDTE4sU0FBU2pDO2dGQUNUZ0QsVUFBVSxDQUFDL0M7Z0ZBQ1gyQixXQUFXLG9EQUlWLE9BSEMzQixtQkFDSSxnRUFDQTs7a0dBR04sOERBQUMzRSxpUUFBU0E7d0ZBQUNzRyxXQUFVOzs7Ozs7b0ZBQWlCOzs7Ozs7OzBGQUd4Qyw4REFBQ3pJLDhDQUFNQTtnRkFDTDhJLFNBQVMsSUFBTXRFLHFCQUFxQjtnRkFDcENpRSxXQUFVOzBGQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRUFRUCw4REFBQ0k7NERBQUlKLFdBQVU7OzhFQUViLDhEQUFDdkksMENBQUlBO29FQUFDdUksV0FBVTs7c0ZBQ2QsOERBQUNySSxnREFBVUE7c0ZBQ1QsNEVBQUNDLCtDQUFTQTtnRkFBQ29JLFdBQVU7O2tHQUNuQiw4REFBQ3BHLGlRQUFRQTt3RkFBQ29HLFdBQVU7Ozs7OztrR0FDcEIsOERBQUNDO2tHQUFLOzs7Ozs7Ozs7Ozs7Ozs7OztzRkFHViw4REFBQ3ZJLGlEQUFXQTs0RUFBQ3NJLFdBQVU7OzhGQUVyQiw4REFBQ0k7b0ZBQUlKLFdBQVU7O3NHQUNiLDhEQUFDSTs0RkFBSUosV0FBVTs7OEdBQ2IsOERBQUMvRyxpUUFBVUE7b0dBQUMrRyxXQUFVOzs7Ozs7OEdBQ3RCLDhEQUFDSTs7c0hBQ0MsOERBQUNoSSw0Q0FBS0E7NEdBQUM0SCxXQUFVO3NIQUE4Qjs7Ozs7O3NIQUcvQyw4REFBQ2E7NEdBQUViLFdBQVU7c0hBQThCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0dBSy9DLDhEQUFDSTs0RkFBSUosV0FBVTs7OEdBQ2IsOERBQUM1SCw0Q0FBS0E7b0dBQ0o0SCxXQUFXLDZCQUlWLE9BSENoRSxRQUFRM0IsU0FBUyxLQUFLLFVBQ2xCLG9CQUNBOzhHQUVQOzs7Ozs7OEdBR0QsOERBQUMxQiwrQ0FBTUE7b0dBQ0wwSSxTQUFTckYsUUFBUTNCLFNBQVMsS0FBSztvR0FDL0JpSCxpQkFBaUIsQ0FBQ0QsVUFDaEJwRCxjQUNFLGFBQ0FvRCxVQUFVLFNBQVM7b0dBR3ZCckIsV0FBVTs7Ozs7OzhHQUVaLDhEQUFDNUgsNENBQUtBO29HQUNKNEgsV0FBVyw2QkFJVixPQUhDaEUsUUFBUTNCLFNBQVMsS0FBSyxTQUNsQixvQkFDQTs4R0FFUDs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhGQU9MLDhEQUFDK0Y7b0ZBQUlKLFdBQVU7O3NHQUNiLDhEQUFDSTs0RkFBSUosV0FBVTs7OEdBQ2IsOERBQUM5RyxpUUFBVUE7b0dBQUM4RyxXQUFVOzs7Ozs7OEdBQ3RCLDhEQUFDNUgsNENBQUtBO29HQUFDNEgsV0FBVTs7d0dBQXNDO3dHQUVwRGhFLFFBQVEzQixTQUFTLEtBQUssU0FDbkIsa0JBQ0E7d0dBQU07Ozs7Ozs7Ozs7Ozs7c0dBS2QsOERBQUMrRjs0RkFBSUosV0FBVTs7OEdBQ2IsOERBQUNJO29HQUFJSixXQUFVOztzSEFDYiw4REFBQzVILDRDQUFLQTs0R0FBQzRILFdBQVU7c0hBQWlEOzs7Ozs7c0hBR2xFLDhEQUFDSTs0R0FBSUosV0FBVTs7OEhBQ2IsOERBQUNDO29IQUFLRCxXQUFVOzhIQUE0RTs7Ozs7OzhIQUc1Riw4REFBQzdILDRDQUFLQTtvSEFDSm9KLE1BQUs7b0hBQ0w3RCxPQUFPMUIsUUFBUW5CLFFBQVE7b0hBQ3ZCZ0YsVUFBVSxDQUFDa0IsSUFDVDlDLGNBQ0UsWUFDQXVELFNBQVNULEVBQUVDLE1BQU0sQ0FBQ3RELEtBQUssS0FBSztvSEFHaENzQyxXQUFVO29IQUNWYyxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEdBSWxCLDhEQUFDVjtvR0FBSUosV0FBVTs7c0hBQ2IsOERBQUM1SCw0Q0FBS0E7NEdBQUM0SCxXQUFVO3NIQUFpRDs7Ozs7O3NIQUdsRSw4REFBQ0k7NEdBQUlKLFdBQVU7OzhIQUNiLDhEQUFDQztvSEFBS0QsV0FBVTs4SEFBNEU7Ozs7Ozs4SEFHNUYsOERBQUM3SCw0Q0FBS0E7b0hBQ0pvSixNQUFLO29IQUNMN0QsT0FBTzFCLFFBQVFsQixRQUFRO29IQUN2QitFLFVBQVUsQ0FBQ2tCLElBQ1Q5QyxjQUNFLFlBQ0F1RCxTQUFTVCxFQUFFQyxNQUFNLENBQUN0RCxLQUFLLEtBQUs7b0hBR2hDc0MsV0FBVTtvSEFDVmMsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NHQU1wQiw4REFBQ1Y7NEZBQUlKLFdBQVU7OzhHQUNiLDhEQUFDSTtvR0FBSUosV0FBVTs7c0hBQ2IsOERBQUNDOztnSEFBSztnSEFBS0ssWUFBWXRFLFFBQVFuQixRQUFROzs7Ozs7O3NIQUN2Qyw4REFBQ29GOztnSEFBSztnSEFBS0ssWUFBWXRFLFFBQVFsQixRQUFROzs7Ozs7Ozs7Ozs7OzhHQUV6Qyw4REFBQ3BDLCtDQUFNQTtvR0FDTGdGLE9BQU87d0dBQUMxQixRQUFRbkIsUUFBUTt3R0FBRW1CLFFBQVFsQixRQUFRO3FHQUFDO29HQUMzQzJHLGVBQWU7NEdBQUMsQ0FBQ0MsS0FBS0MsSUFBSTt3R0FDeEIxRCxjQUFjLFlBQVl5RDt3R0FDMUJ6RCxjQUFjLFlBQVkwRDtvR0FDNUI7b0dBQ0FBLEtBQ0UzRixRQUFRM0IsU0FBUyxLQUFLLFNBQVMsT0FBTztvR0FFeENxSCxLQUFLO29HQUNMRSxNQUFNNUYsUUFBUTNCLFNBQVMsS0FBSyxTQUFTLEtBQUs7b0dBQzFDMkYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhFQVFwQiw4REFBQ0k7b0VBQUlKLFdBQVU7O3NGQUViLDhEQUFDdkksMENBQUlBOzRFQUFDdUksV0FBVTs7OEZBQ2QsOERBQUNySSxnREFBVUE7OEZBQ1QsNEVBQUNDLCtDQUFTQTt3RkFBQ29JLFdBQVU7OzBHQUNuQiw4REFBQ25HLGlRQUFLQTtnR0FBQ21HLFdBQVU7Ozs7OzswR0FDakIsOERBQUNDOzBHQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs4RkFHViw4REFBQ3ZJLGlEQUFXQTtvRkFBQ3NJLFdBQVU7O3NHQUNyQiw4REFBQ0k7NEZBQUlKLFdBQVU7OzhHQUNiLDhEQUFDSTtvR0FBSUosV0FBVTs7c0hBQ2IsOERBQUM1SCw0Q0FBS0E7NEdBQUM0SCxXQUFVO3NIQUFpRDs7Ozs7O3NIQUdsRSw4REFBQzdILDRDQUFLQTs0R0FDSm9KLE1BQUs7NEdBQ0w3RCxPQUFPMUIsUUFBUTFCLE9BQU87NEdBQ3RCdUYsVUFBVSxDQUFDa0IsSUFDVDlDLGNBQ0UsV0FDQXVELFNBQVNULEVBQUVDLE1BQU0sQ0FBQ3RELEtBQUssS0FBSzs0R0FHaENzQyxXQUFVOzRHQUNWYyxhQUFZOzs7Ozs7Ozs7Ozs7OEdBR2hCLDhEQUFDVjtvR0FBSUosV0FBVTs7c0hBQ2IsOERBQUM1SCw0Q0FBS0E7NEdBQUM0SCxXQUFVO3NIQUFpRDs7Ozs7O3NIQUdsRSw4REFBQzdILDRDQUFLQTs0R0FDSm9KLE1BQUs7NEdBQ0w3RCxPQUFPMUIsUUFBUXpCLE9BQU87NEdBQ3RCc0YsVUFBVSxDQUFDa0IsSUFDVDlDLGNBQ0UsV0FDQXVELFNBQVNULEVBQUVDLE1BQU0sQ0FBQ3RELEtBQUssS0FBSzs0R0FHaENzQyxXQUFVOzRHQUNWYyxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0dBS2xCLDhEQUFDVjs0RkFBSUosV0FBVTs7OEdBQ2IsOERBQUNJO29HQUFJSixXQUFVOztzSEFDYiw4REFBQ0M7O2dIQUNFakUsUUFBUTFCLE9BQU8sQ0FBQ2lHLGNBQWM7Z0hBQUc7Ozs7Ozs7c0hBRXBDLDhEQUFDTjs7Z0hBQ0VqRSxRQUFRekIsT0FBTyxDQUFDZ0csY0FBYztnSEFBRzs7Ozs7Ozs7Ozs7Ozs4R0FHdEMsOERBQUM3SCwrQ0FBTUE7b0dBQ0xnRixPQUFPO3dHQUFDMUIsUUFBUTFCLE9BQU87d0dBQUUwQixRQUFRekIsT0FBTztxR0FBQztvR0FDekNrSCxlQUFlOzRHQUFDLENBQUNDLEtBQUtDLElBQUk7d0dBQ3hCMUQsY0FBYyxXQUFXeUQ7d0dBQ3pCekQsY0FBYyxXQUFXMEQ7b0dBQzNCO29HQUNBQSxLQUFLO29HQUNMRCxLQUFLO29HQUNMRSxNQUFNO29HQUNONUIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NGQU9sQiw4REFBQ3ZJLDBDQUFJQTs0RUFBQ3VJLFdBQVU7OzhGQUNkLDhEQUFDckksZ0RBQVVBOzhGQUNULDRFQUFDQywrQ0FBU0E7d0ZBQUNvSSxXQUFVOzswR0FDbkIsOERBQUNsRyxpUUFBS0E7Z0dBQUNrRyxXQUFVOzs7Ozs7MEdBQ2pCLDhEQUFDQzswR0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEZBR1YsOERBQUN2SSxpREFBV0E7OEZBQ1YsNEVBQUMwSTt3RkFBSUosV0FBVTs7MEdBQ2IsOERBQUM1SCw0Q0FBS0E7Z0dBQUM0SCxXQUFVOzBHQUFpRDs7Ozs7OzBHQUdsRSw4REFBQzNILCtDQUFNQTtnR0FDTHFGLE9BQU8xQixRQUFRcEIsY0FBYztnR0FDN0I2RyxlQUFlLENBQUMvRCxRQUNkTyxjQUFjLGtCQUFrQlA7O2tIQUdsQyw4REFBQ2xGLHNEQUFhQTt3R0FBQ3dILFdBQVU7a0hBQ3ZCLDRFQUFDdkgsb0RBQVdBOzRHQUFDcUksYUFBWTs7Ozs7Ozs7Ozs7a0hBRTNCLDhEQUFDeEksc0RBQWFBO2tIQUNYNkMsc0JBQXNCK0UsR0FBRyxDQUFDLENBQUNDLHVCQUMxQiw4REFBQzVILG1EQUFVQTtnSEFFVG1GLE9BQU95QyxPQUFPekMsS0FBSzswSEFFbEJ5QyxPQUFPeEMsS0FBSzsrR0FIUndDLE9BQU96QyxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBY2pDLDhEQUFDMEM7O3NGQUNDLDhEQUFDQTs0RUFBSUosV0FBVTs7OEZBQ2IsOERBQUNoRyxpUUFBUUE7b0ZBQUNnRyxXQUFVOzs7Ozs7OEZBQ3BCLDhEQUFDNkI7b0ZBQUc3QixXQUFVOzhGQUEwQjs7Ozs7Ozs7Ozs7O3NGQUsxQyw4REFBQ0k7NEVBQUlKLFdBQVU7OzhGQUViLDhEQUFDTjtvRkFDQy9CLE9BQU07b0ZBQ05tQyxNQUFNL0YsaVFBQU1BO29GQUNaNEYsU0FBU3JDO29GQUNUc0MsUUFBUTVELFFBQVF4QixpQkFBaUI7b0ZBQ2pDcUYsVUFBVSxDQUFDRCxTQUNUM0IsY0FBYyxxQkFBcUIyQjs7Ozs7OzhGQUt2Qyw4REFBQ0Y7b0ZBQ0MvQixPQUFNO29GQUNObUMsTUFBTWhILGlRQUFTQTtvRkFDZjZHLFNBQVNwQztvRkFDVHFDLFFBQVE1RCxRQUFRdkIsU0FBUztvRkFDekJvRixVQUFVLENBQUNELFNBQ1QzQixjQUFjLGFBQWEyQjs7Ozs7OzhGQUsvQiw4REFBQ0Y7b0ZBQ0MvQixPQUFNO29GQUNObUMsTUFBTXZHLGlRQUFHQTtvRkFDVG9HLFNBQVNuQztvRkFDVG9DLFFBQVE1RCxRQUFRdEIsUUFBUTtvRkFDeEJtRixVQUFVLENBQUNELFNBQ1QzQixjQUFjLFlBQVkyQjs7Ozs7OzhGQUs5Qiw4REFBQ0Y7b0ZBQ0MvQixPQUFNO29GQUNObUMsTUFBTTdGLGlRQUFZQTtvRkFDbEIwRixTQUFTbEM7b0ZBQ1RtQyxRQUFRNUQsUUFBUXJCLFdBQVc7b0ZBQzNCa0YsVUFBVSxDQUFDRCxTQUNUM0IsY0FBYyxlQUFlMkI7Ozs7Ozs7Ozs7Ozs7Ozs7OztnRUFPcEN2QixrQ0FDQyw4REFBQzVHLDBDQUFJQTtvRUFBQ3VJLFdBQVU7OEVBQ2QsNEVBQUN0SSxpREFBV0E7d0VBQUNzSSxXQUFVO2tGQUNyQiw0RUFBQ0k7NEVBQUlKLFdBQVU7OzhGQUNiLDhEQUFDSTs7c0dBQ0MsOERBQUMwQjs0RkFBRzlCLFdBQVU7c0dBQStCOzs7Ozs7c0dBRzdDLDhEQUFDYTs0RkFBRWIsV0FBVTs7Z0dBQ1YxQjtnR0FBa0I7Z0dBQW1CO2dHQUNyQ1csaUJBQWlCekMsTUFBTTtnR0FBQzs7Ozs7Ozs7Ozs7Ozs4RkFJN0IsOERBQUNqRiw4Q0FBTUE7b0ZBQ0xtSixTQUFRO29GQUNSTCxTQUFTakM7b0ZBQ1Q0QixXQUFVOztzR0FFViw4REFBQ3RHLGlRQUFTQTs0RkFBQ3NHLFdBQVU7Ozs7Ozt3RkFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQVl0RCw4REFBQzNILCtDQUFNQTs0Q0FBQ3FGLE9BQU9sQzs0Q0FBUWlHLGVBQWVoRzs7OERBQ3BDLDhEQUFDakQsc0RBQWFBO29EQUFDd0gsV0FBVTs4REFDdkIsNEVBQUN2SCxvREFBV0E7d0RBQUNxSSxhQUFZOzs7Ozs7Ozs7Ozs4REFFM0IsOERBQUN4SSxzREFBYUE7O3NFQUNaLDhEQUFDQyxtREFBVUE7NERBQUNtRixPQUFNO3NFQUFXOzs7Ozs7c0VBQzdCLDhEQUFDbkYsbURBQVVBOzREQUFDbUYsT0FBTTtzRUFBWTs7Ozs7O3NFQUM5Qiw4REFBQ25GLG1EQUFVQTs0REFBQ21GLE9BQU07c0VBQWE7Ozs7OztzRUFDL0IsOERBQUNuRixtREFBVUE7NERBQUNtRixPQUFNO3NFQUFhOzs7Ozs7c0VBQy9CLDhEQUFDbkYsbURBQVVBOzREQUFDbUYsT0FBTTtzRUFBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUtqQyw4REFBQzBDOzRDQUFJSixXQUFVOzs4REFDYiw4REFBQ3pJLDhDQUFNQTtvREFDTG1KLFNBQVNwRixhQUFhLFNBQVMsWUFBWTtvREFDM0NxRixNQUFLO29EQUNMTixTQUFTLElBQU05RSxZQUFZO29EQUMzQnlFLFdBQVcsZ0JBSVYsT0FIQzFFLGFBQWEsU0FDVCw2QkFDQTs4REFHTiw0RUFBQ25DLGlRQUFPQTt3REFBQzZHLFdBQVU7Ozs7Ozs7Ozs7OzhEQUVyQiw4REFBQ3pJLDhDQUFNQTtvREFDTG1KLFNBQVNwRixhQUFhLFNBQVMsWUFBWTtvREFDM0NxRixNQUFLO29EQUNMTixTQUFTLElBQU05RSxZQUFZO29EQUMzQnlFLFdBQVcsZ0JBSVYsT0FIQzFFLGFBQWEsU0FDVCw2QkFDQTs4REFHTiw0RUFBQ2xDLGlRQUFJQTt3REFBQzRHLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQU92QjNCLGtDQUNDLDhEQUFDK0I7NEJBQUlKLFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBS0QsV0FBVTs4Q0FBOEI7Ozs7OztnQ0FHN0NoRSxRQUFRM0IsU0FBUyxLQUFLLHlCQUNyQiw4REFBQzdDLDRDQUFLQTtvQ0FDSmtKLFNBQVE7b0NBQ1JWLFdBQVU7O3dDQUNYO3NEQUVDLDhEQUFDdkcsaVFBQUNBOzRDQUNBdUcsV0FBVTs0Q0FDVkssU0FBUyxJQUFNcEMsY0FBYyxhQUFhOzs7Ozs7Ozs7Ozs7Z0NBSTlDakMsQ0FBQUEsUUFBUTFCLE9BQU8sS0FBSyxLQUFLMEIsUUFBUXpCLE9BQU8sS0FBSyxJQUFHLG1CQUNoRCw4REFBQy9DLDRDQUFLQTtvQ0FDSmtKLFNBQVE7b0NBQ1JWLFdBQVU7O3dDQUNYO3dDQUNRaEUsUUFBUTFCLE9BQU87d0NBQUM7d0NBQUUwQixRQUFRekIsT0FBTzt3Q0FBQztzREFDekMsOERBQUNkLGlRQUFDQTs0Q0FDQXVHLFdBQVU7NENBQ1ZLLFNBQVM7Z0RBQ1BwQyxjQUFjLFdBQVc7Z0RBQ3pCQSxjQUFjLFdBQVc7NENBQzNCOzs7Ozs7Ozs7Ozs7Z0NBSUxqQyxRQUFReEIsaUJBQWlCLENBQUMwRixHQUFHLENBQUMsQ0FBQ2xCLHVCQUM5Qiw4REFBQ3hILDRDQUFLQTt3Q0FFSmtKLFNBQVE7d0NBQ1JWLFdBQVU7OzRDQUVUaEI7MERBQ0QsOERBQUN2RixpUUFBQ0E7Z0RBQ0F1RyxXQUFVO2dEQUNWSyxTQUFTLElBQ1BwQyxjQUNFLHFCQUNBakMsUUFBUXhCLGlCQUFpQixDQUFDdUQsTUFBTSxDQUFDLENBQUNnRSxJQUFNQSxNQUFNL0M7Ozs7Ozs7dUNBVi9DQTs7Ozs7Z0NBZ0JSaEQsUUFBUXZCLFNBQVMsQ0FBQ3lGLEdBQUcsQ0FBQyxDQUFDcUIscUJBQ3RCLDhEQUFDL0osNENBQUtBO3dDQUVKa0osU0FBUTt3Q0FDUlYsV0FBVTs7NENBRVR1QjswREFDRCw4REFBQzlILGlRQUFDQTtnREFDQXVHLFdBQVU7Z0RBQ1ZLLFNBQVMsSUFDUHBDLGNBQ0UsYUFDQWpDLFFBQVF2QixTQUFTLENBQUNzRCxNQUFNLENBQUMsQ0FBQ2lFLElBQU1BLE1BQU1UOzs7Ozs7O3VDQVZ2Q0E7Ozs7O2dDQWdCUnZGLFFBQVF0QixRQUFRLENBQUN3RixHQUFHLENBQUMsQ0FBQytCLHdCQUNyQiw4REFBQ3pLLDRDQUFLQTt3Q0FFSmtKLFNBQVE7d0NBQ1JWLFdBQVU7OzRDQUVUaUM7MERBQ0QsOERBQUN4SSxpUUFBQ0E7Z0RBQ0F1RyxXQUFVO2dEQUNWSyxTQUFTLElBQ1BwQyxjQUNFLFlBQ0FqQyxRQUFRdEIsUUFBUSxDQUFDcUQsTUFBTSxDQUFDLENBQUNzQixJQUFNQSxNQUFNNEM7Ozs7Ozs7dUNBVnRDQTs7Ozs7Z0NBZ0JSakcsUUFBUXJCLFdBQVcsQ0FBQ3VGLEdBQUcsQ0FBQyxDQUFDbEIsdUJBQ3hCLDhEQUFDeEgsNENBQUtBO3dDQUVKa0osU0FBUTt3Q0FDUlYsV0FBVTs7NENBRVRoQjswREFDRCw4REFBQ3ZGLGlRQUFDQTtnREFDQXVHLFdBQVU7Z0RBQ1ZLLFNBQVMsSUFDUHBDLGNBQ0UsZUFDQWpDLFFBQVFyQixXQUFXLENBQUNvRCxNQUFNLENBQUMsQ0FBQ2dFLElBQU1BLE1BQU0vQzs7Ozs7Ozt1Q0FWekNBOzs7OztnQ0FnQlJoRCxRQUFRcEIsY0FBYyxLQUFLLHVCQUMxQiw4REFBQ3BELDRDQUFLQTtvQ0FDSmtKLFNBQVE7b0NBQ1JWLFdBQVU7O3lDQUdSN0UsOEJBQUFBLHNCQUFzQitHLElBQUksQ0FDeEIsQ0FBQ0MsTUFBUUEsSUFBSXpFLEtBQUssS0FBSzFCLFFBQVFwQixjQUFjLGVBRC9DTyxrREFBQUEsNEJBRUd3QyxLQUFLO3NEQUVWLDhEQUFDbEUsaVFBQUNBOzRDQUNBdUcsV0FBVTs0Q0FDVkssU0FBUyxJQUFNcEMsY0FBYyxrQkFBa0I7Ozs7Ozs7Ozs7Ozs4Q0FJckQsOERBQUMxRyw4Q0FBTUE7b0NBQ0xtSixTQUFRO29DQUNSQyxNQUFLO29DQUNMTixTQUFTakM7b0NBQ1Q0QixXQUFVOzhDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTVCw4REFBQ29DO2dCQUFLcEMsV0FBVTs7b0JBRWI1RCx5QkFDQyw4REFBQ2dFO3dCQUFJSixXQUFVO2tDQUNaNUMsTUFBTWlGLElBQUksQ0FBQzs0QkFBRTdGLFFBQVE7d0JBQUUsR0FBRzBELEdBQUcsQ0FBQyxDQUFDb0MsR0FBR0Msa0JBQ2pDLDhEQUFDbEwsOERBQW9CQSxNQUFNa0w7Ozs7Ozs7Ozs7b0JBTWhDakcsU0FBUyxDQUFDRix5QkFDVCw4REFBQzlFLHVEQUFVQTt3QkFBQ2tMLFNBQVMsSUFBTUM7d0JBQW1CdkYsU0FBU1o7Ozs7OztvQkFJeEQsQ0FBQ0YsV0FBVyxDQUFDRSxTQUFTMkMsaUJBQWlCekMsTUFBTSxLQUFLLGtCQUNqRCw4REFBQzREO3dCQUFJSixXQUFVOzswQ0FDYiw4REFBQ2xILGlRQUFTQTtnQ0FBQ2tILFdBQVU7Ozs7OzswQ0FDckIsOERBQUM2QjtnQ0FBRzdCLFdBQVU7MENBQStCOzs7Ozs7MENBRzdDLDhEQUFDYTtnQ0FBRWIsV0FBVTswQ0FBc0I7Ozs7OzswQ0FJbkMsOERBQUN6SSw4Q0FBTUE7Z0NBQ0w4SSxTQUFTakM7Z0NBQ1Q0QixXQUFVOzBDQUNYOzs7Ozs7Ozs7Ozs2Q0FLSDs7NEJBRUcxRSxhQUFhLHdCQUNaLDhEQUFDOEU7Z0NBQUlKLFdBQVU7MENBQ1pmLGlCQUFpQmlCLEdBQUcsQ0FBQyxDQUFDdkIseUJBQ3JCLDhEQUFDbEgsMENBQUlBO3dDQUVIdUksV0FBVTt3Q0FDVkssU0FBUyxJQUFNckYsZ0JBQWdCMkQ7OzBEQUUvQiw4REFBQ3lCO2dEQUFJSixXQUFVOztrRUFDYiw4REFBQzdGLHdFQUFpQkE7d0RBQ2hCdUksS0FDRS9ELFNBQVNnRSxlQUFlLEdBQ3BCLENBQUM7NERBQ0MsSUFBSTtnRUFDRixPQUFPQyxLQUFLQyxLQUFLLENBQUNsRSxTQUFTZ0UsZUFBZSxFQUN2Q0csR0FBRzs0REFDUixFQUFFLFVBQU07Z0VBQ04sT0FBTzs0REFDVDt3REFDRixPQUNBO3dEQUVOQyxLQUFLcEUsU0FBU0MsSUFBSSxJQUFJO3dEQUN0Qm9CLFdBQVU7Ozs7OztvREFFWHJCLFNBQVNjLGtCQUFrQixrQkFDMUIsOERBQUNqSSw0Q0FBS0E7d0RBQUN3SSxXQUFVO2tFQUE4Qzs7Ozs7O2tFQUlqRSw4REFBQ3hJLDRDQUFLQTt3REFBQ3dJLFdBQVU7a0VBQ2RyQixTQUFTSyxNQUFNLElBQUk7Ozs7Ozs7Ozs7OzswREFHeEIsOERBQUN0SCxpREFBV0E7Z0RBQUNzSSxXQUFVOzBEQUNyQiw0RUFBQ0k7b0RBQUlKLFdBQVU7O3NFQUNiLDhEQUFDSTs7OEVBQ0MsOERBQUN5QjtvRUFBRzdCLFdBQVU7OEVBQ1hyQixTQUFTQyxJQUFJLElBQUk7Ozs7Ozs4RUFFcEIsOERBQUN3QjtvRUFBSUosV0FBVTs7c0ZBQ2IsOERBQUNqSCxpUUFBTUE7NEVBQUNpSCxXQUFVOzs7Ozs7c0ZBQ2xCLDhEQUFDQzs0RUFBS0QsV0FBVTtzRkFDYnJCLFNBQVNHLElBQUksSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQUt4Qiw4REFBQ3NCOzREQUFJSixXQUFVOzs4RUFDYiw4REFBQ0k7b0VBQUlKLFdBQVU7OEVBQ1pyQixTQUFTVyxTQUFTLElBQUlYLFNBQVNxRSxTQUFTLEdBQ3JDckUsU0FBU1csU0FBUyxLQUFLWCxTQUFTcUUsU0FBUyxHQUN2QyxHQUVJckUsT0FERkEsU0FBU3NFLGNBQWMsSUFBSSxPQUM1QixLQUF1QyxPQUFwQ3RFLFNBQVNXLFNBQVMsQ0FBQ2lCLGNBQWMsTUFDckMsR0FFSTVCLE9BREZBLFNBQVNzRSxjQUFjLElBQUksT0FDNUIsS0FBNEN0RSxPQUF6Q0EsU0FBU1csU0FBUyxDQUFDaUIsY0FBYyxJQUFHLE9BQXlDLE9BQXBDNUIsU0FBU3FFLFNBQVMsQ0FBQ3pDLGNBQWMsTUFDaEY1QixTQUFTVyxTQUFTLEdBQ2xCLFFBRUlYLE9BREZBLFNBQVNzRSxjQUFjLElBQUksT0FDNUIsS0FBdUMsT0FBcEN0RSxTQUFTVyxTQUFTLENBQUNpQixjQUFjLE1BQ3JDNUIsU0FBU3FFLFNBQVMsR0FDbEIsU0FFSXJFLE9BREZBLFNBQVNzRSxjQUFjLElBQUksT0FDNUIsS0FBdUMsT0FBcEN0RSxTQUFTcUUsU0FBUyxDQUFDekMsY0FBYyxNQUNyQzs7Ozs7OzhFQUVOLDhEQUFDL0ksNENBQUtBO29FQUNKa0osU0FBUTtvRUFDUlYsV0FBVTs4RUFFVHJCLFNBQVN1RSxXQUFXLElBQUk7Ozs7Ozs7Ozs7OztzRUFJN0IsOERBQUM5Qzs0REFBSUosV0FBVTs7OEVBQ2IsOERBQUNJO29FQUFJSixXQUFVOztzRkFDYiw4REFBQ3hHLGlRQUFNQTs0RUFBQ3dHLFdBQVU7Ozs7OztzRkFDbEIsOERBQUNJOzRFQUFJSixXQUFVO3NGQUNackIsU0FBU3dFLFNBQVMsSUFBSTs7Ozs7Ozs7Ozs7OzhFQUczQiw4REFBQy9DO29FQUFJSixXQUFVOztzRkFDYiw4REFBQ2hILGlRQUFRQTs0RUFBQ2dILFdBQVU7Ozs7OztzRkFDcEIsOERBQUNJOzRFQUFJSixXQUFVO3NGQUNackIsU0FBU0ssTUFBTSxJQUFJOzs7Ozs7Ozs7Ozs7OEVBR3hCLDhEQUFDb0I7b0VBQUlKLFdBQVU7O3NGQUNiLDhEQUFDOUYsaVFBQU1BOzRFQUFDOEYsV0FBVTs7Ozs7O3NGQUNsQiw4REFBQ0k7NEVBQUlKLFdBQVU7c0ZBQ1pyQixTQUFTeUUsVUFBVSxHQUFHLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRUFLeEMsOERBQUNoRDs0REFBSUosV0FBVTs7OEVBQ2IsOERBQUNJO29FQUFJSixXQUFVOztzRkFDYiw4REFBQ2hILGlRQUFRQTs0RUFBQ2dILFdBQVU7Ozs7Ozt3RUFDbkJyQixTQUFTWSxtQkFBbUIsR0FDekIsSUFBSThELEtBQ0YxRSxTQUFTWSxtQkFBbUIsRUFDNUIrRCxrQkFBa0IsS0FDcEIzRSxTQUFTNEUsYUFBYSxHQUN0QixrQkFDQTs7Ozs7Ozs4RUFFTiw4REFBQ25EO29FQUFJSixXQUFVOzhFQUNackIsU0FBU0ksU0FBUyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dUNBekcxQkosU0FBUzZFLEVBQUU7Ozs7Ozs7Ozs7NEJBb0h2QmxJLGFBQWEsd0JBQ1osOERBQUM4RTtnQ0FBSUosV0FBVTswQ0FDWmYsaUJBQWlCaUIsR0FBRyxDQUFDLENBQUN2Qix5QkFDckIsOERBQUNsSCwwQ0FBSUE7d0NBRUh1SSxXQUFVO3dDQUNWSyxTQUFTLElBQU1yRixnQkFBZ0IyRDtrREFFL0IsNEVBQUNqSCxpREFBV0E7NENBQUNzSSxXQUFVO3NEQUNyQiw0RUFBQ0k7Z0RBQUlKLFdBQVU7O2tFQUNiLDhEQUFDSTt3REFBSUosV0FBVTs7MEVBQ2IsOERBQUM3Rix3RUFBaUJBO2dFQUNoQnVJLEtBQ0UvRCxTQUFTZ0UsZUFBZSxHQUNwQixDQUFDO29FQUNDLElBQUk7d0VBQ0YsT0FBT0MsS0FBS0MsS0FBSyxDQUNmbEUsU0FBU2dFLGVBQWUsRUFDeEJHLEdBQUc7b0VBQ1AsRUFBRSxVQUFNO3dFQUNOLE9BQU87b0VBQ1Q7Z0VBQ0YsT0FDQTtnRUFFTkMsS0FBS3BFLFNBQVNDLElBQUksSUFBSTtnRUFDdEJvQixXQUFVOzs7Ozs7NERBRVhyQixTQUFTYyxrQkFBa0Isa0JBQzFCLDhEQUFDakksNENBQUtBO2dFQUFDd0ksV0FBVTswRUFBOEM7Ozs7Ozs7Ozs7OztrRUFLbkUsOERBQUNJO3dEQUFJSixXQUFVOzswRUFDYiw4REFBQ0k7Z0VBQUlKLFdBQVU7O2tGQUNiLDhEQUFDSTs7MEZBQ0MsOERBQUN5QjtnRkFBRzdCLFdBQVU7MEZBQ1hyQixTQUFTQyxJQUFJLElBQUk7Ozs7OzswRkFFcEIsOERBQUN3QjtnRkFBSUosV0FBVTs7a0dBQ2IsOERBQUNqSCxpUUFBTUE7d0ZBQUNpSCxXQUFVOzs7Ozs7a0dBQ2xCLDhEQUFDQztrR0FBTXRCLFNBQVNHLElBQUksSUFBSTs7Ozs7O2tHQUN4Qiw4REFBQ21CO3dGQUFLRCxXQUFVO2tHQUFPOzs7Ozs7a0dBQ3ZCLDhEQUFDQztrR0FBTXRCLFNBQVNJLFNBQVMsSUFBSTs7Ozs7Ozs7Ozs7OzBGQUUvQiw4REFBQ3FCO2dGQUFJSixXQUFVOztrR0FDYiw4REFBQ0k7d0ZBQUlKLFdBQVU7OzBHQUNiLDhEQUFDeEcsaVFBQU1BO2dHQUFDd0csV0FBVTs7Ozs7OzRGQUNqQnJCLFNBQVN3RSxTQUFTLElBQUk7Ozs7Ozs7a0dBRXpCLDhEQUFDL0M7d0ZBQUlKLFdBQVU7OzBHQUNiLDhEQUFDaEgsaVFBQVFBO2dHQUFDZ0gsV0FBVTs7Ozs7OzRGQUNuQnJCLFNBQVNLLE1BQU0sSUFBSTs7Ozs7OztrR0FFdEIsOERBQUNvQjt3RkFBSUosV0FBVTs7MEdBQ2IsOERBQUM5RixpUUFBTUE7Z0dBQUM4RixXQUFVOzs7Ozs7NEZBQ2pCckIsU0FBU3lFLFVBQVUsR0FBRyxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tGQUl4Qyw4REFBQ2hEO3dFQUFJSixXQUFVOzswRkFDYiw4REFBQ0k7Z0ZBQUlKLFdBQVU7MEZBQ1pyQixTQUFTVyxTQUFTLElBQUlYLFNBQVNxRSxTQUFTLEdBQ3JDckUsU0FBU1csU0FBUyxLQUFLWCxTQUFTcUUsU0FBUyxHQUN2QyxHQUVJckUsT0FERkEsU0FBU3NFLGNBQWMsSUFBSSxPQUM1QixLQUF1QyxPQUFwQ3RFLFNBQVNXLFNBQVMsQ0FBQ2lCLGNBQWMsTUFDckMsR0FFSTVCLE9BREZBLFNBQVNzRSxjQUFjLElBQUksT0FDNUIsS0FBNEN0RSxPQUF6Q0EsU0FBU1csU0FBUyxDQUFDaUIsY0FBYyxJQUFHLE9BQXlDLE9BQXBDNUIsU0FBU3FFLFNBQVMsQ0FBQ3pDLGNBQWMsTUFDaEY1QixTQUFTVyxTQUFTLEdBQ2xCLFFBRUlYLE9BREZBLFNBQVNzRSxjQUFjLElBQUksT0FDNUIsS0FBdUMsT0FBcEN0RSxTQUFTVyxTQUFTLENBQUNpQixjQUFjLE1BQ3JDNUIsU0FBU3FFLFNBQVMsR0FDbEIsU0FFSXJFLE9BREZBLFNBQVNzRSxjQUFjLElBQUksT0FDNUIsS0FBdUMsT0FBcEN0RSxTQUFTcUUsU0FBUyxDQUFDekMsY0FBYyxNQUNyQzs7Ozs7OzBGQUVOLDhEQUFDL0ksNENBQUtBO2dGQUNKa0osU0FBUTtnRkFDUlYsV0FBVTswRkFFVHJCLFNBQVN1RSxXQUFXLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRUFJL0IsOERBQUM5QztnRUFBSUosV0FBVTswRUFDYiw0RUFBQ0k7b0VBQUlKLFdBQVU7O3NGQUNiLDhEQUFDSTs0RUFBSUosV0FBVTs7OEZBQ2IsOERBQUNoSCxpUUFBUUE7b0ZBQUNnSCxXQUFVOzs7Ozs7Z0ZBQ25CckIsU0FBU1ksbUJBQW1CLEdBQ3pCLElBQUk4RCxLQUNGMUUsU0FBU1ksbUJBQW1CLEVBQzVCK0Qsa0JBQWtCLEtBQ3BCM0UsU0FBUzRFLGFBQWEsR0FDdEIsa0JBQ0E7Ozs7Ozs7c0ZBRU4sOERBQUMvTCw0Q0FBS0E7NEVBQUN3SSxXQUFVO3NGQUNkckIsU0FBU0ssTUFBTSxJQUFJOzs7Ozs7c0ZBRXRCLDhEQUFDaUI7c0ZBQU10QixTQUFTSSxTQUFTLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dUNBcEdsQ0osU0FBUzZFLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQW1IcEM7R0F4cENnQnpJO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL0FsbFByb3BlcnRpZXNQYWdlLnRzeD9iOGRiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VNZW1vLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBMb2FkaW5nU3Bpbm5lciB9IGZyb20gXCIuL3VpL2xvYWRpbmctc3Bpbm5lclwiO1xuaW1wb3J0IHsgUHJvcGVydHlDYXJkU2tlbGV0b24gfSBmcm9tIFwiLi91aS9za2VsZXRvblwiO1xuaW1wb3J0IHsgRXJyb3JTdGF0ZSB9IGZyb20gXCIuL3VpL2VtcHR5LXN0YXRlXCI7XG5pbXBvcnQgeyBQcm9wZXJ0eUZpbHRlcnMgfSBmcm9tIFwiLi9Qcm9wZXJ0eUZpbHRlcnNcIjtcbmltcG9ydCB7IFByb3BlcnR5TGlzdGluZ3MgfSBmcm9tIFwiLi9Qcm9wZXJ0eUxpc3RpbmdzXCI7XG5pbXBvcnQgeyBGZWF0dXJlZFByb2plY3RzIH0gZnJvbSBcIi4vRmVhdHVyZWRQcm9qZWN0c1wiO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIi4vdWkvYnV0dG9uXCI7XG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCIuL3VpL2JhZGdlXCI7XG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSBcIi4vdWkvY2FyZFwiO1xuaW1wb3J0IHtcbiAgRGlhbG9nLFxuICBEaWFsb2dDb250ZW50LFxuICBEaWFsb2dEZXNjcmlwdGlvbixcbiAgRGlhbG9nSGVhZGVyLFxuICBEaWFsb2dUaXRsZSxcbiAgRGlhbG9nVHJpZ2dlcixcbn0gZnJvbSBcIi4vdWkvZGlhbG9nXCI7XG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCIuL3VpL2lucHV0XCI7XG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gXCIuL3VpL2xhYmVsXCI7XG5pbXBvcnQge1xuICBTZWxlY3QsXG4gIFNlbGVjdENvbnRlbnQsXG4gIFNlbGVjdEl0ZW0sXG4gIFNlbGVjdFRyaWdnZXIsXG4gIFNlbGVjdFZhbHVlLFxufSBmcm9tIFwiLi91aS9zZWxlY3RcIjtcbmltcG9ydCB7IFNsaWRlciB9IGZyb20gXCIuL3VpL3NsaWRlclwiO1xuaW1wb3J0IHsgU2VwYXJhdG9yIH0gZnJvbSBcIi4vdWkvc2VwYXJhdG9yXCI7XG5pbXBvcnQgeyBTd2l0Y2ggfSBmcm9tIFwiLi91aS9zd2l0Y2hcIjtcbmltcG9ydCB7XG4gIEhvbWUsXG4gIENoZXZyb25SaWdodCxcbiAgQnVpbGRpbmcyLFxuICBNYXBQaW4sXG4gIENhbGVuZGFyLFxuICBEb2xsYXJTaWduLFxuICBUcmVuZGluZ1VwLFxuICBGaWx0ZXIsXG4gIEdyaWQzWDMsXG4gIExpc3QsXG4gIFNlYXJjaCxcbiAgU2xpZGVyc0hvcml6b250YWwsXG4gIFN0YXIsXG4gIEJlZCxcbiAgQmF0aCxcbiAgU3F1YXJlLFxuICBBcnJvd1VwRG93bixcbiAgWCxcbiAgUm90YXRlQ2N3LFxuICBDaGVjayxcbiAgQmFua25vdGUsXG4gIFJ1bGVyLFxuICBDbG9jayxcbiAgSGFtbWVyLFxuICBIb21lIGFzIEhvbWVJY29uLFxuICBVc2VycyxcbiAgU2hvcHBpbmdDYXJ0LFxuICBTaGllbGQsXG59IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcbmltcG9ydCB7IEltYWdlV2l0aEZhbGxiYWNrIH0gZnJvbSBcIi4vZmlnbWEvSW1hZ2VXaXRoRmFsbGJhY2tcIjtcblxuaW50ZXJmYWNlIEFsbFByb3BlcnRpZXNQYWdlUHJvcHMge1xuICBvblByb2plY3RTZWxlY3Q6IChwcm9qZWN0OiBhbnkpID0+IHZvaWQ7XG4gIG9uQmFjazogKCkgPT4gdm9pZDtcbiAgc2VsZWN0ZWREZXZlbG9wZXI/OiBhbnk7XG59XG5cbmludGVyZmFjZSBGaWx0ZXJTdGF0ZSB7XG4gIHByaWNlVW5pdDogXCJ0b3RhbFwiIHwgXCJzcWZ0XCI7XG4gIG1pbkFyZWE6IG51bWJlcjtcbiAgbWF4QXJlYTogbnVtYmVyO1xuICBkZXZlbG9wbWVudFN0YXR1czogc3RyaW5nW107XG4gIHVuaXRUeXBlczogc3RyaW5nW107XG4gIGJlZHJvb21zOiBzdHJpbmdbXTtcbiAgc2FsZXNTdGF0dXM6IHN0cmluZ1tdO1xuICBjb21wbGV0aW9uRGF0ZTogc3RyaW5nO1xuICBtaW5QcmljZTogbnVtYmVyO1xuICBtYXhQcmljZTogbnVtYmVyO1xufVxuXG5jb25zdCBpbml0aWFsRmlsdGVyczogRmlsdGVyU3RhdGUgPSB7XG4gIHByaWNlVW5pdDogXCJ0b3RhbFwiLFxuICBtaW5BcmVhOiAwLFxuICBtYXhBcmVhOiA1MDAwLFxuICBkZXZlbG9wbWVudFN0YXR1czogW10sXG4gIHVuaXRUeXBlczogW10sXG4gIGJlZHJvb21zOiBbXSxcbiAgc2FsZXNTdGF0dXM6IFtdLFxuICBjb21wbGV0aW9uRGF0ZTogXCJhbGxcIixcbiAgbWluUHJpY2U6IDAsXG4gIG1heFByaWNlOiAxMDAwMDAwMCxcbn07XG5cbmV4cG9ydCBmdW5jdGlvbiBBbGxQcm9wZXJ0aWVzUGFnZSh7XG4gIG9uUHJvamVjdFNlbGVjdCxcbiAgb25CYWNrLFxuICBzZWxlY3RlZERldmVsb3Blcixcbn06IEFsbFByb3BlcnRpZXNQYWdlUHJvcHMpIHtcbiAgY29uc29sZS5sb2coXCLwn46vIEFsbFByb3BlcnRpZXNQYWdlIGNvbXBvbmVudCByZW5kZXJlZFwiKTtcblxuICBjb25zdCBbdmlld01vZGUsIHNldFZpZXdNb2RlXSA9IHVzZVN0YXRlPFwiZ3JpZFwiIHwgXCJsaXN0XCI+KFwiZ3JpZFwiKTtcbiAgY29uc3QgW3NvcnRCeSwgc2V0U29ydEJ5XSA9IHVzZVN0YXRlKFwiZmVhdHVyZWRcIik7XG4gIGNvbnN0IFtzZWFyY2hRdWVyeSwgc2V0U2VhcmNoUXVlcnldID0gdXNlU3RhdGUoXCJcIik7XG4gIGNvbnN0IFtmaWx0ZXJzT3Blbiwgc2V0RmlsdGVyc09wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNGaWx0ZXJNb2RhbE9wZW4sIHNldElzRmlsdGVyTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2ZpbHRlcnMsIHNldEZpbHRlcnNdID0gdXNlU3RhdGU8RmlsdGVyU3RhdGU+KGluaXRpYWxGaWx0ZXJzKTtcblxuICAvLyBMb2NhbCBzdGF0ZSBmb3IgcHJvcGVydGllc1xuICBjb25zdCBbcHJvcGVydGllcywgc2V0UHJvcGVydGllc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgY29uc29sZS5sb2coXCLwn5SNIEN1cnJlbnQgcHJvcGVydGllcyBzdGF0ZTpcIiwgcHJvcGVydGllcy5sZW5ndGgsIFwiaXRlbXNcIik7XG5cbiAgLy8gVXNlIHVzZUVmZmVjdCB0byBmZXRjaCBwcm9wZXJ0aWVzIG9uIG1vdW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHByb3BlcnRpZXMubGVuZ3RoID09PSAwICYmICFsb2FkaW5nICYmICFlcnJvcikge1xuICAgICAgY29uc29sZS5sb2coXCLwn5qAIHVzZUVmZmVjdDogVHJpZ2dlcmluZyBmZXRjaCBzaW5jZSBwcm9wZXJ0aWVzIGlzIGVtcHR5XCIpO1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIGZldGNoKFwiL2FwaS9wcm9wZXJ0aWVzXCIpXG4gICAgICAgIC50aGVuKChyZXNwb25zZSkgPT4gcmVzcG9uc2UuanNvbigpKVxuICAgICAgICAudGhlbigoZGF0YSkgPT4ge1xuICAgICAgICAgIGNvbnNvbGUubG9nKFwi4pyFIHVzZUVmZmVjdCBmZXRjaCBzdWNjZXNzZnVsOlwiLCBkYXRhKTtcbiAgICAgICAgICBpZiAoZGF0YS5zdWNjZXNzICYmIGRhdGEuZGF0YSAmJiBkYXRhLmRhdGEuaXRlbXMpIHtcbiAgICAgICAgICAgIHNldFByb3BlcnRpZXMoZGF0YS5kYXRhLml0ZW1zKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwi4pyFIFByb3BlcnRpZXMgc2V0OlwiLCBkYXRhLmRhdGEuaXRlbXMubGVuZ3RoLCBcIml0ZW1zXCIpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgfSlcbiAgICAgICAgLmNhdGNoKChlcnIpID0+IHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFwi4p2MIHVzZUVmZmVjdCBmZXRjaCBlcnJvcjpcIiwgZXJyKTtcbiAgICAgICAgICBzZXRFcnJvcihlcnIubWVzc2FnZSk7XG4gICAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgfSwgW3Byb3BlcnRpZXMubGVuZ3RoLCBsb2FkaW5nLCBlcnJvcl0pO1xuXG4gIC8vIFByb3BlcnRpZXMgYXJlIGZldGNoZWQgZGlyZWN0bHkgaW4gdGhlIGNvbXBvbmVudCBib2R5IGFib3ZlXG5cbiAgLy8gVXNlIHJlYWwgcHJvcGVydGllcyBkYXRhIC0gZW5zdXJlIGl0J3MgYWx3YXlzIGFuIGFycmF5XG4gIGNvbnN0IGFsbFByb3BlcnRpZXMgPSBBcnJheS5pc0FycmF5KHByb3BlcnRpZXMpID8gcHJvcGVydGllcyA6IFtdO1xuICBjb25zb2xlLmxvZyhcbiAgICBcIvCfk4ogYWxsUHJvcGVydGllcyBkZXJpdmVkIGZyb20gc3RhdGU6XCIsXG4gICAgYWxsUHJvcGVydGllcy5sZW5ndGgsXG4gICAgXCJpdGVtc1wiXG4gICk7XG5cbiAgLy8gRmlsdGVyIG9wdGlvbnNcbiAgY29uc3QgZGV2ZWxvcG1lbnRTdGF0dXNPcHRpb25zID0gW1xuICAgIFwiUHJlc2FsZVwiLFxuICAgIFwiVW5kZXIgQ29uc3RydWN0aW9uXCIsXG4gICAgXCJDb21wbGV0ZWRcIixcbiAgXTtcbiAgY29uc3QgdW5pdFR5cGVPcHRpb25zID0gW1xuICAgIFwiQXBhcnRtZW50c1wiLFxuICAgIFwiVmlsbGFcIixcbiAgICBcIlRvd25ob3VzZVwiLFxuICAgIFwiRHVwbGV4XCIsXG4gICAgXCJQZW50aG91c2VcIixcbiAgXTtcbiAgY29uc3QgYmVkcm9vbU9wdGlvbnMgPSBbXCJTdHVkaW9cIiwgXCIxIEJSXCIsIFwiMiBCUlwiLCBcIjMgQlJcIiwgXCI0IEJSXCIsIFwiNSsgQlJcIl07XG4gIGNvbnN0IHNhbGVzU3RhdHVzT3B0aW9ucyA9IFtcbiAgICBcIkFubm91bmNlZFwiLFxuICAgIFwiUHJlc2FsZSAoRU9JKVwiLFxuICAgIFwiU3RhcnQgb2YgU2FsZXNcIixcbiAgICBcIk9uIFNhbGVcIixcbiAgICBcIk91dCBvZiBTdG9ja1wiLFxuICBdO1xuICBjb25zdCBjb21wbGV0aW9uRGF0ZU9wdGlvbnMgPSBbXG4gICAgeyB2YWx1ZTogXCJhbGxcIiwgbGFiZWw6IFwiQWxsIFByb2plY3RzXCIgfSxcbiAgICB7IHZhbHVlOiBcIjEybW9udGhzXCIsIGxhYmVsOiBcIkNvbXBsZXRpbmcgaW4gMTIgbW9udGhzXCIgfSxcbiAgICB7IHZhbHVlOiBcIjJ5ZWFyc1wiLCBsYWJlbDogXCJDb21wbGV0aW5nIGluIDIgeWVhcnNcIiB9LFxuICAgIHsgdmFsdWU6IFwiM3llYXJzXCIsIGxhYmVsOiBcIkNvbXBsZXRpbmcgaW4gMyB5ZWFyc1wiIH0sXG4gICAgeyB2YWx1ZTogXCI0eWVhcnNcIiwgbGFiZWw6IFwiQ29tcGxldGluZyBpbiA0IHllYXJzXCIgfSxcbiAgICB7IHZhbHVlOiBcIjV5ZWFyc1wiLCBsYWJlbDogXCJDb21wbGV0aW5nIGluIDUrIHllYXJzXCIgfSxcbiAgXTtcblxuICAvLyBIZWxwZXIgZnVuY3Rpb25zIGZvciBmaWx0ZXJzXG4gIGNvbnN0IHRvZ2dsZUFycmF5RmlsdGVyID0gKGFycmF5OiBzdHJpbmdbXSwgdmFsdWU6IHN0cmluZykgPT4ge1xuICAgIHJldHVybiBhcnJheS5pbmNsdWRlcyh2YWx1ZSlcbiAgICAgID8gYXJyYXkuZmlsdGVyKChpdGVtKSA9PiBpdGVtICE9PSB2YWx1ZSlcbiAgICAgIDogWy4uLmFycmF5LCB2YWx1ZV07XG4gIH07XG5cbiAgY29uc3QgdXBkYXRlRmlsdGVycyA9IChrZXk6IGtleW9mIEZpbHRlclN0YXRlLCB2YWx1ZTogYW55KSA9PiB7XG4gICAgc2V0RmlsdGVycygocHJldikgPT4gKHsgLi4ucHJldiwgW2tleV06IHZhbHVlIH0pKTtcbiAgfTtcblxuICBjb25zdCByZXNldEZpbHRlcnMgPSAoKSA9PiB7XG4gICAgc2V0RmlsdGVycyhpbml0aWFsRmlsdGVycyk7XG4gIH07XG5cbiAgY29uc3QgaGFzQWN0aXZlRmlsdGVycyA9IHVzZU1lbW8oKCkgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICBmaWx0ZXJzLnByaWNlVW5pdCAhPT0gXCJ0b3RhbFwiIHx8XG4gICAgICBmaWx0ZXJzLm1pbkFyZWEgIT09IDAgfHxcbiAgICAgIGZpbHRlcnMubWF4QXJlYSAhPT0gNTAwMCB8fFxuICAgICAgZmlsdGVycy5kZXZlbG9wbWVudFN0YXR1cy5sZW5ndGggPiAwIHx8XG4gICAgICBmaWx0ZXJzLnVuaXRUeXBlcy5sZW5ndGggPiAwIHx8XG4gICAgICBmaWx0ZXJzLmJlZHJvb21zLmxlbmd0aCA+IDAgfHxcbiAgICAgIGZpbHRlcnMuc2FsZXNTdGF0dXMubGVuZ3RoID4gMCB8fFxuICAgICAgZmlsdGVycy5jb21wbGV0aW9uRGF0ZSAhPT0gXCJhbGxcIiB8fFxuICAgICAgZmlsdGVycy5taW5QcmljZSAhPT0gMCB8fFxuICAgICAgZmlsdGVycy5tYXhQcmljZSAhPT0gMTAwMDAwMDBcbiAgICApO1xuICB9LCBbZmlsdGVyc10pO1xuXG4gIGNvbnN0IGFjdGl2ZUZpbHRlckNvdW50ID0gdXNlTWVtbygoKSA9PiB7XG4gICAgbGV0IGNvdW50ID0gMDtcbiAgICBpZiAoZmlsdGVycy5wcmljZVVuaXQgIT09IFwidG90YWxcIikgY291bnQrKztcbiAgICBpZiAoZmlsdGVycy5taW5BcmVhICE9PSAwIHx8IGZpbHRlcnMubWF4QXJlYSAhPT0gNTAwMCkgY291bnQrKztcbiAgICBpZiAoZmlsdGVycy5kZXZlbG9wbWVudFN0YXR1cy5sZW5ndGggPiAwKSBjb3VudCsrO1xuICAgIGlmIChmaWx0ZXJzLnVuaXRUeXBlcy5sZW5ndGggPiAwKSBjb3VudCsrO1xuICAgIGlmIChmaWx0ZXJzLmJlZHJvb21zLmxlbmd0aCA+IDApIGNvdW50Kys7XG4gICAgaWYgKGZpbHRlcnMuc2FsZXNTdGF0dXMubGVuZ3RoID4gMCkgY291bnQrKztcbiAgICBpZiAoZmlsdGVycy5jb21wbGV0aW9uRGF0ZSAhPT0gXCJhbGxcIikgY291bnQrKztcbiAgICBpZiAoZmlsdGVycy5taW5QcmljZSAhPT0gMCB8fCBmaWx0ZXJzLm1heFByaWNlICE9PSAxMDAwMDAwMCkgY291bnQrKztcbiAgICByZXR1cm4gY291bnQ7XG4gIH0sIFtmaWx0ZXJzXSk7XG5cbiAgLy8gQXBwbHkgZmlsdGVycyB0byBwcm9wZXJ0aWVzXG4gIGNvbnN0IGZpbHRlcmVkUHJvcGVydGllcyA9IHVzZU1lbW8oKCkgPT4ge1xuICAgIGNvbnNvbGUubG9nKFxuICAgICAgXCLwn5SNIFN0YXJ0aW5nIGZpbHRlcmluZyB3aXRoIGFsbFByb3BlcnRpZXM6XCIsXG4gICAgICBhbGxQcm9wZXJ0aWVzLmxlbmd0aCxcbiAgICAgIFwiaXRlbXNcIlxuICAgICk7XG5cbiAgICAvLyBFbnN1cmUgYWxsUHJvcGVydGllcyBpcyBhbiBhcnJheSBiZWZvcmUgZmlsdGVyaW5nXG4gICAgaWYgKCFBcnJheS5pc0FycmF5KGFsbFByb3BlcnRpZXMpKSB7XG4gICAgICBjb25zb2xlLndhcm4oXCLimqDvuI8gYWxsUHJvcGVydGllcyBpcyBub3QgYW4gYXJyYXk6XCIsIGFsbFByb3BlcnRpZXMpO1xuICAgICAgcmV0dXJuIFtdO1xuICAgIH1cblxuICAgIGxldCBmaWx0ZXJlZCA9IFsuLi5hbGxQcm9wZXJ0aWVzXTtcbiAgICBjb25zb2xlLmxvZyhcIvCflI0gSW5pdGlhbCBmaWx0ZXJlZCBjb3VudDpcIiwgZmlsdGVyZWQubGVuZ3RoKTtcblxuICAgIC8vIEFwcGx5IHNlYXJjaCBxdWVyeVxuICAgIGlmIChzZWFyY2hRdWVyeSkge1xuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIoXG4gICAgICAgIChwcm9wZXJ0eSkgPT5cbiAgICAgICAgICBwcm9wZXJ0eS5uYW1lPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICAgICAgcHJvcGVydHkuYXJlYT8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hRdWVyeS50b0xvd2VyQ2FzZSgpKSB8fFxuICAgICAgICAgIHByb3BlcnR5LmRldmVsb3Blcj8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hRdWVyeS50b0xvd2VyQ2FzZSgpKVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBBcHBseSBkZXZlbG9wZXIgZmlsdGVyIGlmIHNwZWNpZmllZFxuICAgIGlmIChzZWxlY3RlZERldmVsb3Blcikge1xuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIoXG4gICAgICAgIChwcm9wZXJ0eSkgPT4gcHJvcGVydHkuZGV2ZWxvcGVyID09PSBzZWxlY3RlZERldmVsb3Blci5uYW1lXG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIFNraXAgYXJlYSBmaWx0ZXJzIGZvciBub3cgc2luY2UgQVBJIGRvZXNuJ3QgaGF2ZSBudW1lcmljIGFyZWFcbiAgICAvLyBUaGUgJ2FyZWEnIGZpZWxkIGluIEFQSSBpcyBsb2NhdGlvbiBuYW1lLCBub3Qgc2l6ZVxuXG4gICAgLy8gU2tpcCBwcmljZSBmaWx0ZXJzIGZvciBub3cgc2luY2UgbW9zdCBwcm9wZXJ0aWVzIGhhdmUgbnVsbCBwcmljZXNcbiAgICAvLyBXZSdsbCBzaG93IGFsbCBwcm9wZXJ0aWVzIHJlZ2FyZGxlc3Mgb2YgcHJpY2UgZmlsdGVyc1xuXG4gICAgLy8gQXBwbHkgZGV2ZWxvcG1lbnQgc3RhdHVzIGZpbHRlclxuICAgIGlmIChmaWx0ZXJzLmRldmVsb3BtZW50U3RhdHVzLmxlbmd0aCA+IDApIHtcbiAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKChwcm9wZXJ0eSkgPT5cbiAgICAgICAgZmlsdGVycy5kZXZlbG9wbWVudFN0YXR1cy5pbmNsdWRlcyhwcm9wZXJ0eS5zdGF0dXMpXG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIFNraXAgdW5pdCB0eXBlIGZpbHRlciBzaW5jZSBBUEkgZG9lc24ndCBoYXZlIHByb3BlcnR5VHlwZSBmaWVsZFxuXG4gICAgLy8gU2tpcCBiZWRyb29tIGZpbHRlciBzaW5jZSBBUEkgZG9lc24ndCBoYXZlIGJlZHJvb21zIGZpZWxkXG5cbiAgICBjb25zb2xlLmxvZyhcIvCflI0gRmluYWwgZmlsdGVyZWQgY291bnQ6XCIsIGZpbHRlcmVkLmxlbmd0aCk7XG4gICAgaWYgKGZpbHRlcmVkLmxlbmd0aCA9PT0gMCkge1xuICAgICAgY29uc29sZS53YXJuKFwi4pqg77iPIE5vIHByb3BlcnRpZXMgYWZ0ZXIgZmlsdGVyaW5nISBDaGVjayBmaWx0ZXIgbG9naWMuXCIpO1xuICAgIH1cblxuICAgIHJldHVybiBmaWx0ZXJlZDtcbiAgfSwgW2FsbFByb3BlcnRpZXMsIHNlYXJjaFF1ZXJ5LCBzZWxlY3RlZERldmVsb3BlciwgZmlsdGVyc10pO1xuXG4gIGNvbnN0IHNvcnRlZFByb3BlcnRpZXMgPSB1c2VNZW1vKCgpID0+IHtcbiAgICBjb25zdCBzb3J0ZWQgPSBbLi4uZmlsdGVyZWRQcm9wZXJ0aWVzXTtcblxuICAgIHN3aXRjaCAoc29ydEJ5KSB7XG4gICAgICBjYXNlIFwicHJpY2UtbG93XCI6XG4gICAgICAgIHJldHVybiBzb3J0ZWQuc29ydCgoYSwgYikgPT4gKGEubWluX3ByaWNlIHx8IDApIC0gKGIubWluX3ByaWNlIHx8IDApKTtcbiAgICAgIGNhc2UgXCJwcmljZS1oaWdoXCI6XG4gICAgICAgIHJldHVybiBzb3J0ZWQuc29ydCgoYSwgYikgPT4gKGIubWluX3ByaWNlIHx8IDApIC0gKGEubWluX3ByaWNlIHx8IDApKTtcbiAgICAgIGNhc2UgXCJjb21wbGV0aW9uXCI6XG4gICAgICAgIHJldHVybiBzb3J0ZWQuc29ydCgoYSwgYikgPT5cbiAgICAgICAgICAoYS5jb21wbGV0aW9uX2RhdGV0aW1lIHx8IFwiXCIpLmxvY2FsZUNvbXBhcmUoXG4gICAgICAgICAgICBiLmNvbXBsZXRpb25fZGF0ZXRpbWUgfHwgXCJcIlxuICAgICAgICAgIClcbiAgICAgICAgKTtcbiAgICAgIGNhc2UgXCJsb2NhdGlvblwiOlxuICAgICAgICByZXR1cm4gc29ydGVkLnNvcnQoKGEsIGIpID0+XG4gICAgICAgICAgKGEuYXJlYSB8fCBcIlwiKS5sb2NhbGVDb21wYXJlKGIuYXJlYSB8fCBcIlwiKVxuICAgICAgICApO1xuICAgICAgY2FzZSBcImZlYXR1cmVkXCI6XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gc29ydGVkLnNvcnQoXG4gICAgICAgICAgKGEsIGIpID0+XG4gICAgICAgICAgICAoYi5pc19wYXJ0bmVyX3Byb2plY3QgPyAxIDogMCkgLSAoYS5pc19wYXJ0bmVyX3Byb2plY3QgPyAxIDogMClcbiAgICAgICAgKTtcbiAgICB9XG4gIH0sIFtmaWx0ZXJlZFByb3BlcnRpZXMsIHNvcnRCeV0pO1xuXG4gIGNvbnN0IEx1eHVyeUNoZWNrYm94R3JvdXAgPSAoe1xuICAgIG9wdGlvbnMsXG4gICAgdmFsdWVzLFxuICAgIG9uQ2hhbmdlLFxuICAgIGxhYmVsLFxuICAgIGljb246IEljb24sXG4gIH06IHtcbiAgICBvcHRpb25zOiBzdHJpbmdbXTtcbiAgICB2YWx1ZXM6IHN0cmluZ1tdO1xuICAgIG9uQ2hhbmdlOiAodmFsdWVzOiBzdHJpbmdbXSkgPT4gdm9pZDtcbiAgICBsYWJlbDogc3RyaW5nO1xuICAgIGljb246IGFueTtcbiAgfSkgPT4gKFxuICAgIDxDYXJkIGNsYXNzTmFtZT1cImJvcmRlci1iZWlnZS82MCBzaGFkb3ctc21cIj5cbiAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTRcIj5cbiAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgdGV4dC1zb2Z0LWJyb3duIHRleHQtbGdcIj5cbiAgICAgICAgICA8SWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtZ29sZFwiIC8+XG4gICAgICAgICAgPHNwYW4+e2xhYmVsfTwvc3Bhbj5cbiAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgIHtvcHRpb25zLm1hcCgob3B0aW9uKSA9PiAoXG4gICAgICAgICAgPGRpdiBrZXk9e29wdGlvbn0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIGdyb3VwXCI+XG4gICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctNSBoLTUgcm91bmRlZC1tZCBib3JkZXItMiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcbiAgICAgICAgICAgICAgICB2YWx1ZXMuaW5jbHVkZXMob3B0aW9uKVxuICAgICAgICAgICAgICAgICAgPyBcImJnLWdvbGQgYm9yZGVyLWdvbGQgdGV4dC1jaGFyY29hbCBzaGFkb3ctc21cIlxuICAgICAgICAgICAgICAgICAgOiBcImJvcmRlci1zb2Z0LWJyb3duLzMwIGhvdmVyOmJvcmRlci1nb2xkIGhvdmVyOmJnLWdvbGQvNSBob3ZlcjpzaGFkb3ctc21cIlxuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb25DaGFuZ2UodG9nZ2xlQXJyYXlGaWx0ZXIodmFsdWVzLCBvcHRpb24pKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge3ZhbHVlcy5pbmNsdWRlcyhvcHRpb24pICYmIDxDaGVjayBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz59XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxsYWJlbFxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtd2FybS1ncmF5IGN1cnNvci1wb2ludGVyIGZsZXgtMSBncm91cC1ob3Zlcjp0ZXh0LXNvZnQtYnJvd24gdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvbkNoYW5nZSh0b2dnbGVBcnJheUZpbHRlcih2YWx1ZXMsIG9wdGlvbikpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7b3B0aW9ufVxuICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSl9XG4gICAgICA8L0NhcmRDb250ZW50PlxuICAgIDwvQ2FyZD5cbiAgKTtcblxuICBjb25zdCBmb3JtYXRQcmljZSA9ICh2YWx1ZTogbnVtYmVyKSA9PiB7XG4gICAgaWYgKGZpbHRlcnMucHJpY2VVbml0ID09PSBcInNxZnRcIikge1xuICAgICAgcmV0dXJuIGAke3ZhbHVlLnRvTG9jYWxlU3RyaW5nKCl9YDtcbiAgICB9IGVsc2Uge1xuICAgICAgaWYgKHZhbHVlID49IDEwMDAwMDApIHtcbiAgICAgICAgcmV0dXJuIGAkeyh2YWx1ZSAvIDEwMDAwMDApLnRvRml4ZWQoMSl9TWA7XG4gICAgICB9IGVsc2UgaWYgKHZhbHVlID49IDEwMDApIHtcbiAgICAgICAgcmV0dXJuIGAkeyh2YWx1ZSAvIDEwMDApLnRvRml4ZWQoMCl9S2A7XG4gICAgICB9XG4gICAgICByZXR1cm4gdmFsdWUudG9Mb2NhbGVTdHJpbmcoKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1pdm9yeVwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLWIgYm9yZGVyLWJlaWdlIHNoYWRvdy1zbVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBweS02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbWItMlwiPlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17b25CYWNrfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC13YXJtLWdyYXkgaG92ZXI6dGV4dC1nb2xkXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8SG9tZSBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgSG9tZVxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXdhcm0tZ3JheVwiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zb2Z0LWJyb3duXCI+UHJvcGVydGllczwvc3Bhbj5cbiAgICAgICAgICAgICAgICB7c2VsZWN0ZWREZXZlbG9wZXIgJiYgKFxuICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgPENoZXZyb25SaWdodCBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtd2FybS1ncmF5XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1nb2xkXCI+e3NlbGVjdGVkRGV2ZWxvcGVyLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXNvZnQtYnJvd24gdGV4dC1bNDhweF0gbGVhZGluZy1bMS4yXSBweS0yXCI+XG4gICAgICAgICAgICAgICAge3NlbGVjdGVkRGV2ZWxvcGVyXG4gICAgICAgICAgICAgICAgICA/IGAke3NlbGVjdGVkRGV2ZWxvcGVyLm5hbWV9YFxuICAgICAgICAgICAgICAgICAgOiBcIkFsbCBEZXZlbG9wbWVudHNcIn1cbiAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13YXJtLWdyYXkgbXQtMlwiPlxuICAgICAgICAgICAgICAgIHtzb3J0ZWRQcm9wZXJ0aWVzLmxlbmd0aH0gcHJvcGVydGllcyBmb3VuZFxuICAgICAgICAgICAgICAgIHtoYXNBY3RpdmVGaWx0ZXJzICYmIGAgKCR7YWN0aXZlRmlsdGVyQ291bnR9IGZpbHRlcnMgYXBwbGllZClgfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBTZWFyY2ggYW5kIENvbnRyb2xzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBsZzpmbGV4LXJvdyBnYXAtNCBpdGVtcy1zdGFydCBsZzppdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtYXgtdy1tZFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgPFNlYXJjaCBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB0ZXh0LXdhcm0tZ3JheSB3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIHByb3BlcnRpZXMsIGxvY2F0aW9ucywgZGV2ZWxvcGVycy4uLlwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoUXVlcnl9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFF1ZXJ5KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBsLTEwIGJnLXdoaXRlIGJvcmRlci1iZWlnZSBmb2N1czpib3JkZXItZ29sZCByb3VuZGVkLXhsXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICB7LyogQWR2YW5jZWQgRmlsdGVycyBCdXR0b24gKi99XG4gICAgICAgICAgICAgIDxEaWFsb2dcbiAgICAgICAgICAgICAgICBvcGVuPXtpc0ZpbHRlck1vZGFsT3Blbn1cbiAgICAgICAgICAgICAgICBvbk9wZW5DaGFuZ2U9e3NldElzRmlsdGVyTW9kYWxPcGVufVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPERpYWxvZ1RyaWdnZXIgYXNDaGlsZD5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bib3JkZXItc29mdC1icm93bi8zMCB0ZXh0LXNvZnQtYnJvd24gaG92ZXI6Ymctc29mdC1icm93biBob3Zlcjp0ZXh0LXdoaXRlIHJvdW5kZWQteGwgcmVsYXRpdmUgJHtcbiAgICAgICAgICAgICAgICAgICAgICBoYXNBY3RpdmVGaWx0ZXJzXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctZ29sZCB0ZXh0LWNoYXJjb2FsIGJvcmRlci1nb2xkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJcIlxuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPFNsaWRlcnNIb3Jpem9udGFsIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgIEZpbHRlcnNcbiAgICAgICAgICAgICAgICAgICAge2FjdGl2ZUZpbHRlckNvdW50ID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIGNsYXNzTmFtZT1cIm1sLTIgYmctc29mdC1icm93biB0ZXh0LXdoaXRlIHRleHQteHMgbWluLXctWzIwcHhdIGgtNVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2FjdGl2ZUZpbHRlckNvdW50fVxuICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L0RpYWxvZ1RyaWdnZXI+XG4gICAgICAgICAgICAgICAgPERpYWxvZ0NvbnRlbnQgY2xhc3NOYW1lPVwic206bWF4LXctNnhsIG1heC1oLVs5MHZoXSBiZy13aGl0ZSBmbGV4IGZsZXgtY29sIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgICAgey8qIEZpeGVkIEhlYWRlciAtIE5vIFNjcm9sbCAqL31cbiAgICAgICAgICAgICAgICAgIDxEaWFsb2dIZWFkZXIgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCBwYi02IGJvcmRlci1iIGJvcmRlci1iZWlnZVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8RGlhbG9nVGl0bGUgY2xhc3NOYW1lPVwidGV4dC0yeGwgdGV4dC1zb2Z0LWJyb3duXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIEFkdmFuY2VkIEZpbHRlcnNcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8RGlhbG9nRGVzY3JpcHRpb24gY2xhc3NOYW1lPVwidGV4dC13YXJtLWdyYXkgbXQtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBSZWZpbmUgeW91ciBwcm9wZXJ0eSBzZWFyY2ggdXNpbmcgdGhlIGZpbHRlcnMgYmVsb3cgdG9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgZmluZCBwcm9wZXJ0aWVzIHRoYXQgbWF0Y2ggeW91ciBzcGVjaWZpYyByZXF1aXJlbWVudHMuXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0RpYWxvZ0Rlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17cmVzZXRGaWx0ZXJzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWhhc0FjdGl2ZUZpbHRlcnN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGJvcmRlci1zb2Z0LWJyb3duLzMwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhc0FjdGl2ZUZpbHRlcnNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LWdvbGQgYm9yZGVyLWdvbGQvMzAgaG92ZXI6YmctZ29sZC8xMCBob3Zlcjpib3JkZXItZ29sZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC13YXJtLWdyYXkgYm9yZGVyLWJlaWdlIGhvdmVyOmJnLWJlaWdlLzUwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxSb3RhdGVDY3cgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgUmVzZXRcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc0ZpbHRlck1vZGFsT3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdvbGQgaG92ZXI6YmctZ29sZC85MCB0ZXh0LWNoYXJjb2FsXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgQXBwbHkgRmlsdGVyc1xuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9EaWFsb2dIZWFkZXI+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBTY3JvbGxhYmxlIENvbnRlbnQgQXJlYSAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0byBweS04IHNwYWNlLXktOFwiPlxuICAgICAgICAgICAgICAgICAgICB7LyogUHJpY2UgQ29uZmlndXJhdGlvbiBTZWN0aW9uICovfVxuICAgICAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJib3JkZXItZ29sZC8yMCBiZy1ncmFkaWVudC10by1yIGZyb20tbGlnaHQtZ29sZC8xMCB0by1iZWlnZS8zMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgdGV4dC1zb2Z0LWJyb3duIHRleHQteGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhbmtub3RlIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1nb2xkXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+UHJpY2UgQ29uZmlndXJhdGlvbjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogUHJpY2UgVW5pdCBUb2dnbGUgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTQgYmctd2hpdGUgcm91bmRlZC14bCBib3JkZXIgYm9yZGVyLWJlaWdlLzUwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPERvbGxhclNpZ24gY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWdvbGRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zb2Z0LWJyb3duIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFByaWNlIERpc3BsYXkgTW9kZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC13YXJtLWdyYXkgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBDaG9vc2UgaG93IHByaWNlcyBhcmUgZGlzcGxheWVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWx0ZXJzLnByaWNlVW5pdCA9PT0gXCJ0b3RhbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtc29mdC1icm93blwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtd2FybS1ncmF5XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFRvdGFsIFByaWNlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U3dpdGNoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtmaWx0ZXJzLnByaWNlVW5pdCA9PT0gXCJzcWZ0XCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkKSA9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVGaWx0ZXJzKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwicHJpY2VVbml0XCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZCA/IFwic3FmdFwiIDogXCJ0b3RhbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImRhdGEtW3N0YXRlPWNoZWNrZWRdOmJnLWdvbGRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXNtIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbHRlcnMucHJpY2VVbml0ID09PSBcInNxZnRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LXNvZnQtYnJvd25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXdhcm0tZ3JheVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBQZXIgU3EgRnRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogUHJpY2UgUmFuZ2UgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmVuZGluZ1VwIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1nb2xkXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zb2Z0LWJyb3duIGZvbnQtbWVkaXVtIHRleHQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFByaWNlIFJhbmdlIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmaWx0ZXJzLnByaWNlVW5pdCA9PT0gXCJzcWZ0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcIkFFRCBwZXIgU3EgRnRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiQUVEXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtd2FybS1ncmF5IHVwcGVyY2FzZSB0cmFja2luZy13aWRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1pbmltdW1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHRleHQtd2FybS1ncmF5IHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBBRURcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmlsdGVycy5taW5QcmljZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVGaWx0ZXJzKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIm1pblByaWNlXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSB8fCAwXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBsLTEyIGJvcmRlci1iZWlnZS81MCBmb2N1czpib3JkZXItZ29sZCByb3VuZGVkLWxnXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtd2FybS1ncmF5IHVwcGVyY2FzZSB0cmFja2luZy13aWRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1heGltdW1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHRleHQtd2FybS1ncmF5IHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBBRURcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmlsdGVycy5tYXhQcmljZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVGaWx0ZXJzKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIm1heFByaWNlXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSB8fCAxMDAwMDAwMFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC0xMiBib3JkZXItYmVpZ2UvNTAgZm9jdXM6Ym9yZGVyLWdvbGQgcm91bmRlZC1sZ1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIxMCwwMDAsMDAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMyBwLTQgYmctYmVpZ2UvMzAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbSB0ZXh0LXdhcm0tZ3JheVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+QUVEIHtmb3JtYXRQcmljZShmaWx0ZXJzLm1pblByaWNlKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5BRUQge2Zvcm1hdFByaWNlKGZpbHRlcnMubWF4UHJpY2UpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2xpZGVyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17W2ZpbHRlcnMubWluUHJpY2UsIGZpbHRlcnMubWF4UHJpY2VdfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17KFttaW4sIG1heF0pID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlRmlsdGVycyhcIm1pblByaWNlXCIsIG1pbik7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZUZpbHRlcnMoXCJtYXhQcmljZVwiLCBtYXgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1heD17XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbHRlcnMucHJpY2VVbml0ID09PSBcInNxZnRcIiA/IDUwMDAgOiAyMDAwMDAwMFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWluPXswfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RlcD17ZmlsdGVycy5wcmljZVVuaXQgPT09IFwic3FmdFwiID8gNTAgOiA1MDAwMH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIEFyZWEgYW5kIFRpbWluZyBTZWN0aW9uICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7LyogQXJlYSBSYW5nZSAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJib3JkZXItYmVpZ2UvNjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiB0ZXh0LXNvZnQtYnJvd24gdGV4dC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxSdWxlciBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtZ29sZFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+QXJlYSBSYW5nZTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXdhcm0tZ3JheSB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBNaW4gU3EgRnRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmaWx0ZXJzLm1pbkFyZWF9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVGaWx0ZXJzKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJtaW5BcmVhXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYXJzZUludChlLnRhcmdldC52YWx1ZSkgfHwgMFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItYmVpZ2UvNTAgZm9jdXM6Ym9yZGVyLWdvbGQgcm91bmRlZC1sZ1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXdhcm0tZ3JheSB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBNYXggU3EgRnRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmaWx0ZXJzLm1heEFyZWF9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVGaWx0ZXJzKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJtYXhBcmVhXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYXJzZUludChlLnRhcmdldC52YWx1ZSkgfHwgNTAwMFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItYmVpZ2UvNTAgZm9jdXM6Ym9yZGVyLWdvbGQgcm91bmRlZC1sZ1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiNSwwMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgcC0zIGJnLWJlaWdlLzIwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIHRleHQtc20gdGV4dC13YXJtLWdyYXlcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZmlsdGVycy5taW5BcmVhLnRvTG9jYWxlU3RyaW5nKCl9IHNxIGZ0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2ZpbHRlcnMubWF4QXJlYS50b0xvY2FsZVN0cmluZygpfSBzcSBmdFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTbGlkZXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtbZmlsdGVycy5taW5BcmVhLCBmaWx0ZXJzLm1heEFyZWFdfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17KFttaW4sIG1heF0pID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlRmlsdGVycyhcIm1pbkFyZWFcIiwgbWluKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlRmlsdGVycyhcIm1heEFyZWFcIiwgbWF4KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYXg9ezgwMDB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaW49ezB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGVwPXs1MH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBQcm9qZWN0IENvbXBsZXRpb24gKi99XG4gICAgICAgICAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYm9yZGVyLWJlaWdlLzYwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgdGV4dC1zb2Z0LWJyb3duIHRleHQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWdvbGRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlByb2plY3QgQ29tcGxldGlvbjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC13YXJtLWdyYXkgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIENvbXBsZXRpb24gVGltZWZyYW1lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmlsdGVycy5jb21wbGV0aW9uRGF0ZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZSkgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlRmlsdGVycyhcImNvbXBsZXRpb25EYXRlXCIsIHZhbHVlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyIGNsYXNzTmFtZT1cInctZnVsbCBib3JkZXItYmVpZ2UvNTAgZm9jdXM6Ym9yZGVyLWdvbGQgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0VmFsdWUgcGxhY2Vob2xkZXI9XCJTZWxlY3QgY29tcGxldGlvbiB0aW1lZnJhbWVcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb21wbGV0aW9uRGF0ZU9wdGlvbnMubWFwKChvcHRpb24pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtvcHRpb24udmFsdWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17b3B0aW9uLnZhbHVlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtvcHRpb24ubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIFByb3BlcnR5IENoYXJhY3RlcmlzdGljcyBTZWN0aW9uICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxIb21lSWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtZ29sZFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LXNvZnQtYnJvd25cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgUHJvcGVydHkgQ2hhcmFjdGVyaXN0aWNzXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogRGV2ZWxvcG1lbnQgU3RhdHVzICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgPEx1eHVyeUNoZWNrYm94R3JvdXBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJEZXZlbG9wbWVudCBTdGF0dXNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uPXtIYW1tZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e2RldmVsb3BtZW50U3RhdHVzT3B0aW9uc31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWVzPXtmaWx0ZXJzLmRldmVsb3BtZW50U3RhdHVzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlcykgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVGaWx0ZXJzKFwiZGV2ZWxvcG1lbnRTdGF0dXNcIiwgdmFsdWVzKVxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogVW5pdCBUeXBlcyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIDxMdXh1cnlDaGVja2JveEdyb3VwXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiVW5pdCBUeXBlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbj17QnVpbGRpbmcyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zPXt1bml0VHlwZU9wdGlvbnN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlcz17ZmlsdGVycy51bml0VHlwZXN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsodmFsdWVzKSA9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZUZpbHRlcnMoXCJ1bml0VHlwZXNcIiwgdmFsdWVzKVxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogQmVkcm9vbXMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8THV4dXJ5Q2hlY2tib3hHcm91cFxuICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIkJlZHJvb21zXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbj17QmVkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zPXtiZWRyb29tT3B0aW9uc31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWVzPXtmaWx0ZXJzLmJlZHJvb21zfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlcykgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVGaWx0ZXJzKFwiYmVkcm9vbXNcIiwgdmFsdWVzKVxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogU2FsZXMgU3RhdHVzICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgPEx1eHVyeUNoZWNrYm94R3JvdXBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJTYWxlcyBTdGF0dXNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uPXtTaG9wcGluZ0NhcnR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e3NhbGVzU3RhdHVzT3B0aW9uc31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWVzPXtmaWx0ZXJzLnNhbGVzU3RhdHVzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlcykgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVGaWx0ZXJzKFwic2FsZXNTdGF0dXNcIiwgdmFsdWVzKVxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogU3VtbWFyeSBTZWN0aW9uICovfVxuICAgICAgICAgICAgICAgICAgICB7aGFzQWN0aXZlRmlsdGVycyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYm9yZGVyLWdvbGQvMzAgYmctZ3JhZGllbnQtdG8tciBmcm9tLWdvbGQvNSB0by1saWdodC1nb2xkLzEwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicHQtNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LXNvZnQtYnJvd24gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBGaWx0ZXIgU3VtbWFyeVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2FybS1ncmF5XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHthY3RpdmVGaWx0ZXJDb3VudH0gZmlsdGVycyBhcHBsaWVkIOKAontcIiBcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NvcnRlZFByb3BlcnRpZXMubGVuZ3RofSBwcm9wZXJ0aWVzIG1hdGNoIHlvdXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3JpdGVyaWFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtyZXNldEZpbHRlcnN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItZ29sZCB0ZXh0LWdvbGQgaG92ZXI6YmctZ29sZCBob3Zlcjp0ZXh0LWNoYXJjb2FsXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Um90YXRlQ2N3IGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBSZXNldCBBbGxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICAgICAgICAgICAgPC9EaWFsb2c+XG5cbiAgICAgICAgICAgICAgey8qIFNvcnQgRHJvcGRvd24gKi99XG4gICAgICAgICAgICAgIDxTZWxlY3QgdmFsdWU9e3NvcnRCeX0gb25WYWx1ZUNoYW5nZT17c2V0U29ydEJ5fT5cbiAgICAgICAgICAgICAgICA8U2VsZWN0VHJpZ2dlciBjbGFzc05hbWU9XCJ3LTQwIGJvcmRlci1zb2Z0LWJyb3duLzMwIHJvdW5kZWQteGxcIj5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSBwbGFjZWhvbGRlcj1cIlNvcnQgYnlcIiAvPlxuICAgICAgICAgICAgICAgIDwvU2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiZmVhdHVyZWRcIj5GZWF0dXJlZDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwicHJpY2UtbG93XCI+UHJpY2U6IExvdyB0byBIaWdoPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJwcmljZS1oaWdoXCI+UHJpY2U6IEhpZ2ggdG8gTG93PC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJjb21wbGV0aW9uXCI+Q29tcGxldGlvbiBEYXRlPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJsb2NhdGlvblwiPkxvY2F0aW9uPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgIDwvU2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgPC9TZWxlY3Q+XG5cbiAgICAgICAgICAgICAgey8qIFZpZXcgTW9kZSBUb2dnbGUgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgYm9yZGVyIGJvcmRlci1zb2Z0LWJyb3duLzMwIHJvdW5kZWQteGwgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgdmFyaWFudD17dmlld01vZGUgPT09IFwiZ3JpZFwiID8gXCJkZWZhdWx0XCIgOiBcImdob3N0XCJ9XG4gICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Vmlld01vZGUoXCJncmlkXCIpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcm91bmRlZC1ub25lICR7XG4gICAgICAgICAgICAgICAgICAgIHZpZXdNb2RlID09PSBcImdyaWRcIlxuICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1zb2Z0LWJyb3duIHRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXNvZnQtYnJvd24gaG92ZXI6Ymctc29mdC1icm93bi8xMFwiXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8R3JpZDNYMyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PXt2aWV3TW9kZSA9PT0gXCJsaXN0XCIgPyBcImRlZmF1bHRcIiA6IFwiZ2hvc3RcIn1cbiAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRWaWV3TW9kZShcImxpc3RcIil9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Byb3VuZGVkLW5vbmUgJHtcbiAgICAgICAgICAgICAgICAgICAgdmlld01vZGUgPT09IFwibGlzdFwiXG4gICAgICAgICAgICAgICAgICAgICAgPyBcImJnLXNvZnQtYnJvd24gdGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtc29mdC1icm93biBob3ZlcjpiZy1zb2Z0LWJyb3duLzEwXCJcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxMaXN0IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEFjdGl2ZSBGaWx0ZXJzIERpc3BsYXkgKi99XG4gICAgICAgICAge2hhc0FjdGl2ZUZpbHRlcnMgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IGZsZXggZmxleC13cmFwIGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtd2FybS1ncmF5IG1yLTJcIj5cbiAgICAgICAgICAgICAgICBBY3RpdmUgZmlsdGVyczpcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICB7ZmlsdGVycy5wcmljZVVuaXQgIT09IFwidG90YWxcIiAmJiAoXG4gICAgICAgICAgICAgICAgPEJhZGdlXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1nb2xkLzEwIHRleHQtZ29sZCBib3JkZXItZ29sZC8zMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgUHJpY2UgcGVyIFNxIEZ0XG4gICAgICAgICAgICAgICAgICA8WFxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTMgaC0zIG1sLTEgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB1cGRhdGVGaWx0ZXJzKFwicHJpY2VVbml0XCIsIFwidG90YWxcIil9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIHsoZmlsdGVycy5taW5BcmVhICE9PSAwIHx8IGZpbHRlcnMubWF4QXJlYSAhPT0gNTAwMCkgJiYgKFxuICAgICAgICAgICAgICAgIDxCYWRnZVxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ29sZC8xMCB0ZXh0LWdvbGQgYm9yZGVyLWdvbGQvMzBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIEFyZWE6IHtmaWx0ZXJzLm1pbkFyZWF9LXtmaWx0ZXJzLm1heEFyZWF9IHNxIGZ0XG4gICAgICAgICAgICAgICAgICA8WFxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTMgaC0zIG1sLTEgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgdXBkYXRlRmlsdGVycyhcIm1pbkFyZWFcIiwgMCk7XG4gICAgICAgICAgICAgICAgICAgICAgdXBkYXRlRmlsdGVycyhcIm1heEFyZWFcIiwgNTAwMCk7XG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIHtmaWx0ZXJzLmRldmVsb3BtZW50U3RhdHVzLm1hcCgoc3RhdHVzKSA9PiAoXG4gICAgICAgICAgICAgICAgPEJhZGdlXG4gICAgICAgICAgICAgICAgICBrZXk9e3N0YXR1c31cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdvbGQvMTAgdGV4dC1nb2xkIGJvcmRlci1nb2xkLzMwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7c3RhdHVzfVxuICAgICAgICAgICAgICAgICAgPFhcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0zIGgtMyBtbC0xIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT5cbiAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVGaWx0ZXJzKFxuICAgICAgICAgICAgICAgICAgICAgICAgXCJkZXZlbG9wbWVudFN0YXR1c1wiLFxuICAgICAgICAgICAgICAgICAgICAgICAgZmlsdGVycy5kZXZlbG9wbWVudFN0YXR1cy5maWx0ZXIoKHMpID0+IHMgIT09IHN0YXR1cylcbiAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIHtmaWx0ZXJzLnVuaXRUeXBlcy5tYXAoKHR5cGUpID0+IChcbiAgICAgICAgICAgICAgICA8QmFkZ2VcbiAgICAgICAgICAgICAgICAgIGtleT17dHlwZX1cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdvbGQvMTAgdGV4dC1nb2xkIGJvcmRlci1nb2xkLzMwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7dHlwZX1cbiAgICAgICAgICAgICAgICAgIDxYXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMyBoLTMgbWwtMSBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+XG4gICAgICAgICAgICAgICAgICAgICAgdXBkYXRlRmlsdGVycyhcbiAgICAgICAgICAgICAgICAgICAgICAgIFwidW5pdFR5cGVzXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBmaWx0ZXJzLnVuaXRUeXBlcy5maWx0ZXIoKHQpID0+IHQgIT09IHR5cGUpXG4gICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICB7ZmlsdGVycy5iZWRyb29tcy5tYXAoKGJlZHJvb20pID0+IChcbiAgICAgICAgICAgICAgICA8QmFkZ2VcbiAgICAgICAgICAgICAgICAgIGtleT17YmVkcm9vbX1cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdvbGQvMTAgdGV4dC1nb2xkIGJvcmRlci1nb2xkLzMwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7YmVkcm9vbX1cbiAgICAgICAgICAgICAgICAgIDxYXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMyBoLTMgbWwtMSBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+XG4gICAgICAgICAgICAgICAgICAgICAgdXBkYXRlRmlsdGVycyhcbiAgICAgICAgICAgICAgICAgICAgICAgIFwiYmVkcm9vbXNcIixcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpbHRlcnMuYmVkcm9vbXMuZmlsdGVyKChiKSA9PiBiICE9PSBiZWRyb29tKVxuICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAge2ZpbHRlcnMuc2FsZXNTdGF0dXMubWFwKChzdGF0dXMpID0+IChcbiAgICAgICAgICAgICAgICA8QmFkZ2VcbiAgICAgICAgICAgICAgICAgIGtleT17c3RhdHVzfVxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ29sZC8xMCB0ZXh0LWdvbGQgYm9yZGVyLWdvbGQvMzBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtzdGF0dXN9XG4gICAgICAgICAgICAgICAgICA8WFxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTMgaC0zIG1sLTEgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PlxuICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZUZpbHRlcnMoXG4gICAgICAgICAgICAgICAgICAgICAgICBcInNhbGVzU3RhdHVzXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBmaWx0ZXJzLnNhbGVzU3RhdHVzLmZpbHRlcigocykgPT4gcyAhPT0gc3RhdHVzKVxuICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAge2ZpbHRlcnMuY29tcGxldGlvbkRhdGUgIT09IFwiYWxsXCIgJiYgKFxuICAgICAgICAgICAgICAgIDxCYWRnZVxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ29sZC8xMCB0ZXh0LWdvbGQgYm9yZGVyLWdvbGQvMzBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgY29tcGxldGlvbkRhdGVPcHRpb25zLmZpbmQoXG4gICAgICAgICAgICAgICAgICAgICAgKG9wdCkgPT4gb3B0LnZhbHVlID09PSBmaWx0ZXJzLmNvbXBsZXRpb25EYXRlXG4gICAgICAgICAgICAgICAgICAgICk/LmxhYmVsXG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICA8WFxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTMgaC0zIG1sLTEgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB1cGRhdGVGaWx0ZXJzKFwiY29tcGxldGlvbkRhdGVcIiwgXCJhbGxcIil9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17cmVzZXRGaWx0ZXJzfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ29sZCBob3ZlcjpiZy1nb2xkLzEwIHRleHQteHNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgQ2xlYXIgYWxsXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2hlYWRlcj5cblxuICAgICAgey8qIE1haW4gQ29udGVudCAqL31cbiAgICAgIDxtYWluIGNsYXNzTmFtZT1cImNvbnRhaW5lciBweS04XCI+XG4gICAgICAgIHsvKiBMb2FkaW5nIFN0YXRlICovfVxuICAgICAgICB7bG9hZGluZyAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC04XCI+XG4gICAgICAgICAgICB7QXJyYXkuZnJvbSh7IGxlbmd0aDogNiB9KS5tYXAoKF8sIGkpID0+IChcbiAgICAgICAgICAgICAgPFByb3BlcnR5Q2FyZFNrZWxldG9uIGtleT17aX0gLz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBFcnJvciBTdGF0ZSAqL31cbiAgICAgICAge2Vycm9yICYmICFsb2FkaW5nICYmIChcbiAgICAgICAgICA8RXJyb3JTdGF0ZSBvblJldHJ5PXsoKSA9PiBmZXRjaFByb3BlcnRpZXMoKX0gbWVzc2FnZT17ZXJyb3J9IC8+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIE5vIFByb3BlcnRpZXMgRm91bmQgKi99XG4gICAgICAgIHshbG9hZGluZyAmJiAhZXJyb3IgJiYgc29ydGVkUHJvcGVydGllcy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xNlwiPlxuICAgICAgICAgICAgPEJ1aWxkaW5nMiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgdGV4dC13YXJtLWdyYXkgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtc29mdC1icm93biBtYi0yXCI+XG4gICAgICAgICAgICAgIE5vIHByb3BlcnRpZXMgZm91bmRcbiAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdhcm0tZ3JheSBtYi02XCI+XG4gICAgICAgICAgICAgIFRyeSBhZGp1c3RpbmcgeW91ciBmaWx0ZXJzIG9yIHNlYXJjaCBjcml0ZXJpYSB0byBmaW5kIG1vcmVcbiAgICAgICAgICAgICAgcHJvcGVydGllcy5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17cmVzZXRGaWx0ZXJzfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1nb2xkIGhvdmVyOmJnLWdvbGQvOTAgdGV4dC1jaGFyY29hbFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIFJlc2V0IEZpbHRlcnNcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogKFxuICAgICAgICAgIDw+XG4gICAgICAgICAgICB7LyogR3JpZCBWaWV3ICovfVxuICAgICAgICAgICAge3ZpZXdNb2RlID09PSBcImdyaWRcIiAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtOFwiPlxuICAgICAgICAgICAgICAgIHtzb3J0ZWRQcm9wZXJ0aWVzLm1hcCgocHJvcGVydHkpID0+IChcbiAgICAgICAgICAgICAgICAgIDxDYXJkXG4gICAgICAgICAgICAgICAgICAgIGtleT17cHJvcGVydHkuaWR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIGN1cnNvci1wb2ludGVyIGJvcmRlciBib3JkZXItYmVpZ2UgaG92ZXI6c2hhZG93LXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBob3ZlcjotdHJhbnNsYXRlLXktMSBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC14bFwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uUHJvamVjdFNlbGVjdChwcm9wZXJ0eSl9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgYXNwZWN0LVs0LzNdIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZVdpdGhGYWxsYmFja1xuICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvcGVydHkuY292ZXJfaW1hZ2VfdXJsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBKU09OLnBhcnNlKHByb3BlcnR5LmNvdmVyX2ltYWdlX3VybClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC51cmw7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2gge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBcIi9wbGFjZWhvbGRlci1wcm9wZXJ0eS5qcGdcIjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkoKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCIvcGxhY2Vob2xkZXItcHJvcGVydHkuanBnXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGFsdD17cHJvcGVydHkubmFtZSB8fCBcIlByb3BlcnR5XCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlciBncm91cC1ob3ZlcjpzY2FsZS0xMDUgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIHtwcm9wZXJ0eS5pc19wYXJ0bmVyX3Byb2plY3QgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC00IGxlZnQtNCBiZy1nb2xkIHRleHQtY2hhcmNvYWxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgRmVhdHVyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTQgcmlnaHQtNCBiZy1ibGFjay83MCB0ZXh0LXdoaXRlIGJvcmRlci0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7cHJvcGVydHkuc3RhdHVzIHx8IFwiQXZhaWxhYmxlXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1zb2Z0LWJyb3duIGdyb3VwLWhvdmVyOnRleHQtZ29sZCB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9wZXJ0eS5uYW1lIHx8IFwiUHJvcGVydHkgVGl0bGVcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LXdhcm0tZ3JheSBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPE1hcFBpbiBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9wZXJ0eS5hcmVhIHx8IFwiTG9jYXRpb25cIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgdGV4dC1nb2xkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb3BlcnR5Lm1pbl9wcmljZSAmJiBwcm9wZXJ0eS5tYXhfcHJpY2VcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gcHJvcGVydHkubWluX3ByaWNlID09PSBwcm9wZXJ0eS5tYXhfcHJpY2VcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByb3BlcnR5LnByaWNlX2N1cnJlbmN5IHx8IFwiQUVEXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9ICR7cHJvcGVydHkubWluX3ByaWNlLnRvTG9jYWxlU3RyaW5nKCl9YFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvcGVydHkucHJpY2VfY3VycmVuY3kgfHwgXCJBRURcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gJHtwcm9wZXJ0eS5taW5fcHJpY2UudG9Mb2NhbGVTdHJpbmcoKX0gLSAke3Byb3BlcnR5Lm1heF9wcmljZS50b0xvY2FsZVN0cmluZygpfWBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogcHJvcGVydHkubWluX3ByaWNlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGBGcm9tICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvcGVydHkucHJpY2VfY3VycmVuY3kgfHwgXCJBRURcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9ICR7cHJvcGVydHkubWluX3ByaWNlLnRvTG9jYWxlU3RyaW5nKCl9YFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBwcm9wZXJ0eS5tYXhfcHJpY2VcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gYFVwIHRvICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvcGVydHkucHJpY2VfY3VycmVuY3kgfHwgXCJBRURcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9ICR7cHJvcGVydHkubWF4X3ByaWNlLnRvTG9jYWxlU3RyaW5nKCl9YFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIlByaWNlIG9uIFJlcXVlc3RcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIGJvcmRlci1nb2xkLzMwIHRleHQtZ29sZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvcGVydHkuc2FsZV9zdGF0dXMgfHwgXCJBdmFpbGFibGVcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTMgZ2FwLTQgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U3F1YXJlIGNsYXNzTmFtZT1cInctNCBoLTQgbXgtYXV0byB0ZXh0LWdvbGRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXdhcm0tZ3JheVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb3BlcnR5LmFyZWFfdW5pdCB8fCBcInNxZnRcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cInctNCBoLTQgbXgtYXV0byB0ZXh0LWdvbGRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXdhcm0tZ3JheVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb3BlcnR5LnN0YXR1cyB8fCBcIkF2YWlsYWJsZVwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2hpZWxkIGNsYXNzTmFtZT1cInctNCBoLTQgbXgtYXV0byB0ZXh0LWdvbGRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXdhcm0tZ3JheVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb3BlcnR5Lmhhc19lc2Nyb3cgPyBcIkVzY3Jvd1wiIDogXCJEaXJlY3RcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcHQtNCBib3JkZXItdCBib3JkZXItYmVpZ2VcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtd2FybS1ncmF5XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cInctNCBoLTQgaW5saW5lIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9wZXJ0eS5jb21wbGV0aW9uX2RhdGV0aW1lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IG5ldyBEYXRlKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByb3BlcnR5LmNvbXBsZXRpb25fZGF0ZXRpbWVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKS50b0xvY2FsZURhdGVTdHJpbmcoKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBwcm9wZXJ0eS5wb3N0X2hhbmRvdmVyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiUG9zdCBIYW5kb3ZlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiVEJEXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC13YXJtLWdyYXlcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvcGVydHkuZGV2ZWxvcGVyIHx8IFwiRGV2ZWxvcGVyXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIExpc3QgVmlldyAqL31cbiAgICAgICAgICAgIHt2aWV3TW9kZSA9PT0gXCJsaXN0XCIgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgIHtzb3J0ZWRQcm9wZXJ0aWVzLm1hcCgocHJvcGVydHkpID0+IChcbiAgICAgICAgICAgICAgICAgIDxDYXJkXG4gICAgICAgICAgICAgICAgICAgIGtleT17cHJvcGVydHkuaWR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIGN1cnNvci1wb2ludGVyIGJvcmRlciBib3JkZXItYmVpZ2UgaG92ZXI6c2hhZG93LWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC14bFwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uUHJvamVjdFNlbGVjdChwcm9wZXJ0eSl9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWQ6ZmxleC1yb3dcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy1mdWxsIG1kOnctODAgYXNwZWN0LVs0LzNdIG1kOmFzcGVjdC1hdXRvIG1kOmgtNDggb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZVdpdGhGYWxsYmFja1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm9wZXJ0eS5jb3Zlcl9pbWFnZV91cmxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIEpTT04ucGFyc2UoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvcGVydHkuY292ZXJfaW1hZ2VfdXJsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkudXJsO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBjYXRjaCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBcIi9wbGFjZWhvbGRlci1wcm9wZXJ0eS5qcGdcIjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSgpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCIvcGxhY2Vob2xkZXItcHJvcGVydHkuanBnXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PXtwcm9wZXJ0eS5uYW1lIHx8IFwiUHJvcGVydHlcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlciBncm91cC1ob3ZlcjpzY2FsZS0xMDUgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb3BlcnR5LmlzX3BhcnRuZXJfcHJvamVjdCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC00IGxlZnQtNCBiZy1nb2xkIHRleHQtY2hhcmNvYWxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEZlYXR1cmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnQgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LXNvZnQtYnJvd24gZ3JvdXAtaG92ZXI6dGV4dC1nb2xkIHRyYW5zaXRpb24tY29sb3JzIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb3BlcnR5Lm5hbWUgfHwgXCJQcm9wZXJ0eSBUaXRsZVwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC13YXJtLWdyYXkgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TWFwUGluIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntwcm9wZXJ0eS5hcmVhIHx8IFwiTG9jYXRpb25cIn08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm14LTJcIj7igKI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntwcm9wZXJ0eS5kZXZlbG9wZXIgfHwgXCJEZXZlbG9wZXJcIn08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IHRleHQtc20gdGV4dC13YXJtLWdyYXlcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTcXVhcmUgY2xhc3NOYW1lPVwidy00IGgtNCBtci0xIHRleHQtZ29sZFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb3BlcnR5LmFyZWFfdW5pdCB8fCBcInNxZnRcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FsZW5kYXIgY2xhc3NOYW1lPVwidy00IGgtNCBtci0xIHRleHQtZ29sZFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb3BlcnR5LnN0YXR1cyB8fCBcIkF2YWlsYWJsZVwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTaGllbGQgY2xhc3NOYW1lPVwidy00IGgtNCBtci0xIHRleHQtZ29sZFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb3BlcnR5Lmhhc19lc2Nyb3cgPyBcIkVzY3Jvd1wiIDogXCJEaXJlY3RcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgdGV4dC1nb2xkIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb3BlcnR5Lm1pbl9wcmljZSAmJiBwcm9wZXJ0eS5tYXhfcHJpY2VcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHByb3BlcnR5Lm1pbl9wcmljZSA9PT0gcHJvcGVydHkubWF4X3ByaWNlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByb3BlcnR5LnByaWNlX2N1cnJlbmN5IHx8IFwiQUVEXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSAke3Byb3BlcnR5Lm1pbl9wcmljZS50b0xvY2FsZVN0cmluZygpfWBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogYCR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvcGVydHkucHJpY2VfY3VycmVuY3kgfHwgXCJBRURcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9ICR7cHJvcGVydHkubWluX3ByaWNlLnRvTG9jYWxlU3RyaW5nKCl9IC0gJHtwcm9wZXJ0eS5tYXhfcHJpY2UudG9Mb2NhbGVTdHJpbmcoKX1gXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBwcm9wZXJ0eS5taW5fcHJpY2VcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGBGcm9tICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByb3BlcnR5LnByaWNlX2N1cnJlbmN5IHx8IFwiQUVEXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gJHtwcm9wZXJ0eS5taW5fcHJpY2UudG9Mb2NhbGVTdHJpbmcoKX1gXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBwcm9wZXJ0eS5tYXhfcHJpY2VcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGBVcCB0byAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm9wZXJ0eS5wcmljZV9jdXJyZW5jeSB8fCBcIkFFRFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9ICR7cHJvcGVydHkubWF4X3ByaWNlLnRvTG9jYWxlU3RyaW5nKCl9YFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJQcmljZSBvbiBSZXF1ZXN0XCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1nb2xkLzMwIHRleHQtZ29sZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9wZXJ0eS5zYWxlX3N0YXR1cyB8fCBcIkF2YWlsYWJsZVwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB0LTQgYm9yZGVyLXQgYm9yZGVyLWJlaWdlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTYgdGV4dC1zbSB0ZXh0LXdhcm0tZ3JheVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FsZW5kYXIgY2xhc3NOYW1lPVwidy00IGgtNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb3BlcnR5LmNvbXBsZXRpb25fZGF0ZXRpbWVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IG5ldyBEYXRlKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm9wZXJ0eS5jb21wbGV0aW9uX2RhdGV0aW1lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLnRvTG9jYWxlRGF0ZVN0cmluZygpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBwcm9wZXJ0eS5wb3N0X2hhbmRvdmVyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcIlBvc3QgSGFuZG92ZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJUQkRcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIGNsYXNzTmFtZT1cImJnLWJlaWdlIHRleHQtc29mdC1icm93blwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvcGVydHkuc3RhdHVzIHx8IFwiQXZhaWxhYmxlXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3Byb3BlcnR5LmRldmVsb3BlciB8fCBcIkRldmVsb3BlclwifTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvPlxuICAgICAgICApfVxuICAgICAgPC9tYWluPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlTWVtbyIsInVzZUVmZmVjdCIsIlByb3BlcnR5Q2FyZFNrZWxldG9uIiwiRXJyb3JTdGF0ZSIsIkJ1dHRvbiIsIkJhZGdlIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkRpYWxvZyIsIkRpYWxvZ0NvbnRlbnQiLCJEaWFsb2dEZXNjcmlwdGlvbiIsIkRpYWxvZ0hlYWRlciIsIkRpYWxvZ1RpdGxlIiwiRGlhbG9nVHJpZ2dlciIsIklucHV0IiwiTGFiZWwiLCJTZWxlY3QiLCJTZWxlY3RDb250ZW50IiwiU2VsZWN0SXRlbSIsIlNlbGVjdFRyaWdnZXIiLCJTZWxlY3RWYWx1ZSIsIlNsaWRlciIsIlN3aXRjaCIsIkhvbWUiLCJDaGV2cm9uUmlnaHQiLCJCdWlsZGluZzIiLCJNYXBQaW4iLCJDYWxlbmRhciIsIkRvbGxhclNpZ24iLCJUcmVuZGluZ1VwIiwiR3JpZDNYMyIsIkxpc3QiLCJTZWFyY2giLCJTbGlkZXJzSG9yaXpvbnRhbCIsIkJlZCIsIlNxdWFyZSIsIlgiLCJSb3RhdGVDY3ciLCJDaGVjayIsIkJhbmtub3RlIiwiUnVsZXIiLCJDbG9jayIsIkhhbW1lciIsIkhvbWVJY29uIiwiU2hvcHBpbmdDYXJ0IiwiU2hpZWxkIiwiSW1hZ2VXaXRoRmFsbGJhY2siLCJpbml0aWFsRmlsdGVycyIsInByaWNlVW5pdCIsIm1pbkFyZWEiLCJtYXhBcmVhIiwiZGV2ZWxvcG1lbnRTdGF0dXMiLCJ1bml0VHlwZXMiLCJiZWRyb29tcyIsInNhbGVzU3RhdHVzIiwiY29tcGxldGlvbkRhdGUiLCJtaW5QcmljZSIsIm1heFByaWNlIiwiQWxsUHJvcGVydGllc1BhZ2UiLCJvblByb2plY3RTZWxlY3QiLCJvbkJhY2siLCJzZWxlY3RlZERldmVsb3BlciIsImNvbXBsZXRpb25EYXRlT3B0aW9ucyIsImNvbnNvbGUiLCJsb2ciLCJ2aWV3TW9kZSIsInNldFZpZXdNb2RlIiwic29ydEJ5Iiwic2V0U29ydEJ5Iiwic2VhcmNoUXVlcnkiLCJzZXRTZWFyY2hRdWVyeSIsImZpbHRlcnNPcGVuIiwic2V0RmlsdGVyc09wZW4iLCJpc0ZpbHRlck1vZGFsT3BlbiIsInNldElzRmlsdGVyTW9kYWxPcGVuIiwiZmlsdGVycyIsInNldEZpbHRlcnMiLCJwcm9wZXJ0aWVzIiwic2V0UHJvcGVydGllcyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImxlbmd0aCIsImZldGNoIiwidGhlbiIsInJlc3BvbnNlIiwianNvbiIsImRhdGEiLCJzdWNjZXNzIiwiaXRlbXMiLCJjYXRjaCIsImVyciIsIm1lc3NhZ2UiLCJhbGxQcm9wZXJ0aWVzIiwiQXJyYXkiLCJpc0FycmF5IiwiZGV2ZWxvcG1lbnRTdGF0dXNPcHRpb25zIiwidW5pdFR5cGVPcHRpb25zIiwiYmVkcm9vbU9wdGlvbnMiLCJzYWxlc1N0YXR1c09wdGlvbnMiLCJ2YWx1ZSIsImxhYmVsIiwidG9nZ2xlQXJyYXlGaWx0ZXIiLCJhcnJheSIsImluY2x1ZGVzIiwiZmlsdGVyIiwiaXRlbSIsInVwZGF0ZUZpbHRlcnMiLCJrZXkiLCJwcmV2IiwicmVzZXRGaWx0ZXJzIiwiaGFzQWN0aXZlRmlsdGVycyIsImFjdGl2ZUZpbHRlckNvdW50IiwiY291bnQiLCJmaWx0ZXJlZFByb3BlcnRpZXMiLCJ3YXJuIiwiZmlsdGVyZWQiLCJwcm9wZXJ0eSIsIm5hbWUiLCJ0b0xvd2VyQ2FzZSIsImFyZWEiLCJkZXZlbG9wZXIiLCJzdGF0dXMiLCJzb3J0ZWRQcm9wZXJ0aWVzIiwic29ydGVkIiwic29ydCIsImEiLCJiIiwibWluX3ByaWNlIiwiY29tcGxldGlvbl9kYXRldGltZSIsImxvY2FsZUNvbXBhcmUiLCJpc19wYXJ0bmVyX3Byb2plY3QiLCJMdXh1cnlDaGVja2JveEdyb3VwIiwib3B0aW9ucyIsInZhbHVlcyIsIm9uQ2hhbmdlIiwiaWNvbiIsIkljb24iLCJjbGFzc05hbWUiLCJzcGFuIiwibWFwIiwib3B0aW9uIiwiZGl2Iiwib25DbGljayIsImZvcm1hdFByaWNlIiwidG9Mb2NhbGVTdHJpbmciLCJ0b0ZpeGVkIiwiaGVhZGVyIiwidmFyaWFudCIsInNpemUiLCJoMSIsInAiLCJwbGFjZWhvbGRlciIsImUiLCJ0YXJnZXQiLCJvcGVuIiwib25PcGVuQ2hhbmdlIiwiYXNDaGlsZCIsImRpc2FibGVkIiwiY2hlY2tlZCIsIm9uQ2hlY2tlZENoYW5nZSIsInR5cGUiLCJwYXJzZUludCIsIm9uVmFsdWVDaGFuZ2UiLCJtaW4iLCJtYXgiLCJzdGVwIiwiaDMiLCJoNCIsInMiLCJ0IiwiYmVkcm9vbSIsImZpbmQiLCJvcHQiLCJtYWluIiwiZnJvbSIsIl8iLCJpIiwib25SZXRyeSIsImZldGNoUHJvcGVydGllcyIsInNyYyIsImNvdmVyX2ltYWdlX3VybCIsIkpTT04iLCJwYXJzZSIsInVybCIsImFsdCIsIm1heF9wcmljZSIsInByaWNlX2N1cnJlbmN5Iiwic2FsZV9zdGF0dXMiLCJhcmVhX3VuaXQiLCJoYXNfZXNjcm93IiwiRGF0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsInBvc3RfaGFuZG92ZXIiLCJpZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AllPropertiesPage.tsx\n"));

/***/ })

});