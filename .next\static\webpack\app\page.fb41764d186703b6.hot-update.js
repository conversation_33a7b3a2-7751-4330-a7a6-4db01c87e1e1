"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/JoinAsPartnerPage.tsx":
/*!**********************************************!*\
  !*** ./src/components/JoinAsPartnerPage.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JoinAsPartnerPage: function() { return /* binding */ JoinAsPartnerPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Building,Calendar,CheckCircle,DollarSign,Globe,Heart,Mail,Phone,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Building,Calendar,CheckCircle,DollarSign,Globe,Heart,Mail,Phone,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Building,Calendar,CheckCircle,DollarSign,Globe,Heart,Mail,Phone,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Building,Calendar,CheckCircle,DollarSign,Globe,Heart,Mail,Phone,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Building,Calendar,CheckCircle,DollarSign,Globe,Heart,Mail,Phone,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Building,Calendar,CheckCircle,DollarSign,Globe,Heart,Mail,Phone,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Building,Calendar,CheckCircle,DollarSign,Globe,Heart,Mail,Phone,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Building,Calendar,CheckCircle,DollarSign,Globe,Heart,Mail,Phone,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Building,Calendar,CheckCircle,DollarSign,Globe,Heart,Mail,Phone,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Building,Calendar,CheckCircle,DollarSign,Globe,Heart,Mail,Phone,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Building,Calendar,CheckCircle,DollarSign,Globe,Heart,Mail,Phone,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Building,Calendar,CheckCircle,DollarSign,Globe,Heart,Mail,Phone,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Building,Calendar,CheckCircle,DollarSign,Globe,Heart,Mail,Phone,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction JoinAsPartnerPage(param) {\n    let { onBack } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        phone: \"\",\n        company: \"\",\n        experience: \"\",\n        portfolio: \"\",\n        message: \"\"\n    });\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        console.log(\"Partner application submitted:\", formData);\n        // Handle form submission here\n        setFormData({\n            name: \"\",\n            email: \"\",\n            phone: \"\",\n            company: \"\",\n            experience: \"\",\n            portfolio: \"\",\n            message: \"\"\n        });\n    };\n    const partnerBenefits = [\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: \"Competitive Commission\",\n            description: \"Earn up to 40% commission on every successful deal you close\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"Market Leading Support\",\n            description: \"Access to exclusive off-plan projects and marketing materials\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: \"Professional Recognition\",\n            description: \"Join an elite network of certified real estate partners\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: \"Team Support\",\n            description: \"Dedicated account manager and 24/7 technical support\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"International Reach\",\n            description: \"Access to global investor networks and referral systems\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"Premium Projects\",\n            description: \"Exclusive access to luxury developments before public launch\"\n        }\n    ];\n    const requirements = [\n        \"Licensed real estate broker or agent\",\n        \"Minimum 2 years experience in property sales\",\n        \"Proven track record in luxury real estate\",\n        \"Strong network of high-net-worth individuals\",\n        \"Commitment to professional excellence\",\n        \"Fluency in English and Arabic preferred\"\n    ];\n    const supportItems = [\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"Marketing Support\",\n            description: \"Professional marketing materials, brochures, and digital assets\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: \"Training Program\",\n            description: \"Comprehensive training on Dubai market and our exclusive projects\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            title: \"Dedicated Support\",\n            description: \"Personal account manager and priority customer service\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"Lead Generation\",\n            description: \"Access to qualified leads and referral opportunities\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-ivory pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-soft-brown text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onBack,\n                                className: \"text-white hover:bg-white/10 mr-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Back to Home\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gold rounded-2xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-8 h-8 text-soft-brown\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-white mb-6 text-[48px]\",\n                                    children: \"Join As A Partner\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-tan text-xl leading-relaxed\",\n                                    children: \"Partner with Smart Off Plan and unlock exclusive opportunities in Dubai's thriving off-plan property market. Build your business with our premium support.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-soft-brown mb-6 text-[36px] text-[40px]\",\n                                    children: \"Why Partner With Us?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-warm-gray text-lg max-w-3xl mx-auto\",\n                                    children: \"Join Dubai's most trusted off-plan property platform and grow your business with exclusive access to premium developments and comprehensive support.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: partnerBenefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-white rounded-3xl shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] hover:shadow-[0_12px_40px_-4px_rgba(139,115,85,0.15),0_6px_20px_-4px_rgba(139,115,85,0.1)] hover:-translate-y-2 transition-all duration-300 border-0 overflow-hidden group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-8 text-center h-full flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-br from-beige to-ivory rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(benefit.icon, {\n                                                    className: \"w-8 h-8 text-gold\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-soft-brown mb-4 group-hover:text-gold transition-colors\",\n                                                children: benefit.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-warm-gray text-sm leading-relaxed flex-grow\",\n                                                children: benefit.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-gradient-to-br from-beige to-ivory\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-soft-brown mb-8\",\n                                        children: \"Partner Requirements\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: requirements.map((requirement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-5 h-5 text-gold mt-1 mr-3 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-warm-gray\",\n                                                        children: requirement\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8 bg-white rounded-2xl p-6 shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-soft-brown mb-4\",\n                                                children: \"Ready to Get Started?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-warm-gray text-sm mb-4\",\n                                                children: \"Complete the application form and our team will review your profile within 48 hours.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-gold hover:bg-gold/90 text-soft-brown\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Schedule Interview\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-soft-brown mb-8\",\n                                        children: \"What We Provide\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: supportItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-2xl p-6 shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] hover:shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] hover:-translate-y-1 transition-all duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-beige rounded-xl flex items-center justify-center mr-4 flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                className: \"w-6 h-6 text-gold\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                    className: \"text-soft-brown mb-2\",\n                                                                    children: item.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-warm-gray text-sm leading-relaxed\",\n                                                                    children: item.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-soft-brown mb-6 text-[36px] text-[40px]\",\n                                        children: \"Partner Application\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-warm-gray text-lg max-w-2xl mx-auto\",\n                                        children: \"Ready to join our exclusive partner network? Complete the application below and take the first step towards a profitable partnership.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-3xl p-8 shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"name\",\n                                                            className: \"block text-sm text-soft-brown mb-2\",\n                                                            children: \"Full Name *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"name\",\n                                                            name: \"name\",\n                                                            type: \"text\",\n                                                            required: true,\n                                                            value: formData.name,\n                                                            onChange: handleInputChange,\n                                                            placeholder: \"Your full name\",\n                                                            className: \"w-full py-3 border border-soft-gray/30 rounded-xl focus:border-gold focus:ring-gold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"email\",\n                                                            className: \"block text-sm text-soft-brown mb-2\",\n                                                            children: \"Email Address *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"email\",\n                                                            name: \"email\",\n                                                            type: \"email\",\n                                                            required: true,\n                                                            value: formData.email,\n                                                            onChange: handleInputChange,\n                                                            placeholder: \"<EMAIL>\",\n                                                            className: \"w-full py-3 border border-soft-gray/30 rounded-xl focus:border-gold focus:ring-gold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"phone\",\n                                                            className: \"block text-sm text-soft-brown mb-2\",\n                                                            children: \"Phone Number *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"phone\",\n                                                            name: \"phone\",\n                                                            type: \"tel\",\n                                                            required: true,\n                                                            value: formData.phone,\n                                                            onChange: handleInputChange,\n                                                            placeholder: \"+971 50 123 4567\",\n                                                            className: \"w-full py-3 border border-soft-gray/30 rounded-xl focus:border-gold focus:ring-gold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"company\",\n                                                            className: \"block text-sm text-soft-brown mb-2\",\n                                                            children: \"Company/Agency Name *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"company\",\n                                                            name: \"company\",\n                                                            type: \"text\",\n                                                            required: true,\n                                                            value: formData.company,\n                                                            onChange: handleInputChange,\n                                                            placeholder: \"Your company name\",\n                                                            className: \"w-full py-3 border border-soft-gray/30 rounded-xl focus:border-gold focus:ring-gold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"experience\",\n                                                            className: \"block text-sm text-soft-brown mb-2\",\n                                                            children: \"Years of Experience *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"experience\",\n                                                            name: \"experience\",\n                                                            type: \"text\",\n                                                            required: true,\n                                                            value: formData.experience,\n                                                            onChange: handleInputChange,\n                                                            placeholder: \"e.g., 5 years\",\n                                                            className: \"w-full py-3 border border-soft-gray/30 rounded-xl focus:border-gold focus:ring-gold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"portfolio\",\n                                                            className: \"block text-sm text-soft-brown mb-2\",\n                                                            children: \"Portfolio/Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"portfolio\",\n                                                            name: \"portfolio\",\n                                                            type: \"url\",\n                                                            value: formData.portfolio,\n                                                            onChange: handleInputChange,\n                                                            placeholder: \"https://yourwebsite.com\",\n                                                            className: \"w-full py-3 border border-soft-gray/30 rounded-xl focus:border-gold focus:ring-gold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"message\",\n                                                    className: \"block text-sm text-soft-brown mb-2\",\n                                                    children: \"Tell Us About Your Experience *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                    id: \"message\",\n                                                    name: \"message\",\n                                                    required: true,\n                                                    value: formData.message,\n                                                    onChange: handleInputChange,\n                                                    placeholder: \"Describe your real estate experience, specializations, and why you'd like to partner with us...\",\n                                                    rows: 6,\n                                                    className: \"w-full p-4 border border-soft-gray/30 rounded-xl focus:border-gold focus:ring-gold resize-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"submit\",\n                                                className: \"bg-gold hover:bg-gold/90 text-soft-brown px-8 py-3 rounded-xl transition-all duration-300 hover:shadow-[0_4px_16px_-2px_rgba(212,175,55,0.3)] hover:scale-105\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Submit Application\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-soft-brown text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-white mb-6 text-[36px] text-[40px]\",\n                                children: \"Questions About Partnership?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-tan text-lg mb-8 leading-relaxed\",\n                                children: \"Our partnership team is ready to discuss opportunities and answer any questions you may have about joining our exclusive network.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-gold hover:bg-gold/90 text-soft-brown px-8 py-3 text-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Call Partnership Team\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        className: \"border-white text-white hover:bg-white hover:text-soft-brown px-8 py-3 text-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Building_Calendar_CheckCircle_DollarSign_Globe_Heart_Mail_Phone_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Email Us\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n                lineNumber: 429,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\JoinAsPartnerPage.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_s(JoinAsPartnerPage, \"ft4BX/fINxN4wKMWS0ofPIV7VfY=\");\n_c = JoinAsPartnerPage;\nvar _c;\n$RefreshReg$(_c, \"JoinAsPartnerPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/JoinAsPartnerPage.tsx\n"));

/***/ })

});