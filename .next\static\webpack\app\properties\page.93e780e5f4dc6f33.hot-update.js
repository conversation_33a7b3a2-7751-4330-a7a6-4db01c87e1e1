"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/properties/page",{

/***/ "(app-pages-browser)/./src/components/AllPropertiesPage.tsx":
/*!**********************************************!*\
  !*** ./src/components/AllPropertiesPage.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AllPropertiesPage: function() { return /* binding */ AllPropertiesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_skeleton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _ui_empty_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/empty-state */ \"(app-pages-browser)/./src/components/ui/empty-state.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _ui_slider__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ui/slider */ \"(app-pages-browser)/./src/components/ui/slider.tsx\");\n/* harmony import */ var _ui_switch__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ruler.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hammer.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bed.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bath.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Bath,Bed,Building2,Calendar,Check,ChevronRight,Clock,DollarSign,Grid3X3,Hammer,Home,List,MapPin,RotateCcw,Ruler,Search,ShoppingCart,SlidersHorizontal,Square,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./figma/ImageWithFallback */ \"(app-pages-browser)/./src/components/figma/ImageWithFallback.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst initialFilters = {\n    priceUnit: \"total\",\n    minArea: 0,\n    maxArea: 5000,\n    developmentStatus: [],\n    unitTypes: [],\n    bedrooms: [],\n    salesStatus: [],\n    completionDate: \"all\",\n    minPrice: 0,\n    maxPrice: ********\n};\nfunction AllPropertiesPage(param) {\n    let { onProjectSelect, onBack, selectedDeveloper } = param;\n    var _completionDateOptions_find;\n    _s();\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"featured\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filtersOpen, setFiltersOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFilterModalOpen, setIsFilterModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFilters);\n    // Local state for properties\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch properties function\n    const fetchProperties = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            console.log(\"\\uD83D\\uDE80 Fetching properties from API...\");\n            const response = await fetch(\"/api/properties\");\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            console.log(\"✅ Properties fetched successfully:\", data);\n            console.log(\"\\uD83D\\uDCCA Data type:\", typeof data);\n            console.log(\"\\uD83D\\uDCCA Data structure:\", Object.keys(data));\n            // Handle the API response structure\n            let propertiesArray = [];\n            if (data.success && data.data) {\n                // Our API wraps the external response in { success: true, data: externalData }\n                const externalData = data.data;\n                if (externalData.items && Array.isArray(externalData.items)) {\n                    // The external API returns { items: [...], pagination: {...} }\n                    propertiesArray = externalData.items;\n                } else if (Array.isArray(externalData)) {\n                    // Fallback: if external data is directly an array\n                    propertiesArray = externalData;\n                }\n            } else if (Array.isArray(data)) {\n                // If data is directly an array\n                propertiesArray = data;\n            } else if (data.data && Array.isArray(data.data)) {\n                // If data is wrapped in a data property\n                propertiesArray = data.data;\n            } else {\n                // Fallback: try to find any array in the response\n                const possibleArrays = Object.values(data).filter(Array.isArray);\n                if (possibleArrays.length > 0) {\n                    propertiesArray = possibleArrays[0];\n                }\n            }\n            console.log(\"\\uD83D\\uDCCB Properties array:\", propertiesArray);\n            console.log(\"\\uD83D\\uDCCA Properties count:\", propertiesArray.length);\n            setProperties(propertiesArray);\n        } catch (err) {\n            console.error(\"❌ Error fetching properties:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch properties\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Fetch properties on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProperties();\n    }, []);\n    // Use real properties data - ensure it's always an array\n    const allProperties = Array.isArray(properties) ? properties : [];\n    // Filter options\n    const developmentStatusOptions = [\n        \"Presale\",\n        \"Under Construction\",\n        \"Completed\"\n    ];\n    const unitTypeOptions = [\n        \"Apartments\",\n        \"Villa\",\n        \"Townhouse\",\n        \"Duplex\",\n        \"Penthouse\"\n    ];\n    const bedroomOptions = [\n        \"Studio\",\n        \"1 BR\",\n        \"2 BR\",\n        \"3 BR\",\n        \"4 BR\",\n        \"5+ BR\"\n    ];\n    const salesStatusOptions = [\n        \"Announced\",\n        \"Presale (EOI)\",\n        \"Start of Sales\",\n        \"On Sale\",\n        \"Out of Stock\"\n    ];\n    const completionDateOptions = [\n        {\n            value: \"all\",\n            label: \"All Projects\"\n        },\n        {\n            value: \"12months\",\n            label: \"Completing in 12 months\"\n        },\n        {\n            value: \"2years\",\n            label: \"Completing in 2 years\"\n        },\n        {\n            value: \"3years\",\n            label: \"Completing in 3 years\"\n        },\n        {\n            value: \"4years\",\n            label: \"Completing in 4 years\"\n        },\n        {\n            value: \"5years\",\n            label: \"Completing in 5+ years\"\n        }\n    ];\n    // Helper functions for filters\n    const toggleArrayFilter = (array, value)=>{\n        return array.includes(value) ? array.filter((item)=>item !== value) : [\n            ...array,\n            value\n        ];\n    };\n    const updateFilters = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    const resetFilters = ()=>{\n        setFilters(initialFilters);\n    };\n    const hasActiveFilters = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return filters.priceUnit !== \"total\" || filters.minArea !== 0 || filters.maxArea !== 5000 || filters.developmentStatus.length > 0 || filters.unitTypes.length > 0 || filters.bedrooms.length > 0 || filters.salesStatus.length > 0 || filters.completionDate !== \"all\" || filters.minPrice !== 0 || filters.maxPrice !== ********;\n    }, [\n        filters\n    ]);\n    const activeFilterCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        let count = 0;\n        if (filters.priceUnit !== \"total\") count++;\n        if (filters.minArea !== 0 || filters.maxArea !== 5000) count++;\n        if (filters.developmentStatus.length > 0) count++;\n        if (filters.unitTypes.length > 0) count++;\n        if (filters.bedrooms.length > 0) count++;\n        if (filters.salesStatus.length > 0) count++;\n        if (filters.completionDate !== \"all\") count++;\n        if (filters.minPrice !== 0 || filters.maxPrice !== ********) count++;\n        return count;\n    }, [\n        filters\n    ]);\n    // Apply filters to properties\n    const filteredProperties = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // Ensure allProperties is an array before filtering\n        if (!Array.isArray(allProperties)) {\n            console.warn(\"⚠️ allProperties is not an array:\", allProperties);\n            return [];\n        }\n        let filtered = [\n            ...allProperties\n        ];\n        // Apply search query\n        if (searchQuery) {\n            filtered = filtered.filter((property)=>{\n                var _property_title, _property_location_name, _property_location, _property_developer_name, _property_developer;\n                return ((_property_title = property.title) === null || _property_title === void 0 ? void 0 : _property_title.toLowerCase().includes(searchQuery.toLowerCase())) || ((_property_location = property.location) === null || _property_location === void 0 ? void 0 : (_property_location_name = _property_location.name) === null || _property_location_name === void 0 ? void 0 : _property_location_name.toLowerCase().includes(searchQuery.toLowerCase())) || ((_property_developer = property.developer) === null || _property_developer === void 0 ? void 0 : (_property_developer_name = _property_developer.name) === null || _property_developer_name === void 0 ? void 0 : _property_developer_name.toLowerCase().includes(searchQuery.toLowerCase()));\n            });\n        }\n        // Apply developer filter if specified\n        if (selectedDeveloper) {\n            filtered = filtered.filter((property)=>{\n                var _property_developer;\n                return ((_property_developer = property.developer) === null || _property_developer === void 0 ? void 0 : _property_developer.name) === selectedDeveloper.name;\n            });\n        }\n        // Apply area filters\n        filtered = filtered.filter((property)=>property.area >= filters.minArea && property.area <= filters.maxArea);\n        // Apply price filters\n        const getPrice = (property)=>{\n            const price = property.price || 0;\n            return filters.priceUnit === \"sqft\" ? price / property.area : price;\n        };\n        filtered = filtered.filter((property)=>{\n            const price = getPrice(property);\n            return price >= filters.minPrice && price <= filters.maxPrice;\n        });\n        // Apply development status filter\n        if (filters.developmentStatus.length > 0) {\n            filtered = filtered.filter((property)=>filters.developmentStatus.includes(property.status));\n        }\n        // Apply unit type filter\n        if (filters.unitTypes.length > 0) {\n            filtered = filtered.filter((property)=>filters.unitTypes.includes(property.propertyType));\n        }\n        // Apply bedroom filter\n        if (filters.bedrooms.length > 0) {\n            filtered = filtered.filter((property)=>{\n                const bedrooms = \"\".concat(property.bedrooms, \" BR\");\n                return filters.bedrooms.includes(bedrooms);\n            });\n        }\n        return filtered;\n    }, [\n        allProperties,\n        searchQuery,\n        selectedDeveloper,\n        filters\n    ]);\n    const sortedProperties = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const sorted = [\n            ...filteredProperties\n        ];\n        switch(sortBy){\n            case \"price-low\":\n                return sorted.sort((a, b)=>(a.price || 0) - (b.price || 0));\n            case \"price-high\":\n                return sorted.sort((a, b)=>(b.price || 0) - (a.price || 0));\n            case \"completion\":\n                return sorted.sort((a, b)=>(a.completionDate || \"\").localeCompare(b.completionDate || \"\"));\n            case \"location\":\n                return sorted.sort((a, b)=>{\n                    var _a_location, _b_location;\n                    return (((_a_location = a.location) === null || _a_location === void 0 ? void 0 : _a_location.name) || \"\").localeCompare(((_b_location = b.location) === null || _b_location === void 0 ? void 0 : _b_location.name) || \"\");\n                });\n            case \"featured\":\n            default:\n                return sorted.sort((a, b)=>(b.isFeatured ? 1 : 0) - (a.isFeatured ? 1 : 0));\n        }\n    }, [\n        filteredProperties,\n        sortBy\n    ]);\n    const LuxuryCheckboxGroup = (param)=>{\n        let { options, values, onChange, label, icon: Icon } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n            className: \"border-beige/60 shadow-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                    className: \"pb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                className: \"w-5 h-5 text-gold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                    className: \"space-y-3\",\n                    children: options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 h-5 rounded-md border-2 flex items-center justify-center cursor-pointer transition-all duration-200 \".concat(values.includes(option) ? \"bg-gold border-gold text-charcoal shadow-sm\" : \"border-soft-brown/30 hover:border-gold hover:bg-gold/5 hover:shadow-sm\"),\n                                    onClick: ()=>onChange(toggleArrayFilter(values, option)),\n                                    children: values.includes(option) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-3 h-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 43\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm text-warm-gray cursor-pointer flex-1 group-hover:text-soft-brown transition-colors\",\n                                    onClick: ()=>onChange(toggleArrayFilter(values, option)),\n                                    children: option\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, option, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n            lineNumber: 362,\n            columnNumber: 5\n        }, this);\n    };\n    const formatPrice = (value)=>{\n        if (filters.priceUnit === \"sqft\") {\n            return \"\".concat(value.toLocaleString());\n        } else {\n            if (value >= 1000000) {\n                return \"\".concat((value / 1000000).toFixed(1), \"M\");\n            } else if (value >= 1000) {\n                return \"\".concat((value / 1000).toFixed(0), \"K\");\n            }\n            return value.toLocaleString();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-ivory\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-beige shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container py-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: onBack,\n                                                className: \"text-warm-gray hover:text-gold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Home\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 text-warm-gray\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-soft-brown\",\n                                                children: \"Properties\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this),\n                                            selectedDeveloper && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4 text-warm-gray\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gold\",\n                                                        children: selectedDeveloper.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-soft-brown text-[48px] leading-[1.2] py-2\",\n                                        children: selectedDeveloper ? \"\".concat(selectedDeveloper.name) : \"All Developments\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-warm-gray mt-2\",\n                                        children: [\n                                            sortedProperties.length,\n                                            \" properties found\",\n                                            hasActiveFilters && \" (\".concat(activeFilterCount, \" filters applied)\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 max-w-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                placeholder: \"Search properties, locations, developers...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10 bg-white border-beige focus:border-gold rounded-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                                            open: isFilterModalOpen,\n                                            onOpenChange: setIsFilterModalOpen,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"border-soft-brown/30 text-soft-brown hover:bg-soft-brown hover:text-white rounded-xl relative \".concat(hasActiveFilters ? \"bg-gold text-charcoal border-gold\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Filters\",\n                                                            activeFilterCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"ml-2 bg-soft-brown text-white text-xs min-w-[20px] h-5\",\n                                                                children: activeFilterCount\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                                                    className: \"sm:max-w-6xl max-h-[90vh] bg-white flex flex-col overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                                            className: \"flex-shrink-0 pb-6 border-b border-beige\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                                                                className: \"text-2xl text-soft-brown\",\n                                                                                children: \"Advanced Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 489,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogDescription, {\n                                                                                className: \"text-warm-gray mt-2\",\n                                                                                children: \"Refine your property search using the filters below to find properties that match your specific requirements.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 492,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 488,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: resetFilters,\n                                                                                disabled: !hasActiveFilters,\n                                                                                className: \"border-soft-brown/30 transition-all duration-200 \".concat(hasActiveFilters ? \"text-gold border-gold/30 hover:bg-gold/10 hover:border-gold\" : \"text-warm-gray border-beige hover:bg-beige/50\"),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"w-4 h-4 mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 509,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    \"Reset\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 498,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                onClick: ()=>setIsFilterModalOpen(false),\n                                                                                className: \"bg-gold hover:bg-gold/90 text-charcoal\",\n                                                                                children: \"Apply Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 512,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 overflow-y-auto py-8 space-y-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                    className: \"border-gold/20 bg-gradient-to-r from-light-gold/10 to-beige/30\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                                                className: \"flex items-center space-x-2 text-soft-brown text-xl\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"w-6 h-6 text-gold\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 528,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Price Configuration\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 529,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 527,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 526,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                            className: \"space-y-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between p-4 bg-white rounded-xl border border-beige/50\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                                    className: \"w-5 h-5 text-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 536,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-soft-brown font-medium\",\n                                                                                                            children: \"Price Display Mode\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 538,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"text-xs text-warm-gray mt-1\",\n                                                                                                            children: \"Choose how prices are displayed\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 541,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 537,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 535,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                    className: \"text-sm transition-colors \".concat(filters.priceUnit === \"total\" ? \"text-soft-brown\" : \"text-warm-gray\"),\n                                                                                                    children: \"Total Price\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 547,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_switch__WEBPACK_IMPORTED_MODULE_12__.Switch, {\n                                                                                                    checked: filters.priceUnit === \"sqft\",\n                                                                                                    onCheckedChange: (checked)=>updateFilters(\"priceUnit\", checked ? \"sqft\" : \"total\"),\n                                                                                                    className: \"data-[state=checked]:bg-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 556,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                    className: \"text-sm transition-colors \".concat(filters.priceUnit === \"sqft\" ? \"text-soft-brown\" : \"text-warm-gray\"),\n                                                                                                    children: \"Per Sq Ft\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 566,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 546,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 534,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                                    className: \"w-5 h-5 text-gold\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 581,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                    className: \"text-soft-brown font-medium text-lg\",\n                                                                                                    children: [\n                                                                                                        \"Price Range (\",\n                                                                                                        filters.priceUnit === \"sqft\" ? \"AED per Sq Ft\" : \"AED\",\n                                                                                                        \")\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 582,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 580,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-2 gap-4\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Minimum\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 593,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"relative\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray text-sm\",\n                                                                                                                    children: \"AED\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 597,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                                    type: \"number\",\n                                                                                                                    value: filters.minPrice,\n                                                                                                                    onChange: (e)=>updateFilters(\"minPrice\", parseInt(e.target.value) || 0),\n                                                                                                                    className: \"pl-12 border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                                    placeholder: \"0\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 600,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 596,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 592,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Maximum\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 615,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"relative\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray text-sm\",\n                                                                                                                    children: \"AED\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 619,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                                    type: \"number\",\n                                                                                                                    value: filters.maxPrice,\n                                                                                                                    onChange: (e)=>updateFilters(\"maxPrice\", parseInt(e.target.value) || ********),\n                                                                                                                    className: \"pl-12 border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                                    placeholder: \"10,000,000\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                    lineNumber: 622,\n                                                                                                                    columnNumber: 33\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 618,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 614,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 591,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-3 p-4 bg-beige/30 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex justify-between text-sm text-warm-gray\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"AED \",\n                                                                                                                formatPrice(filters.minPrice)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 640,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"AED \",\n                                                                                                                formatPrice(filters.maxPrice)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 641,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 639,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_slider__WEBPACK_IMPORTED_MODULE_11__.Slider, {\n                                                                                                    value: [\n                                                                                                        filters.minPrice,\n                                                                                                        filters.maxPrice\n                                                                                                    ],\n                                                                                                    onValueChange: (param)=>{\n                                                                                                        let [min, max] = param;\n                                                                                                        updateFilters(\"minPrice\", min);\n                                                                                                        updateFilters(\"maxPrice\", max);\n                                                                                                    },\n                                                                                                    max: filters.priceUnit === \"sqft\" ? 5000 : 20000000,\n                                                                                                    min: 0,\n                                                                                                    step: filters.priceUnit === \"sqft\" ? 50 : 50000,\n                                                                                                    className: \"w-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 643,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 638,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 579,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                            className: \"border-beige/60\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                                                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                                className: \"w-5 h-5 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 667,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Area Range\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 668,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 666,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 665,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                                    className: \"space-y-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-2 gap-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Min Sq Ft\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 674,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                            type: \"number\",\n                                                                                                            value: filters.minArea,\n                                                                                                            onChange: (e)=>updateFilters(\"minArea\", parseInt(e.target.value) || 0),\n                                                                                                            className: \"border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                            placeholder: \"0\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 677,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 673,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"space-y-2\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                            className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                            children: \"Max Sq Ft\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 691,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                                            type: \"number\",\n                                                                                                            value: filters.maxArea,\n                                                                                                            onChange: (e)=>updateFilters(\"maxArea\", parseInt(e.target.value) || 5000),\n                                                                                                            className: \"border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                            placeholder: \"5,000\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 694,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 690,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 672,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-2 p-3 bg-beige/20 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex justify-between text-sm text-warm-gray\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                filters.minArea.toLocaleString(),\n                                                                                                                \" sq ft\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 711,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                filters.maxArea.toLocaleString(),\n                                                                                                                \" sq ft\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 714,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 710,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_slider__WEBPACK_IMPORTED_MODULE_11__.Slider, {\n                                                                                                    value: [\n                                                                                                        filters.minArea,\n                                                                                                        filters.maxArea\n                                                                                                    ],\n                                                                                                    onValueChange: (param)=>{\n                                                                                                        let [min, max] = param;\n                                                                                                        updateFilters(\"minArea\", min);\n                                                                                                        updateFilters(\"maxArea\", max);\n                                                                                                    },\n                                                                                                    max: 8000,\n                                                                                                    min: 0,\n                                                                                                    step: 50,\n                                                                                                    className: \"w-full\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                    lineNumber: 718,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 709,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 671,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 664,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                            className: \"border-beige/60\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                                                        className: \"flex items-center space-x-2 text-soft-brown text-lg\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                                className: \"w-5 h-5 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 737,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Project Completion\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 738,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 736,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 735,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                                                className: \"text-xs text-warm-gray uppercase tracking-wide\",\n                                                                                                children: \"Completion Timeframe\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 743,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                                                                value: filters.completionDate,\n                                                                                                onValueChange: (value)=>updateFilters(\"completionDate\", value),\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                                                        className: \"w-full border-beige/50 focus:border-gold rounded-lg\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                                                            placeholder: \"Select completion timeframe\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                            lineNumber: 753,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                        lineNumber: 752,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                                                        children: completionDateOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                                                                value: option.value,\n                                                                                                                children: option.label\n                                                                                                            }, option.value, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                                lineNumber: 757,\n                                                                                                                columnNumber: 35\n                                                                                                            }, this))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                        lineNumber: 755,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 746,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 742,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 741,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 734,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mb-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"w-6 h-6 text-gold\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 774,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-xl text-soft-brown\",\n                                                                                    children: \"Property Characteristics\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 775,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 773,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Development Status\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                                                                                    options: developmentStatusOptions,\n                                                                                    values: filters.developmentStatus,\n                                                                                    onChange: (values)=>updateFilters(\"developmentStatus\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 782,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Unit Type\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                                                                                    options: unitTypeOptions,\n                                                                                    values: filters.unitTypes,\n                                                                                    onChange: (values)=>updateFilters(\"unitTypes\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 793,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Bedrooms\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                                                                                    options: bedroomOptions,\n                                                                                    values: filters.bedrooms,\n                                                                                    onChange: (values)=>updateFilters(\"bedrooms\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 804,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LuxuryCheckboxGroup, {\n                                                                                    label: \"Sales Status\",\n                                                                                    icon: _barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                                                                    options: salesStatusOptions,\n                                                                                    values: filters.salesStatus,\n                                                                                    onChange: (values)=>updateFilters(\"salesStatus\", values)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 815,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 780,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 772,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                    className: \"border-gold/30 bg-gradient-to-r from-gold/5 to-light-gold/10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                        className: \"pt-6\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"text-lg text-soft-brown mb-2\",\n                                                                                            children: \"Filter Summary\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 833,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-warm-gray\",\n                                                                                            children: [\n                                                                                                activeFilterCount,\n                                                                                                \" filters applied •\",\n                                                                                                \" \",\n                                                                                                sortedProperties.length,\n                                                                                                \" properties match your criteria\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 836,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 832,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    onClick: resetFilters,\n                                                                                    className: \"border-gold text-gold hover:bg-gold hover:text-charcoal\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                            className: \"w-4 h-4 mr-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                            lineNumber: 847,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Reset All\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 842,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 831,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 830,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                            value: sortBy,\n                                            onValueChange: setSortBy,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                    className: \"w-40 border-soft-brown/30 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                        placeholder: \"Sort by\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 861,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 860,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"featured\",\n                                                            children: \"Featured\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"price-low\",\n                                                            children: \"Price: Low to High\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 865,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"price-high\",\n                                                            children: \"Price: High to Low\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 866,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"completion\",\n                                                            children: \"Completion Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 867,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: \"location\",\n                                                            children: \"Location\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 868,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 863,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 859,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-soft-brown/30 rounded-xl overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: viewMode === \"grid\" ? \"default\" : \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setViewMode(\"grid\"),\n                                                    className: \"rounded-none \".concat(viewMode === \"grid\" ? \"bg-soft-brown text-white\" : \"text-soft-brown hover:bg-soft-brown/10\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 874,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: viewMode === \"list\" ? \"default\" : \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setViewMode(\"list\"),\n                                                    className: \"rounded-none \".concat(viewMode === \"list\" ? \"bg-soft-brown text-white\" : \"text-soft-brown hover:bg-soft-brown/10\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 873,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this),\n                        hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex flex-wrap items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-warm-gray mr-2\",\n                                    children: \"Active filters:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 15\n                                }, this),\n                                filters.priceUnit !== \"total\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        \"Price per Sq Ft\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>updateFilters(\"priceUnit\", \"total\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 909,\n                                    columnNumber: 17\n                                }, this),\n                                (filters.minArea !== 0 || filters.maxArea !== 5000) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        \"Area: \",\n                                        filters.minArea,\n                                        \"-\",\n                                        filters.maxArea,\n                                        \" sq ft\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>{\n                                                updateFilters(\"minArea\", 0);\n                                                updateFilters(\"maxArea\", 5000);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 926,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 921,\n                                    columnNumber: 17\n                                }, this),\n                                filters.developmentStatus.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            status,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"developmentStatus\", filters.developmentStatus.filter((s)=>s !== status))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 942,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, status, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 936,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.unitTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            type,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"unitTypes\", filters.unitTypes.filter((t)=>t !== type))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 960,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, type, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 954,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.bedrooms.map((bedroom)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            bedroom,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"bedrooms\", filters.bedrooms.filter((b)=>b !== bedroom))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 978,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, bedroom, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 972,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.salesStatus.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"bg-gold/10 text-gold border-gold/30\",\n                                        children: [\n                                            status,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                                onClick: ()=>updateFilters(\"salesStatus\", filters.salesStatus.filter((s)=>s !== status))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 996,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, status, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 990,\n                                        columnNumber: 17\n                                    }, this)),\n                                filters.completionDate !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"bg-gold/10 text-gold border-gold/30\",\n                                    children: [\n                                        (_completionDateOptions_find = completionDateOptions.find((opt)=>opt.value === filters.completionDate)) === null || _completionDateOptions_find === void 0 ? void 0 : _completionDateOptions_find.label,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-3 h-3 ml-1 cursor-pointer\",\n                                            onClick: ()=>updateFilters(\"completionDate\", \"all\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 1017,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 1008,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: resetFilters,\n                                    className: \"text-gold hover:bg-gold/10 text-xs\",\n                                    children: \"Clear all\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                    lineNumber: 1023,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                            lineNumber: 904,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                lineNumber: 410,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container py-8\",\n                children: [\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: Array.from({\n                            length: 6\n                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_2__.PropertyCardSkeleton, {}, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1042,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 1040,\n                        columnNumber: 11\n                    }, this),\n                    error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_empty_state__WEBPACK_IMPORTED_MODULE_3__.ErrorState, {\n                        onRetry: ()=>fetchProperties(),\n                        message: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 1049,\n                        columnNumber: 11\n                    }, this),\n                    !loading && !error && sortedProperties.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                className: \"w-16 h-16 text-warm-gray mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1055,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl text-soft-brown mb-2\",\n                                children: \"No properties found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1056,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-warm-gray mb-6\",\n                                children: \"Try adjusting your filters or search criteria to find more properties.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1059,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: resetFilters,\n                                className: \"bg-gold hover:bg-gold/90 text-charcoal\",\n                                children: \"Reset Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1063,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                        lineNumber: 1054,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            viewMode === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: sortedProperties.map((property)=>{\n                                    var _property_images_, _property_images, _property_location, _property_location1, _property_developer;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        className: \"group cursor-pointer border border-beige hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden rounded-xl\",\n                                        onClick: ()=>onProjectSelect(property),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative aspect-[4/3] overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_13__.ImageWithFallback, {\n                                                        src: ((_property_images = property.images) === null || _property_images === void 0 ? void 0 : (_property_images_ = _property_images[0]) === null || _property_images_ === void 0 ? void 0 : _property_images_.url) || \"/placeholder-property.jpg\",\n                                                        alt: property.title || \"Property\",\n                                                        className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1082,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    property.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: \"absolute top-4 left-4 bg-gold text-charcoal\",\n                                                        children: \"Featured\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1091,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: \"absolute top-4 right-4 bg-black/70 text-white border-0\",\n                                                        children: property.status || \"Available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1095,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1081,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl text-soft-brown group-hover:text-gold transition-colors\",\n                                                                    children: property.title || \"Property Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1102,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-warm-gray mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1106,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: ((_property_location = property.location) === null || _property_location === void 0 ? void 0 : _property_location.name) || ((_property_location1 = property.location) === null || _property_location1 === void 0 ? void 0 : _property_location1.city) || \"Location\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1107,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1105,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1101,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl text-gold\",\n                                                                    children: filters.priceUnit === \"sqft\" ? \"\".concat(property.currency || \"AED\", \" \").concat(Math.round((property.price || 0) / (property.area || 1)).toLocaleString(), \"/sq ft\") : \"\".concat(property.currency || \"AED\", \" \").concat((property.price || 0).toLocaleString())\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1116,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs border-gold/30 text-gold\",\n                                                                    children: property.propertyType || \"Property\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1125,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1115,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-4 text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            className: \"w-4 h-4 mx-auto text-gold\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1135,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-warm-gray\",\n                                                                            children: [\n                                                                                property.bedrooms || 0,\n                                                                                \" BR\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1136,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1134,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                            className: \"w-4 h-4 mx-auto text-gold\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1141,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-warm-gray\",\n                                                                            children: [\n                                                                                property.bathrooms || 0,\n                                                                                \" Bath\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1142,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1140,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                            className: \"w-4 h-4 mx-auto text-gold\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1147,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-warm-gray\",\n                                                                            children: [\n                                                                                property.area || 0,\n                                                                                \" \",\n                                                                                property.areaUnit || \"sqft\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1148,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1146,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1133,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between pt-4 border-t border-beige\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-warm-gray\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                            className: \"w-4 h-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1156,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        property.completionDate || \"TBD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1155,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-warm-gray\",\n                                                                    children: ((_property_developer = property.developer) === null || _property_developer === void 0 ? void 0 : _property_developer.name) || \"Developer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1159,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                            lineNumber: 1154,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                    lineNumber: 1100,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1099,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, property.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 1076,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1074,\n                                columnNumber: 15\n                            }, this),\n                            viewMode === \"list\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: sortedProperties.map((property)=>{\n                                    var _property_images_, _property_images, _property_location, _property_location1, _property_developer, _property_developer1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        className: \"group cursor-pointer border border-beige hover:shadow-lg transition-all duration-300 overflow-hidden rounded-xl\",\n                                        onClick: ()=>onProjectSelect(property),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                            className: \"p-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-full md:w-80 aspect-[4/3] md:aspect-auto md:h-48 overflow-hidden\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_figma_ImageWithFallback__WEBPACK_IMPORTED_MODULE_13__.ImageWithFallback, {\n                                                                src: ((_property_images = property.images) === null || _property_images === void 0 ? void 0 : (_property_images_ = _property_images[0]) === null || _property_images_ === void 0 ? void 0 : _property_images_.url) || \"/placeholder-property.jpg\",\n                                                                alt: property.title || \"Property\",\n                                                                className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1182,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            property.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"absolute top-4 left-4 bg-gold text-charcoal\",\n                                                                children: \"Featured\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1191,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1181,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"text-xl text-soft-brown group-hover:text-gold transition-colors mb-2\",\n                                                                                children: property.title || \"Property Title\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1199,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center text-warm-gray mb-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                                        className: \"w-4 h-4 mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1203,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: ((_property_location = property.location) === null || _property_location === void 0 ? void 0 : _property_location.name) || ((_property_location1 = property.location) === null || _property_location1 === void 0 ? void 0 : _property_location1.city) || \"Location\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1204,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mx-2\",\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1209,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: ((_property_developer = property.developer) === null || _property_developer === void 0 ? void 0 : _property_developer.name) || \"Developer\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1210,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1202,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-4 text-sm text-warm-gray\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                                className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 1216,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            property.bedrooms || 0,\n                                                                                            \" BR\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1215,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                                className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 1220,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            property.bathrooms || 0,\n                                                                                            \" Bath\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1219,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                                className: \"w-4 h-4 mr-1 text-gold\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                                lineNumber: 1224,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            property.area || 0,\n                                                                                            \" \",\n                                                                                            property.areaUnit || \"sqft\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                        lineNumber: 1223,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1214,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1198,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-2xl text-gold mb-2\",\n                                                                                children: filters.priceUnit === \"sqft\" ? \"\".concat(property.currency || \"AED\", \" \").concat(Math.round((property.price || 0) / (property.area || 1)).toLocaleString(), \"/sq ft\") : \"\".concat(property.currency || \"AED\", \" \").concat((property.price || 0).toLocaleString())\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1231,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                className: \"border-gold/30 text-gold\",\n                                                                                children: property.propertyType || \"Property\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                lineNumber: 1241,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                        lineNumber: 1230,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1197,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between pt-4 border-t border-beige\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-6 text-sm text-warm-gray\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Bath_Bed_Building2_Calendar_Check_ChevronRight_Clock_DollarSign_Grid3X3_Hammer_Home_List_MapPin_RotateCcw_Ruler_Search_ShoppingCart_SlidersHorizontal_Square_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                                    lineNumber: 1252,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                property.completionDate || \"TBD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1251,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: \"bg-beige text-soft-brown\",\n                                                                            children: property.status || \"Available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1255,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: ((_property_developer1 = property.developer) === null || _property_developer1 === void 0 ? void 0 : _property_developer1.name) || \"Developer\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                            lineNumber: 1258,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                    lineNumber: 1250,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                                lineNumber: 1249,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                        lineNumber: 1196,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                                lineNumber: 1180,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                            lineNumber: 1179,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, property.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                        lineNumber: 1174,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                                lineNumber: 1172,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n                lineNumber: 1037,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\components\\\\AllPropertiesPage.tsx\",\n        lineNumber: 408,\n        columnNumber: 5\n    }, this);\n}\n_s(AllPropertiesPage, \"RLWQKBGSbsSD5mMFmqXAAW0BhnI=\");\n_c = AllPropertiesPage;\nvar _c;\n$RefreshReg$(_c, \"AllPropertiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AllPropertiesPage.tsx\n"));

/***/ })

});