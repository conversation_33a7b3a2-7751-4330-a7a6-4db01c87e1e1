"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-api/page",{

/***/ "(app-pages-browser)/./src/app/test-api/page.tsx":
/*!***********************************!*\
  !*** ./src/app/test-api/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestApiPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_loading_spinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/loading-spinner */ \"(app-pages-browser)/./src/components/ui/loading-spinner.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TestApiPage() {\n    var _selectedProperty_price, _selectedProperty_location, _selectedProperty_location1, _selectedProperty_location2;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedProperty, setSelectedProperty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fetchProperties = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            console.log(\"Fetching properties from:\", _lib_config__WEBPACK_IMPORTED_MODULE_5__.endpoints.properties.list);\n            const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.api.get(_lib_config__WEBPACK_IMPORTED_MODULE_5__.endpoints.properties.list, {\n            });\n            console.log(\"API Response:\", response);\n            setProperties(response.data || []);\n        } catch (err) {\n            console.error(\"API Error:\", err);\n            setError(err.message || \"Failed to fetch properties\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchPropertyDetail = async (propertyId)=>{\n        setLoading(true);\n        setError(null);\n        try {\n            console.log(\"Fetching property detail for ID:\", propertyId);\n            const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.api.get(_lib_config__WEBPACK_IMPORTED_MODULE_5__.endpoints.properties.detail(propertyId));\n            console.log(\"Property Detail Response:\", response);\n            setSelectedProperty(response.data);\n        } catch (err) {\n            console.error(\"API Error:\", err);\n            setError(err.message || \"Failed to fetch property detail\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold mb-8\",\n                children: \"API Test Page\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 p-4 rounded-lg mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-2\",\n                        children: \"API Configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"External API Base URL:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            \"https://search-listings-production.up.railway.app\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"External API Key:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                             true ? \"✅ Set\" : 0\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Our API Endpoint:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            _lib_config__WEBPACK_IMPORTED_MODULE_5__.endpoints.properties.list\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Full External URL:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            \"https://search-listings-production.up.railway.app\",\n                            \"/v1/properties\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"Test Properties List\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: fetchProperties,\n                                disabled: loading,\n                                className: \"bg-soft-brown hover:bg-deep-brown\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loading_spinner__WEBPACK_IMPORTED_MODULE_3__.LoadingSpinner, {\n                                            size: \"sm\",\n                                            color: \"white\",\n                                            className: \"mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Loading...\"\n                                    ]\n                                }, void 0, true) : \"Fetch Properties\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Error:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            \" \",\n                            error\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this),\n                    properties.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border rounded-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 bg-gray-50 border-b\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold\",\n                                    children: [\n                                        \"Properties Found: \",\n                                        properties.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"divide-y\",\n                                children: properties.slice(0, 5).map((property, index)=>{\n                                    var _property_location, _property_location1, _property_price;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 hover:bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-lg\",\n                                                            children: property.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                (_property_location = property.location) === null || _property_location === void 0 ? void 0 : _property_location.name,\n                                                                \", \",\n                                                                (_property_location1 = property.location) === null || _property_location1 === void 0 ? void 0 : _property_location1.city\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-soft-brown font-semibold\",\n                                                            children: [\n                                                                property.currency,\n                                                                \" \",\n                                                                (_property_price = property.price) === null || _property_price === void 0 ? void 0 : _property_price.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                property.bedrooms,\n                                                                \" bed • \",\n                                                                property.bathrooms,\n                                                                \" bath •\",\n                                                                \" \",\n                                                                property.area,\n                                                                \" \",\n                                                                property.areaUnit\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: ()=>fetchPropertyDetail(property.id),\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    disabled: loading,\n                                                    children: \"View Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, property.id || index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            selectedProperty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Property Detail\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                children: selectedProperty.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Basic Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Price:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.currency,\n                                                            \" \",\n                                                            (_selectedProperty_price = selectedProperty.price) === null || _selectedProperty_price === void 0 ? void 0 : _selectedProperty_price.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Type:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.propertyType\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Status:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.status\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Bedrooms:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.bedrooms\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Bathrooms:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.bathrooms\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Area:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.area,\n                                                            \" \",\n                                                            selectedProperty.areaUnit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Location\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Area:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            (_selectedProperty_location = selectedProperty.location) === null || _selectedProperty_location === void 0 ? void 0 : _selectedProperty_location.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"City:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            (_selectedProperty_location1 = selectedProperty.location) === null || _selectedProperty_location1 === void 0 ? void 0 : _selectedProperty_location1.city\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Country:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            (_selectedProperty_location2 = selectedProperty.location) === null || _selectedProperty_location2 === void 0 ? void 0 : _selectedProperty_location2.country\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            selectedProperty.developer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold mb-2\",\n                                                        children: \"Developer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: selectedProperty.developer.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            selectedProperty.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"Description\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: selectedProperty.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this),\n                            selectedProperty.amenities && selectedProperty.amenities.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"Amenities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: selectedProperty.amenities.map((amenity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs\",\n                                                children: amenity\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 25\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 17\n                            }, this),\n                            selectedProperty.images && selectedProperty.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: [\n                                            \"Images (\",\n                                            selectedProperty.images.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-2\",\n                                        children: selectedProperty.images.slice(0, 4).map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square bg-gray-200 rounded overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: image.url,\n                                                    alt: image.alt || \"Property image \".concat(index + 1),\n                                                    className: \"w-full h-full object-cover\",\n                                                    onError: (e)=>{\n                                                        e.target.src = \"/placeholder-image.jpg\";\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, this),\n            (properties.length > 0 || selectedProperty) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Raw API Response\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto max-h-96\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"text-xs\",\n                            children: JSON.stringify(selectedProperty || properties, null, 2)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(TestApiPage, \"JL3xCPs5dcubiOi8zpt6zvQ90YQ=\");\n_c = TestApiPage;\nvar _c;\n$RefreshReg$(_c, \"TestApiPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-api/page.tsx\n"));

/***/ })

});