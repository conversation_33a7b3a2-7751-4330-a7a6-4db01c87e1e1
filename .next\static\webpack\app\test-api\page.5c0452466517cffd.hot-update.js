"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-api/page",{

/***/ "(app-pages-browser)/./src/app/test-api/page.tsx":
/*!***********************************!*\
  !*** ./src/app/test-api/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestApiPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_loading_spinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/loading-spinner */ \"(app-pages-browser)/./src/components/ui/loading-spinner.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TestApiPage() {\n    var _selectedProperty_price, _selectedProperty_location, _selectedProperty_location1, _selectedProperty_location2;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedProperty, setSelectedProperty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fetchProperties = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            console.log(\"Fetching properties from:\", _lib_config__WEBPACK_IMPORTED_MODULE_5__.endpoints.properties.list);\n            const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.api.get(_lib_config__WEBPACK_IMPORTED_MODULE_5__.endpoints.properties.list, {\n                params: {\n                    page: 1,\n                    limit: 10\n                }\n            });\n            console.log(\"API Response:\", response);\n            setProperties(response.data || []);\n        } catch (err) {\n            console.error(\"API Error:\", err);\n            setError(err.message || \"Failed to fetch properties\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchPropertyDetail = async (propertyId)=>{\n        setLoading(true);\n        setError(null);\n        try {\n            console.log(\"Fetching property detail for ID:\", propertyId);\n            const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.api.get(_lib_config__WEBPACK_IMPORTED_MODULE_5__.endpoints.properties.detail(propertyId));\n            console.log(\"Property Detail Response:\", response);\n            setSelectedProperty(response.data);\n        } catch (err) {\n            console.error(\"API Error:\", err);\n            setError(err.message || \"Failed to fetch property detail\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold mb-8\",\n                children: \"API Test Page\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 p-4 rounded-lg mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-2\",\n                        children: \"API Configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"External API Base URL:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            \"https://search-listings-production.up.railway.app\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"External API Key:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                             true ? \"✅ Set\" : 0\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Our API Endpoint:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            _lib_config__WEBPACK_IMPORTED_MODULE_5__.endpoints.properties.list\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Full External URL:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            \"https://search-listings-production.up.railway.app\",\n                            \"/v1/properties\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"Test Properties List\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: fetchProperties,\n                                disabled: loading,\n                                className: \"bg-soft-brown hover:bg-deep-brown\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loading_spinner__WEBPACK_IMPORTED_MODULE_3__.LoadingSpinner, {\n                                            size: \"sm\",\n                                            color: \"white\",\n                                            className: \"mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Loading...\"\n                                    ]\n                                }, void 0, true) : \"Fetch Properties\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Error:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            \" \",\n                            error\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this),\n                    properties.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border rounded-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 bg-gray-50 border-b\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold\",\n                                    children: [\n                                        \"Properties Found: \",\n                                        properties.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"divide-y\",\n                                children: properties.slice(0, 5).map((property, index)=>{\n                                    var _property_location, _property_location1, _property_price;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 hover:bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-lg\",\n                                                            children: property.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                (_property_location = property.location) === null || _property_location === void 0 ? void 0 : _property_location.name,\n                                                                \", \",\n                                                                (_property_location1 = property.location) === null || _property_location1 === void 0 ? void 0 : _property_location1.city\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-soft-brown font-semibold\",\n                                                            children: [\n                                                                property.currency,\n                                                                \" \",\n                                                                (_property_price = property.price) === null || _property_price === void 0 ? void 0 : _property_price.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                property.bedrooms,\n                                                                \" bed • \",\n                                                                property.bathrooms,\n                                                                \" bath •\",\n                                                                \" \",\n                                                                property.area,\n                                                                \" \",\n                                                                property.areaUnit\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: ()=>fetchPropertyDetail(property.id),\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    disabled: loading,\n                                                    children: \"View Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, property.id || index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            selectedProperty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Property Detail\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                children: selectedProperty.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Basic Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Price:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.currency,\n                                                            \" \",\n                                                            (_selectedProperty_price = selectedProperty.price) === null || _selectedProperty_price === void 0 ? void 0 : _selectedProperty_price.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Type:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.propertyType\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Status:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.status\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Bedrooms:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.bedrooms\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Bathrooms:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.bathrooms\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Area:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            selectedProperty.area,\n                                                            \" \",\n                                                            selectedProperty.areaUnit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Location\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Area:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            (_selectedProperty_location = selectedProperty.location) === null || _selectedProperty_location === void 0 ? void 0 : _selectedProperty_location.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"City:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            (_selectedProperty_location1 = selectedProperty.location) === null || _selectedProperty_location1 === void 0 ? void 0 : _selectedProperty_location1.city\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Country:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            (_selectedProperty_location2 = selectedProperty.location) === null || _selectedProperty_location2 === void 0 ? void 0 : _selectedProperty_location2.country\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            selectedProperty.developer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold mb-2\",\n                                                        children: \"Developer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: selectedProperty.developer.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            selectedProperty.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"Description\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: selectedProperty.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this),\n                            selectedProperty.amenities && selectedProperty.amenities.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"Amenities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: selectedProperty.amenities.map((amenity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs\",\n                                                children: amenity\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 25\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 17\n                            }, this),\n                            selectedProperty.images && selectedProperty.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: [\n                                            \"Images (\",\n                                            selectedProperty.images.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-2\",\n                                        children: selectedProperty.images.slice(0, 4).map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square bg-gray-200 rounded overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: image.url,\n                                                    alt: image.alt || \"Property image \".concat(index + 1),\n                                                    className: \"w-full h-full object-cover\",\n                                                    onError: (e)=>{\n                                                        e.target.src = \"/placeholder-image.jpg\";\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, this),\n            (properties.length > 0 || selectedProperty) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Raw API Response\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto max-h-96\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"text-xs\",\n                            children: JSON.stringify(selectedProperty || properties, null, 2)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\src\\\\app\\\\test-api\\\\page.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(TestApiPage, \"JL3xCPs5dcubiOi8zpt6zvQ90YQ=\");\n_c = TestApiPage;\nvar _c;\n$RefreshReg$(_c, \"TestApiPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-api/page.tsx\n"));

/***/ })

});