import { useState, useEffect } from 'react';
import { SplashScreen } from './components/SplashScreen';
import { Navbar } from './components/Navbar';
import { HeroSection } from './components/HeroSection';
import { FeaturedProjects } from './components/FeaturedProjects';
import { PropertyFilters } from './components/PropertyFilters';
import { DevelopersListing } from './components/DevelopersListing';
import { PropertyListings } from './components/PropertyListings';
import { MarketInfo } from './components/MarketInfo';
import { AboutCompany } from './components/AboutCompany';
import { Testimonials } from './components/Testimonials';
import { ContactUs } from './components/ContactUs';
import { ContactUsPage } from './components/ContactUsPage';
import { ContactInfoPage } from './components/ContactInfoPage';
import { PropertyDetailPage } from './components/PropertyDetailPage';
import { DeveloperDetailPage } from './components/DeveloperDetailPage';
import { AllPropertiesPage } from './components/AllPropertiesPage';
import { OffPlanInvestmentPage } from './components/OffPlanInvestmentPage';
import { MarketAnalysisPage } from './components/MarketAnalysisPage';
import { PropertyManagementPage } from './components/PropertyManagementPage';
import { LegalAssistancePage } from './components/LegalAssistancePage';
import { AfterSalesSupportPage } from './components/AfterSalesSupportPage';
import { CompanyFormationPage } from './components/CompanyFormationPage';
import { MortgagesPage } from './components/MortgagesPage';
import { GoldenVisaPage } from './components/GoldenVisaPage';
import { JoinUsPage } from './components/JoinUsPage';
import { JoinAsPartnerPage } from './components/JoinAsPartnerPage';
import { BecomeFranchiseePage } from './components/BecomeFranchiseePage';
import { AboutUsPage } from './components/AboutUsPage';
import { DevelopersPage } from './components/DevelopersPage';
import { PrivacyPolicyPage } from './components/PrivacyPolicyPage';
import { TermsOfServicePage } from './components/TermsOfServicePage';
import { CookiePolicyPage } from './components/CookiePolicyPage';
import { AreaDetailPage } from './components/AreaDetailPage';

export default function App() {
  // Enhanced splash screen state management for smooth transitions
  const [showSplash, setShowSplash] = useState(true);
  const [splashExiting, setSplashExiting] = useState(false);
  const [isMainContentReady, setIsMainContentReady] = useState(false);
  const [isLogoClickSplash, setIsLogoClickSplash] = useState(false);
  const [contentVisible, setContentVisible] = useState(false);
  
  // Existing app state
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [selectedDeveloper, setSelectedDeveloper] = useState<any>(null);
  const [selectedArea, setSelectedArea] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState('home');

  // Session management for splash screen
  useEffect(() => {
    // Check if user has already seen splash screen in this session
    const hasSeenSplash = sessionStorage.getItem('smart-off-plan-splash-seen');
    
    if (hasSeenSplash) {
      // Skip splash screen for this session with immediate content display
      setShowSplash(false);
      setSplashExiting(false);
      setIsMainContentReady(true);
      setContentVisible(true);
    } else {
      // Pre-render main content but keep it hidden for smooth transition
      setTimeout(() => {
        setIsMainContentReady(true);
      }, 2000); // Pre-render before splash completes
    }
  }, []);

  // Enhanced splash screen completion with perfect timing
  const handleSplashComplete = () => {
    // Mark splash as seen for this session (unless it's a logo click splash)
    if (!isLogoClickSplash) {
      sessionStorage.setItem('smart-off-plan-splash-seen', 'true');
    }
    
    // Start smooth exit sequence
    setSplashExiting(true);
    
    // If it was a logo click splash, reset navigation states
    if (isLogoClickSplash) {
      setCurrentPage('home');
      setSelectedProject(null);
      setSelectedDeveloper(null);
      setSelectedArea(null);
      setIsLogoClickSplash(false);
    }
    
    // Coordinate splash exit with content entrance for seamless transition
    setTimeout(() => {
      // Start content entrance while splash is still visible but fading
      setContentVisible(true);
      
      // Remove splash after content starts animating in
      setTimeout(() => {
        setShowSplash(false);
        setSplashExiting(false);
        
        // Smooth scroll to top for logo click splashes
        if (isLogoClickSplash) {
          window.scrollTo({ top: 0, behavior: 'smooth' });
        }
      }, 400); // Allow overlap for smooth transition
    }, 600); // Start content transition before splash fully exits
  };

  // Enhanced logo click handler with proper state management
  const handleLogoClick = () => {
    // Set flag to indicate this is a logo click splash
    setIsLogoClickSplash(true);
    
    // Reset all transition states for fresh animation
    setContentVisible(false);
    setSplashExiting(false);
    
    // Pre-render content for smooth transition
    setIsMainContentReady(true);
    
    // Show splash screen again
    setShowSplash(true);
    
    // Don't update session storage for logo clicks - cinematic experience every time
  };

  // Existing navigation handlers - preserved exactly as they were
  const handleProjectSelect = (project: any) => {
    setSelectedProject(project);
    setSelectedDeveloper(null);
    setSelectedArea(null);
    setCurrentPage('property-detail');
  };

  const handleDeveloperSelect = (developer: any) => {
    setSelectedDeveloper(developer);
    setSelectedProject(null);
    setSelectedArea(null);
    setCurrentPage('developer-properties');
  };

  const handleAreaSelect = (area: any) => {
    setSelectedArea(area);
    setSelectedProject(null);
    setSelectedDeveloper(null);
    setCurrentPage('area-detail');
  };

  const handlePageNavigation = (page: string) => {
    // Handle contact navigation - always go to dedicated contact page
    if (page === 'contact') {
      setCurrentPage('contact-info');
      setSelectedProject(null);
      setSelectedDeveloper(null);
      setSelectedArea(null);
      window.scrollTo({ top: 0, behavior: 'smooth' });
      return;
    }

    // Handle regular navigation
    setCurrentPage(page);
    setSelectedProject(null);
    setSelectedDeveloper(null);
    setSelectedArea(null);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleBackToHome = () => {
    setCurrentPage('home');
    setSelectedProject(null);
    setSelectedDeveloper(null);
    setSelectedArea(null);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Existing footer component - preserved exactly
  const renderFooter = () => (
    <footer className="bg-soft-brown text-white">
      <div className="section-padding">
        <div className="container">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
            
            {/* Company Info */}
            <div>
              <h3 className="mb-6 text-white">Smart Off Plan</h3>
              <p className="text-tan text-sm mb-6 leading-relaxed">
                Your trusted partner for Dubai developments. 
                Connecting international investors with premium off-plan opportunities.
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-gold hover:text-soft-brown transition-all duration-300 cursor-pointer">
                  <span className="text-sm">f</span>
                </div>
                <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-gold hover:text-soft-brown transition-all duration-300 cursor-pointer">
                  <span className="text-sm">t</span>
                </div>
                <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-gold hover:text-soft-brown transition-all duration-300 cursor-pointer">
                  <span className="text-sm">in</span>
                </div>
                <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-gold hover:text-soft-brown transition-all duration-300 cursor-pointer">
                  <span className="text-sm">ig</span>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="mb-6 text-white">Quick Links</h4>
              <ul className="space-y-3">
                <li>
                  <button 
                    onClick={handleBackToHome} 
                    className="text-tan hover:text-gold transition-colors text-sm text-left"
                  >
                    Home
                  </button>
                </li>
                <li>
                  <button 
                    onClick={() => handlePageNavigation('all-properties')} 
                    className="text-tan hover:text-gold transition-colors text-sm text-left"
                  >
                    Projects
                  </button>
                </li>
                <li>
                  <button 
                    onClick={() => handlePageNavigation('developers')} 
                    className="text-tan hover:text-gold transition-colors text-sm text-left"
                  >
                    Developers
                  </button>
                </li>
                <li>
                  <button 
                    onClick={() => handlePageNavigation('about-us')} 
                    className="text-tan hover:text-gold transition-colors text-sm text-left"
                  >
                    About Us
                  </button>
                </li>
                <li>
                  <button 
                    onClick={() => handlePageNavigation('contact')} 
                    className="text-tan hover:text-gold transition-colors text-sm text-left"
                  >
                    Contact
                  </button>
                </li>
              </ul>
            </div>

            {/* Services */}
            <div>
              <h4 className="mb-6 text-white">Services</h4>
              <ul className="space-y-3">
                <li>
                  <button 
                    onClick={() => handlePageNavigation('company-formation')} 
                    className="text-tan hover:text-gold transition-colors text-sm text-left"
                  >
                    Company Formation
                  </button>
                </li>
                <li>
                  <button 
                    onClick={() => handlePageNavigation('mortgages')} 
                    className="text-tan hover:text-gold transition-colors text-sm text-left"
                  >
                    Mortgages
                  </button>
                </li>
                <li>
                  <button 
                    onClick={() => handlePageNavigation('golden-visa')} 
                    className="text-tan hover:text-gold transition-colors text-sm text-left"
                  >
                    Golden Visa
                  </button>
                </li>
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h4 className="mb-6 text-white">Get In Touch</h4>
              <div className="space-y-4">
                <div className="text-tan text-sm">
                  <div className="mb-3">📞 +971 4 123 4567</div>
                  <div className="mb-3">📧 <EMAIL></div>
                  <div className="mb-3">📍 Business Bay, Dubai, UAE</div>
                </div>
                <div>
                  <h5 className="text-white mb-3">Working Hours</h5>
                  <div className="text-tan text-sm">
                    <div>Mon - Fri: 9:00 AM - 7:00 PM</div>
                    <div>Sat: 10:00 AM - 4:00 PM</div>
                  </div>
                </div>
              </div>
            </div>

          </div>

          {/* Luxury Divider */}
          <div className="my-12 h-px bg-gradient-to-r from-transparent via-gold to-transparent"></div>

          {/* Bottom Footer */}
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-tan text-sm">
              © 2024 Smart Off Plan. All rights reserved.
            </div>
            <div className="flex space-x-8 mt-4 md:mt-0">
              <button 
                onClick={() => handlePageNavigation('privacy-policy')} 
                className="text-tan hover:text-gold transition-colors text-sm"
              >
                Privacy Policy
              </button>
              <button 
                onClick={() => handlePageNavigation('terms-of-service')} 
                className="text-tan hover:text-gold transition-colors text-sm"
              >
                Terms of Service
              </button>
              <button 
                onClick={() => handlePageNavigation('cookie-policy')} 
                className="text-tan hover:text-gold transition-colors text-sm"
              >
                Cookie Policy
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );

  // Main application content generator
  const mainContent = () => {
    // Render different pages based on current state - preserved exactly as before
    switch (currentPage) {
      case 'all-properties':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <AllPropertiesPage 
              onProjectSelect={handleProjectSelect}
              onBack={handleBackToHome}
            />
            {renderFooter()}
          </div>
        );

      case 'developer-properties':
        if (selectedDeveloper) {
          return (
            <div className="min-h-screen bg-ivory">
              <Navbar 
                onNavigate={handlePageNavigation} 
                onLogoClick={handleLogoClick}
                currentPage={currentPage} 
              />
              <AllPropertiesPage 
                onProjectSelect={handleProjectSelect}
                onBack={handleBackToHome}
                selectedDeveloper={selectedDeveloper}
              />
              {renderFooter()}
            </div>
          );
        }
        break;

      case 'area-detail':
        if (selectedArea) {
          return (
            <div className="min-h-screen bg-ivory">
              <AreaDetailPage 
                area={selectedArea} 
                onBack={handleBackToHome}
                onProjectSelect={handleProjectSelect}
                onNavigate={handlePageNavigation}
                onLogoClick={handleLogoClick}
              />
              {renderFooter()}
            </div>
          );
        }
        break;

      case 'property-detail':
        if (selectedProject) {
          return (
            <div className="min-h-screen bg-ivory">
              <Navbar 
                onNavigate={handlePageNavigation} 
                onLogoClick={handleLogoClick}
                currentPage={currentPage} 
              />
              <PropertyDetailPage project={selectedProject} onBack={handleBackToHome} />
              {renderFooter()}
            </div>
          );
        }
        break;

      case 'developer-detail':
        if (selectedDeveloper) {
          return (
            <div className="min-h-screen bg-ivory">
              <Navbar 
                onNavigate={handlePageNavigation} 
                onLogoClick={handleLogoClick}
                currentPage={currentPage} 
              />
              <DeveloperDetailPage developer={selectedDeveloper} onBack={handleBackToHome} />
              {renderFooter()}
            </div>
          );
        }
        break;

      case 'contact-page':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <ContactUsPage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'contact-info':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage='contact'
            />
            <ContactInfoPage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'off-plan-investment':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <OffPlanInvestmentPage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'market-analysis':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <MarketAnalysisPage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'property-management':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <PropertyManagementPage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'legal-assistance':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <LegalAssistancePage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'after-sales-support':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <AfterSalesSupportPage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'company-formation':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <CompanyFormationPage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'mortgages':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <MortgagesPage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'golden-visa':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <GoldenVisaPage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'join-us':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <JoinUsPage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'join-as-partner':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <JoinAsPartnerPage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'become-franchisee':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <BecomeFranchiseePage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'about-us':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <AboutUsPage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'developers':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <DevelopersPage onBack={handleBackToHome} onDeveloperSelect={handleDeveloperSelect} />
            {renderFooter()}
          </div>
        );

      case 'privacy-policy':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <PrivacyPolicyPage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'terms-of-service':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <TermsOfServicePage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      case 'cookie-policy':
        return (
          <div className="min-h-screen bg-ivory">
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />
            <CookiePolicyPage onBack={handleBackToHome} />
            {renderFooter()}
          </div>
        );

      default:
        // Default home page view - preserved exactly as before
        return (
          <div className="min-h-screen bg-ivory">
            {/* Navigation */}
            <Navbar 
              onNavigate={handlePageNavigation} 
              onLogoClick={handleLogoClick}
              currentPage={currentPage} 
            />

            {/* Main Content */}
            <main>
              {/* Hero Section */}
              <HeroSection />

              {/* Featured Projects */}
              <FeaturedProjects 
                onProjectSelect={handleProjectSelect} 
                onViewAllProjects={() => handlePageNavigation('all-properties')}
              />

              {/* Property Filters & Map */}
              <PropertyFilters onPropertySelect={handleProjectSelect} />

              {/* Partners Section */}
              <DevelopersListing 
                onPartnerSelect={handleDeveloperSelect} 
                displayMode="simple"
                maxItems={8}
              />

              {/* Property Listings */}
              <PropertyListings 
                onProjectSelect={handleProjectSelect} 
                onLoadMore={() => handlePageNavigation('all-properties')}
              />

              {/* Market Information */}
              <MarketInfo onAreaSelect={handleAreaSelect} />

              {/* About Company */}
              <AboutCompany />

              {/* Testimonials */}
              <Testimonials />

              {/* Contact Us */}
              <ContactUs />
            </main>

            {renderFooter()}
          </div>
        );
    }

    // Fallback to home page
    return (
      <div className="min-h-screen bg-ivory">
        <Navbar 
          onNavigate={handlePageNavigation} 
          onLogoClick={handleLogoClick}
          currentPage={currentPage} 
        />
        <HeroSection />
        {renderFooter()}
      </div>
    );
  };

  // Determine which classes to apply for smooth transitions
  const getContentClasses = () => {
    const baseClasses = "app-content";
    
    if (!contentVisible && showSplash) {
      return `${baseClasses} app-content-hidden`;
    } else if (contentVisible && showSplash) {
      return `${baseClasses} app-content-entering`;
    } else if (!showSplash) {
      return `${baseClasses} app-content-visible`;
    }
    
    return baseClasses;
  };

  return (
    <div className="app-container">
      {/* Splash screen with enhanced exit coordination */}
      {showSplash && (
        <div className={`splash-overlay ${splashExiting ? 'splash-overlay-exiting' : ''}`}>
          <SplashScreen onComplete={handleSplashComplete} />
        </div>
      )}
      
      {/* Main content with smooth entrance animation */}
      {isMainContentReady && (
        <div className={getContentClasses()}>
          {mainContent()}
        </div>
      )}
    </div>
  );
}