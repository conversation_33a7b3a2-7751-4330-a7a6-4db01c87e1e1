import { But<PERSON> } from './ui/button';
import { ArrowLeft } from 'lucide-react';

interface MarketAnalysisPageProps {
  onBack?: () => void;
}

export function MarketAnalysisPage({ onBack }: MarketAnalysisPageProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-ivory to-beige">
      {/* Breadcrumb Navigation */}
      <div className="bg-white border-b border-soft-brown/10">
        <div className="container py-4">
          <div className="flex items-center gap-3">
            <Button
              onClick={onBack}
              variant="ghost"
              size="sm"
              className="text-soft-brown hover:text-gold hover:bg-beige/50 transition-all duration-300 rounded-xl px-3 py-2"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
            <span className="text-warm-gray">•</span>
            <span className="text-soft-brown">Market Analysis</span>
          </div>
        </div>
      </div>
      
      {/* Page Header */}
      <section className="section-padding bg-gradient-to-br from-beige to-ivory">
        <div className="container">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-soft-brown mb-6">Market Analysis</h1>
            <p className="text-warm-gray text-xl leading-relaxed">
              Comprehensive market insights and analysis to help you make informed investment decisions in Dubai's real estate market.
            </p>
          </div>
        </div>
      </section>

      {/* Content will be added here in future */}
      <section className="section-padding">
        <div className="container">
          <div className="text-center">
            <p className="text-warm-gray text-lg">
              Content coming soon...
            </p>
          </div>
        </div>
      </section>

    </div>
  );
}