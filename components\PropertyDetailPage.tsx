import { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Slider } from './ui/slider';
import { 
  ArrowLeft, 
  Download, 
  MapPin, 
  Calendar, 
  Building2,
  Phone,
  Mail,
  Send,
  CheckCircle2,
  Star,
  Bath,
  Bed,
  Square,
  Car,
  Wifi,
  Shield,
  Heart,
  User,
  ExternalLink,
  Calculator,
  TrendingUp,
  Share2,
  Bookmark,
  Eye,
  ChevronLeft,
  ChevronRight,
  X,
  Play,
  Expand,
  Camera,
  FileText,
  DollarSign,
  PieChart,
  BarChart3,
  Percent,
  Clock,
  Train,
  Plane,
  ShoppingBag,
  Building,
  GraduationCap,
  Dumbbell,
  Waves,
  Trees,
  Users,
  Baby,
  Navigation,
  Ruler,
  Home,
  CreditCard,
  Banknote,
  Target,
  Wallet,
  Award,
  Globe,
  Zap,
  Sparkles,
  VideoIcon as Video,
  Copy,
  Facebook,
  Twitter,
  Linkedin,
  MessageCircle,
  PhoneCall,
  CalendarDays,
  ChevronDown,
  ChevronUp,
  Info,
  AlertCircle,
  ThumbsUp,
  Filter,
  SortAsc,
  Grid,
  List,
  Search,
  RefreshCcw
} from 'lucide-react';
import { ImageWithFallback } from './figma/ImageWithFallback';

interface Project {
  id: number;
  name: string;
  location: string;
  price: string;
  image: string;
  completion: string;
  description?: string;
  developer: string;
  status: string;
  coordinates?: [number, number];
}

interface PropertyDetailPageProps {
  project: Project;
  onBack: () => void;
}

export function PropertyDetailPage({ project, onBack }: PropertyDetailPageProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [selectedFloorPlan, setSelectedFloorPlan] = useState(0);
  const [isDownloadDialogOpen, setIsDownloadDialogOpen] = useState(false);
  const [isImageGalleryOpen, setIsImageGalleryOpen] = useState(false);
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);
  const [isCallbackDialogOpen, setIsCallbackDialogOpen] = useState(false);
  const [isViewingDialogOpen, setIsViewingDialogOpen] = useState(false);
  const [isInvestmentCalculatorOpen, setIsInvestmentCalculatorOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [calculatorTab, setCalculatorTab] = useState('roi');
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  
  const [downloadFormData, setDownloadFormData] = useState({
    name: '',
    email: '',
    phone: ''
  });

  const [contactFormData, setContactFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    message: '',
    interest: 'buying'
  });

  const [callbackFormData, setCallbackFormData] = useState({
    name: '',
    phone: '',
    preferredTime: ''
  });

  const [viewingFormData, setViewingFormData] = useState({
    name: '',
    email: '',
    phone: '',
    date: '',
    time: ''
  });

  // Investment Calculator Data
  const safeParsePrice = (price: string | undefined): number => {
    if (!price) return 1200000;
    const numericValue = price.replace(/[^0-9.]/g, '');
    const parsed = parseFloat(numericValue);
    return isNaN(parsed) ? 1200000 : parsed;
  };

  const [investmentData, setInvestmentData] = useState({
    propertyPrice: safeParsePrice(project?.price),
    downPayment: 25,
    mortgageTerm: 25,
    interestRate: 4.5,
    expectedRentalYield: 8.5,
    capitalAppreciation: 6,
    holdingPeriod: 10,
    maintenanceCosts: 2,
    managementFees: 5,
  });

  // Property coordinates
  const propertyCoordinates = project?.coordinates || (() => {
    if (!project?.location) return [25.2048, 55.2708];
    
    switch (project.location) {
      case 'Dubai Marina':
        return [25.0772, 55.1384];
      case 'Downtown Dubai':
        return [25.1972, 55.2744];
      case 'Palm Jumeirah':
        return [25.1124, 55.1390];
      case 'Business Bay':
        return [25.1870, 55.2631];
      case 'Dubai Creek Harbour':
        return [25.1838, 55.3167];
      case 'DIFC':
        return [25.2131, 55.2796];
      case 'JBR':
        return [25.0657, 55.1364];
      default:
        return [25.2048, 55.2708];
    }
  })();

  // Property Images
  const propertyImages = [
    {
      id: 1,
      src: "https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      category: "exterior",
      title: "Building Exterior"
    },
    {
      id: 2,
      src: "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      category: "living",
      title: "Living Room"
    },
    {
      id: 3,
      src: "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      category: "bedroom",
      title: "Master Bedroom"
    },
    {
      id: 4,
      src: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      category: "kitchen",
      title: "Modern Kitchen"
    },
    {
      id: 5,
      src: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      category: "bathroom",
      title: "Luxury Bathroom"
    },
    {
      id: 6,
      src: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      category: "view",
      title: "City Views"
    },
    {
      id: 7,
      src: "https://images.unsplash.com/photo-1571055107559-3e67626fa8be?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      category: "amenities",
      title: "Pool Area"
    },
    {
      id: 8,
      src: "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      category: "amenities",
      title: "Fitness Center"
    }
  ];

  // Floor Plans
  const floorPlans = [
    {
      id: 1,
      name: "Studio",
      size: "450 sq ft",
      bedrooms: 0,
      bathrooms: 1,
      price: "AED 650,000",
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      available: 12
    },
    {
      id: 2,
      name: "1 Bedroom",
      size: "850 sq ft",
      bedrooms: 1,
      bathrooms: 1,
      price: "AED 950,000",
      image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      available: 18
    },
    {
      id: 3,
      name: "2 Bedroom",
      size: "1,200 sq ft",
      bedrooms: 2,
      bathrooms: 2,
      price: "AED 1,450,000",
      image: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      available: 24
    },
    {
      id: 4,
      name: "3 Bedroom",
      size: "1,650 sq ft",
      bedrooms: 3,
      bathrooms: 3,
      price: "AED 2,100,000",
      image: "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      available: 8
    },
    {
      id: 5,
      name: "Penthouse",
      size: "2,500 sq ft",
      bedrooms: 4,
      bathrooms: 4,
      price: "AED 4,500,000",
      image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      available: 2
    }
  ];

  // Property Amenities
  const amenities = [
    { name: "Swimming Pool", icon: Waves, category: "Recreation" },
    { name: "Fitness Center", icon: Dumbbell, category: "Health" },
    { name: "24/7 Security", icon: Shield, category: "Safety" },
    { name: "Valet Parking", icon: Car, category: "Convenience" },
    { name: "High-Speed WiFi", icon: Wifi, category: "Technology" },
    { name: "Spa & Wellness", icon: Heart, category: "Wellness" },
    { name: "Kids Play Area", icon: Baby, category: "Family" },
    { name: "Rooftop Garden", icon: Trees, category: "Lifestyle" },
    { name: "Community Lounge", icon: Users, category: "Social" },
    { name: "Concierge Service", icon: User, category: "Service" },
    { name: "Business Center", icon: Building, category: "Work" },
    { name: "Private Beach", icon: Waves, category: "Premium" }
  ];

  // Nearby Landmarks
  const nearbyLandmarks = [
    {
      name: 'Dubai Mall',
      distance: '5 min drive',
      type: 'shopping',
      icon: ShoppingBag,
      description: 'World\'s largest shopping mall'
    },
    {
      name: 'Metro Station',
      distance: '3 min walk',
      type: 'transport',
      icon: Train,
      description: 'Business Bay Metro'
    },
    {
      name: 'Dubai Airport',
      distance: '20 min drive',
      type: 'transport',
      icon: Plane,
      description: 'International Airport'
    },
    {
      name: 'DIFC',
      distance: '8 min drive',
      type: 'business',
      icon: Building,
      description: 'Financial District'
    },
    {
      name: 'Beach Access',
      distance: '10 min drive',
      type: 'recreation',
      icon: Waves,
      description: 'Private beach'
    },
    {
      name: 'Schools',
      distance: '5 min drive',
      type: 'education',
      icon: GraduationCap,
      description: 'Top international schools'
    }
  ];

  // Payment Plans
  const paymentPlans = [
    {
      name: "Easy Plan",
      downPayment: "10%",
      duringConstruction: "60%",
      onCompletion: "30%",
      months: "24 months",
      popular: false
    },
    {
      name: "Flexible Plan",
      downPayment: "20%",
      duringConstruction: "50%",
      onCompletion: "30%",
      months: "36 months",
      popular: true
    },
    {
      name: "Cash Plan",
      downPayment: "100%",
      duringConstruction: "0%",
      onCompletion: "0%",
      months: "Immediate",
      popular: false,
      discount: "5% discount"
    }
  ];

  // Investment Calculations
  const calculateMortgagePayment = () => {
    const principal = investmentData.propertyPrice * (1 - investmentData.downPayment / 100);
    const monthlyRate = investmentData.interestRate / 100 / 12;
    const numPayments = investmentData.mortgageTerm * 12;
    
    if (monthlyRate === 0) return principal / numPayments;
    
    return principal * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
           (Math.pow(1 + monthlyRate, numPayments) - 1);
  };

  const calculateAnnualRentalIncome = () => {
    return investmentData.propertyPrice * (investmentData.expectedRentalYield / 100);
  };

  const calculateNetRentalIncome = () => {
    const grossRental = calculateAnnualRentalIncome();
    const maintenanceCosts = investmentData.propertyPrice * (investmentData.maintenanceCosts / 100);
    const managementFees = grossRental * (investmentData.managementFees / 100);
    return grossRental - maintenanceCosts - managementFees;
  };

  const calculateCashOnCash = () => {
    const downPaymentAmount = investmentData.propertyPrice * (investmentData.downPayment / 100);
    const netRentalIncome = calculateNetRentalIncome();
    const annualMortgagePayments = calculateMortgagePayment() * 12;
    const cashFlow = netRentalIncome - annualMortgagePayments;
    return (cashFlow / downPaymentAmount) * 100;
  };

  const calculateTotalROI = () => {
    const downPaymentAmount = investmentData.propertyPrice * (investmentData.downPayment / 100);
    const futureValue = investmentData.propertyPrice * Math.pow(1 + investmentData.capitalAppreciation / 100, investmentData.holdingPeriod);
    const totalCashFlow = calculateNetRentalIncome() * investmentData.holdingPeriod - (calculateMortgagePayment() * 12 * investmentData.holdingPeriod);
    const totalReturn = (futureValue - investmentData.propertyPrice) + totalCashFlow;
    return (totalReturn / downPaymentAmount) * 100;
  };

  // Utility Functions
  const generatePropertyMapUrl = () => {
    const [lat, lng] = propertyCoordinates;
    return `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3610.1234567890123!2d${lng}!3d${lat}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zM${lat.toFixed(6)}N%20${lng.toFixed(6)}E!5e0!3m2!1sen!2s!4v1703123456789!5m2!1sen!2s`;
  };

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const updateInvestmentData = (field: string, value: number | number[]) => {
    setInvestmentData(prev => ({
      ...prev,
      [field]: Array.isArray(value) ? value[0] : value
    }));
  };

  // Enhanced PDF generation
  const generatePropertyPDF = async () => {
    try {
      const { jsPDF } = await import('jspdf');
      
      const doc = new jsPDF();
      const pageWidth = doc.internal.pageSize.width;
      const pageHeight = doc.internal.pageSize.height;
      let currentY = 20;

      const goldColor = [212, 175, 55];
      const softBrownColor = [139, 115, 85];
      const warmGrayColor = [138, 121, 104];
      const beigeColor = [245, 241, 235];

      const safeText = (text: any): string => {
        if (text === null || text === undefined) return '';
        return String(text);
      };

      // Header
      doc.setFillColor(...softBrownColor);
      doc.rect(0, 0, pageWidth, 60, 'F');
      
      doc.setFillColor(...goldColor);
      doc.rect(20, 15, 30, 30, 'F');
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text('SMART', 35, 32, { align: 'center' });
      doc.text('OFF PLAN', 35, 40, { align: 'center' });
      
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(24);
      doc.setFont('helvetica', 'bold');
      doc.text('Smart Off Plan', 60, 28);
      
      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      doc.text('Premium Dubai Properties', 60, 38);

      currentY = 80;

      // Property Details
      doc.setFillColor(...beigeColor);
      doc.rect(20, currentY, pageWidth - 40, 100, 'F');
      
      doc.setTextColor(...softBrownColor);
      doc.setFontSize(20);
      doc.setFont('helvetica', 'bold');
      doc.text(safeText(project?.name), 30, currentY + 20);
      
      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      doc.text(`Location: ${safeText(project?.location)}`, 30, currentY + 35);
      doc.text(`Developer: ${safeText(project?.developer)}`, 30, currentY + 50);
      doc.text(`Status: ${safeText(project?.status)}`, 30, currentY + 65);
      doc.text(`Completion: ${safeText(project?.completion)}`, 30, currentY + 80);
      
      doc.setTextColor(...goldColor);
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text(`Starting from ${safeText(project?.price)}`, pageWidth - 30, currentY + 30, { align: 'right' });

      currentY += 120;

      // Floor Plans
      doc.setTextColor(...softBrownColor);
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('Available Floor Plans:', 30, currentY);
      currentY += 15;

      floorPlans.forEach((plan, index) => {
        if (currentY > pageHeight - 40) {
          doc.addPage();
          currentY = 30;
        }
        
        doc.setFontSize(12);
        doc.setFont('helvetica', 'normal');
        doc.text(`${plan.name} - ${plan.size} - ${plan.price}`, 30, currentY);
        doc.text(`${plan.bedrooms} Bed | ${plan.bathrooms} Bath | ${plan.available} Available`, 30, currentY + 12);
        currentY += 25;
      });

      // Contact Information
      currentY += 20;
      doc.setFillColor(...goldColor);
      doc.rect(20, currentY, pageWidth - 40, 50, 'F');
      
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text('Contact Information', 30, currentY + 20);
      
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text('Phone: +971 4 123 4567', 30, currentY + 35);
      doc.text('Email: <EMAIL>', 120, currentY + 35);
      doc.text(`Generated: ${new Date().toLocaleDateString()}`, pageWidth - 30, currentY + 35, { align: 'right' });

      const fileName = `${safeText(project?.name || 'Property').replace(/[^a-z0-9]/gi, '_')}_Brochure.pdf`;
      doc.save(fileName);

    } catch (error) {
      console.error('Error generating PDF:', error);
      
      const fallbackContent = `
${project?.name || 'Property'} - Property Brochure
=================================

Location: ${project?.location || 'Dubai'}
Price: ${project?.price || 'Contact for pricing'}
Developer: ${project?.developer || 'Premium Developer'}
Status: ${project?.status || 'Available'}
Completion: ${project?.completion || 'To be announced'}

Available Floor Plans:
${floorPlans.map(plan => `- ${plan.name}: ${plan.size}, ${plan.price}`).join('\n')}

Contact Smart Off Plan:
Phone: +971 4 123 4567
Email: <EMAIL>
Generated on: ${new Date().toLocaleDateString()}
      `.trim();

      const blob = new Blob([fallbackContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${(project?.name || 'Property').replace(/[^a-z0-9]/gi, '_')}_Brochure.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  // Form Handlers
  const handleDownloadSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (downloadFormData.name && downloadFormData.email) {
      setIsDownloadDialogOpen(false);
      generatePropertyPDF();
      setDownloadFormData({ name: '', email: '', phone: '' });
    }
  };

  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Contact form submitted:', contactFormData);
    setContactFormData({
      fullName: '',
      email: '',
      phone: '',
      message: '',
      interest: 'buying'
    });
  };

  const handleCallbackSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Callback requested:', callbackFormData);
    setIsCallbackDialogOpen(false);
    setCallbackFormData({ name: '', phone: '', preferredTime: '' });
  };

  const handleViewingSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Viewing scheduled:', viewingFormData);
    setIsViewingDialogOpen(false);
    setViewingFormData({ name: '', email: '', phone: '', date: '', time: '' });
  };

  const handleShare = (platform: string) => {
    const url = window.location.href;
    const text = `Check out this amazing property: ${project.name} in ${project.location}`;
    
    switch (platform) {
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'linkedin':
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'whatsapp':
        window.open(`https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`, '_blank');
        break;
      case 'copy':
        navigator.clipboard.writeText(url);
        break;
    }
  };

  if (!project) {
    return (
      <div className="min-h-screen bg-ivory flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl text-soft-brown mb-4">Property not found</h2>
          <Button onClick={onBack} variant="outline">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Properties
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-ivory">
      {/* Header */}
      <header className="bg-white border-b border-beige shadow-sm">
        <div className="container py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gold rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold">S</span>
                </div>
                <div>
                  <h1 className="text-xl text-soft-brown font-bold">SMART OFF PLAN</h1>
                  <p className="text-xs text-warm-gray">Premium Dubai Properties</p>
                </div>
              </div>
              
              <Button 
                onClick={onBack}
                variant="ghost" 
                size="sm"
                className="text-warm-gray hover:text-gold"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Properties
              </Button>
            </div>
            
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsShareDialogOpen(true)}
                className="border-gold/30 text-gold hover:bg-gold/10"
              >
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                className="border-gold/30 text-gold hover:bg-gold/10"
              >
                <Bookmark className="w-4 h-4 mr-2" />
                Save
              </Button>

              <Dialog open={isDownloadDialogOpen} onOpenChange={setIsDownloadDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-soft-brown hover:bg-soft-brown/90 text-white">
                    <Download className="w-4 h-4 mr-2" />
                    Download Brochure
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md bg-white">
                  <DialogHeader>
                    <DialogTitle className="text-soft-brown">Download Property Brochure</DialogTitle>
                    <DialogDescription className="text-warm-gray">
                      Get detailed information about {project.name}
                    </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleDownloadSubmit} className="space-y-4">
                    <div>
                      <Label htmlFor="name">Full Name *</Label>
                      <Input
                        id="name"
                        value={downloadFormData.name}
                        onChange={(e) => setDownloadFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter your full name"
                        required
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">Email Address *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={downloadFormData.email}
                        onChange={(e) => setDownloadFormData(prev => ({ ...prev, email: e.target.value }))}
                        placeholder="Enter your email"
                        required
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={downloadFormData.phone}
                        onChange={(e) => setDownloadFormData(prev => ({ ...prev, phone: e.target.value }))}
                        placeholder="Enter your phone number"
                        className="mt-1"
                      />
                    </div>
                    <div className="flex justify-end space-x-3 pt-4">
                      <Button 
                        type="button" 
                        variant="outline" 
                        onClick={() => setIsDownloadDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button type="submit" className="bg-gold hover:bg-gold/90 text-charcoal">
                        <Download className="w-4 h-4 mr-2" />
                        Download PDF
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container py-8">
        
        {/* Property Header */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div>
              <div className="flex items-center gap-3 mb-3">
                <Badge className="bg-gold text-charcoal">
                  {project.status}
                </Badge>
                <Badge variant="outline" className="border-gold/30 text-gold">
                  Premium
                </Badge>
                <Badge variant="outline" className="border-green-500/30 text-green-600">
                  Golden Visa Eligible
                </Badge>
              </div>
              
              <h1 className="text-4xl lg:text-5xl text-soft-brown mb-4">{project.name}</h1>
              
              <div className="flex items-center text-warm-gray text-lg mb-4">
                <MapPin className="w-5 h-5 mr-2 text-gold" />
                <span>{project.location}, Dubai, UAE</span>
              </div>
              
              <div className="text-3xl lg:text-4xl text-gold mb-4">
                Starting from {project.price}
              </div>
              
              <p className="text-warm-gray text-lg leading-relaxed max-w-3xl text-[15px] text-[16px]">
                {project.description || 
                  "Experience luxury living at its finest with this exceptional property offering. Located in one of Dubai's most prestigious neighborhoods, this development combines modern architecture with world-class amenities."
                }
              </p>
            </div>
            
            <div className="flex flex-col lg:flex-row gap-3">
              <Dialog open={isDownloadDialogOpen} onOpenChange={setIsDownloadDialogOpen}>
                <DialogTrigger asChild>
                  <Button size="lg" className="bg-gold hover:bg-gold/90 text-charcoal">
                    <Download className="w-5 h-5 mr-2" />
                    Download Brochure
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md bg-white">
                  <DialogHeader>
                    <DialogTitle className="text-soft-brown">Download Property Brochure</DialogTitle>
                    <DialogDescription className="text-warm-gray">
                      Get detailed information about {project.name}
                    </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleDownloadSubmit} className="space-y-4">
                    <div>
                      <Label htmlFor="name">Full Name *</Label>
                      <Input
                        id="name"
                        value={downloadFormData.name}
                        onChange={(e) => setDownloadFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter your full name"
                        required
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">Email Address *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={downloadFormData.email}
                        onChange={(e) => setDownloadFormData(prev => ({ ...prev, email: e.target.value }))}
                        placeholder="Enter your email"
                        required
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={downloadFormData.phone}
                        onChange={(e) => setDownloadFormData(prev => ({ ...prev, phone: e.target.value }))}
                        placeholder="Enter your phone number"
                        className="mt-1"
                      />
                    </div>
                    <div className="flex justify-end space-x-3 pt-4">
                      <Button 
                        type="button" 
                        variant="outline" 
                        onClick={() => setIsDownloadDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button type="submit" className="bg-gold hover:bg-gold/90 text-charcoal">
                        <Download className="w-4 h-4 mr-2" />
                        Download PDF
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>

              <Dialog open={isInvestmentCalculatorOpen} onOpenChange={setIsInvestmentCalculatorOpen}>
                <DialogTrigger asChild>
                  <Button 
                    size="lg"
                    variant="outline"
                    className="border-soft-brown text-soft-brown hover:bg-soft-brown/10"
                  >
                    <TrendingUp className="w-5 h-5 mr-2" />
                    Smart Investment
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-6xl bg-white max-h-[90vh] overflow-y-auto">
                  <DialogHeader className="pb-6 border-b border-beige">
                    <DialogTitle className="text-3xl text-soft-brown flex items-center">
                      <TrendingUp className="w-8 h-8 mr-3 text-gold" />
                      Investment Calculator
                    </DialogTitle>
                    <DialogDescription className="text-warm-gray mt-2 text-lg">
                      Comprehensive ROI analysis for {project.name}
                    </DialogDescription>
                  </DialogHeader>

                  <div className="py-8">
                    <Tabs value={calculatorTab} onValueChange={setCalculatorTab} className="w-full">
                      <div className="relative w-full">
                        <TabsList className="w-full h-auto flex flex-nowrap md:grid md:grid-cols-3 bg-beige/50 rounded-xl p-2 overflow-x-auto gap-2 md:gap-0 mb-8">
                          <TabsTrigger 
                            value="roi" 
                            className="flex items-center rounded-lg flex-shrink-0 min-w-[120px] text-sm px-3 py-2"
                          >
                            <PieChart className="w-4 h-4 mr-2" />
                            <span className="hidden sm:inline">ROI Analysis</span>
                            <span className="sm:hidden">ROI</span>
                          </TabsTrigger>
                          <TabsTrigger 
                            value="mortgage" 
                            className="flex items-center rounded-lg flex-shrink-0 min-w-[120px] text-sm px-3 py-2"
                          >
                            <CreditCard className="w-4 h-4 mr-2" />
                            Mortgage
                          </TabsTrigger>
                          <TabsTrigger 
                            value="rental" 
                            className="flex items-center rounded-lg flex-shrink-0 min-w-[120px] text-sm px-3 py-2"
                          >
                            <Banknote className="w-4 h-4 mr-2" />
                            <span className="hidden sm:inline">Rental Yield</span>
                            <span className="sm:hidden">Rental</span>
                          </TabsTrigger>
                        </TabsList>
                      </div>

                      {/* ROI Analysis Tab */}
                      <TabsContent value="roi" className="space-y-8">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                          <Card className="rounded-2xl border-0 shadow-xl">
                            <CardHeader className="p-6">
                              <CardTitle className="text-soft-brown text-xl">Investment Parameters</CardTitle>
                            </CardHeader>
                            <CardContent className="p-6 pt-0 space-y-8">
                              <div>
                                <Label className="flex items-center justify-between mb-3">
                                  <span>Property Price</span>
                                  <span className="text-gold font-medium">AED {investmentData.propertyPrice.toLocaleString()}</span>
                                </Label>
                                <Slider
                                  value={[investmentData.propertyPrice]}
                                  onValueChange={(value) => updateInvestmentData('propertyPrice', value)}
                                  max={10000000}
                                  min={500000}
                                  step={50000}
                                  className="w-full"
                                />
                              </div>

                              <div>
                                <Label className="flex items-center justify-between mb-3">
                                  <span>Down Payment</span>
                                  <span className="text-gold font-medium">{investmentData.downPayment}%</span>
                                </Label>
                                <Slider
                                  value={[investmentData.downPayment]}
                                  onValueChange={(value) => updateInvestmentData('downPayment', value)}
                                  max={50}
                                  min={10}
                                  step={5}
                                  className="w-full"
                                />
                              </div>

                              <div>
                                <Label className="flex items-center justify-between mb-3">
                                  <span>Expected Rental Yield</span>
                                  <span className="text-gold font-medium">{investmentData.expectedRentalYield}%</span>
                                </Label>
                                <Slider
                                  value={[investmentData.expectedRentalYield]}
                                  onValueChange={(value) => updateInvestmentData('expectedRentalYield', value)}
                                  max={15}
                                  min={4}
                                  step={0.5}
                                  className="w-full"
                                />
                              </div>

                              <div>
                                <Label className="flex items-center justify-between mb-3">
                                  <span>Capital Appreciation</span>
                                  <span className="text-gold font-medium">{investmentData.capitalAppreciation}%</span>
                                </Label>
                                <Slider
                                  value={[investmentData.capitalAppreciation]}
                                  onValueChange={(value) => updateInvestmentData('capitalAppreciation', value)}
                                  max={12}
                                  min={2}
                                  step={0.5}
                                  className="w-full"
                                />
                              </div>

                              <div>
                                <Label className="flex items-center justify-between mb-3">
                                  <span>Holding Period</span>
                                  <span className="text-gold font-medium">{investmentData.holdingPeriod} years</span>
                                </Label>
                                <Slider
                                  value={[investmentData.holdingPeriod]}
                                  onValueChange={(value) => updateInvestmentData('holdingPeriod', value)}
                                  max={20}
                                  min={3}
                                  step={1}
                                  className="w-full"
                                />
                              </div>
                            </CardContent>
                          </Card>

                          <Card className="rounded-2xl border-0 shadow-xl">
                            <CardHeader className="p-6">
                              <CardTitle className="text-soft-brown text-xl">Investment Returns</CardTitle>
                            </CardHeader>
                            <CardContent className="p-6 pt-0 space-y-6">
                              <div className="grid grid-cols-2 gap-4">
                                <div className="bg-gold/10 p-6 rounded-2xl">
                                  <div className="flex items-center justify-between mb-3">
                                    <span className="text-sm text-warm-gray">Cash on Cash Return</span>
                                    <TrendingUp className="w-5 h-5 text-gold" />
                                  </div>
                                  <div className="text-3xl text-soft-brown font-bold">
                                    {calculateCashOnCash().toFixed(1)}%
                                  </div>
                                  <div className="text-xs text-warm-gray mt-2">Annual cash flow return</div>
                                </div>

                                <div className="bg-gold/10 p-6 rounded-2xl">
                                  <div className="flex items-center justify-between mb-3">
                                    <span className="text-sm text-warm-gray">Total ROI</span>
                                    <Target className="w-5 h-5 text-gold" />
                                  </div>
                                  <div className="text-3xl text-soft-brown font-bold">
                                    {calculateTotalROI().toFixed(1)}%
                                  </div>
                                  <div className="text-xs text-warm-gray mt-2">Over {investmentData.holdingPeriod} years</div>
                                </div>

                                <div className="bg-gold/10 p-6 rounded-2xl">
                                  <div className="flex items-center justify-between mb-3">
                                    <span className="text-sm text-warm-gray">Monthly Cash Flow</span>
                                    <DollarSign className="w-5 h-5 text-gold" />
                                  </div>
                                  <div className="text-2xl text-soft-brown font-bold">
                                    AED {(calculateNetRentalIncome() / 12 - calculateMortgagePayment()).toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                  </div>
                                  <div className="text-xs text-warm-gray mt-2">Net monthly income</div>
                                </div>

                                <div className="bg-gold/10 p-6 rounded-2xl">
                                  <div className="flex items-center justify-between mb-3">
                                    <span className="text-sm text-warm-gray">Initial Investment</span>
                                    <Wallet className="w-5 h-5 text-gold" />
                                  </div>
                                  <div className="text-xl text-soft-brown font-bold">
                                    AED {(investmentData.propertyPrice * investmentData.downPayment / 100).toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                  </div>
                                  <div className="text-xs text-warm-gray mt-2">Down payment required</div>
                                </div>
                              </div>

                              <div className="pt-6 border-t border-beige">
                                <h4 className="text-soft-brown mb-4 font-medium">Investment Summary</h4>
                                <div className="space-y-3">
                                  <div className="flex justify-between">
                                    <span className="text-warm-gray">Property Value:</span>
                                    <span className="text-soft-brown font-medium">AED {investmentData.propertyPrice.toLocaleString()}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-warm-gray">Down Payment:</span>
                                    <span className="text-soft-brown font-medium">AED {(investmentData.propertyPrice * investmentData.downPayment / 100).toLocaleString()}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-warm-gray">Loan Amount:</span>
                                    <span className="text-soft-brown font-medium">AED {(investmentData.propertyPrice * (100 - investmentData.downPayment) / 100).toLocaleString()}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-warm-gray">Annual Rental Income:</span>
                                    <span className="text-soft-brown font-medium">AED {calculateAnnualRentalIncome().toLocaleString()}</span>
                                  </div>
                                  <div className="flex justify-between border-t border-beige pt-3">
                                    <span className="text-warm-gray">Future Property Value:</span>
                                    <span className="text-gold font-medium">AED {(investmentData.propertyPrice * Math.pow(1 + investmentData.capitalAppreciation / 100, investmentData.holdingPeriod)).toLocaleString(undefined, { maximumFractionDigits: 0 })}</span>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                      </TabsContent>

                      {/* Mortgage Tab */}
                      <TabsContent value="mortgage" className="space-y-8">
                        <div className="text-center py-12">
                          <Calculator className="w-16 h-16 text-gold mx-auto mb-4" />
                          <h3 className="text-xl text-soft-brown mb-4">Mortgage Calculator</h3>
                          <p className="text-warm-gray">Detailed mortgage calculations and payment breakdown.</p>
                          <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
                            <div className="bg-beige/30 p-6 rounded-xl">
                              <div className="text-2xl text-soft-brown font-bold">AED {calculateMortgagePayment().toLocaleString(undefined, { maximumFractionDigits: 0 })}</div>
                              <div className="text-sm text-warm-gray">Monthly Payment</div>
                            </div>
                            <div className="bg-beige/30 p-6 rounded-xl">
                              <div className="text-2xl text-soft-brown font-bold">{investmentData.interestRate}%</div>
                              <div className="text-sm text-warm-gray">Interest Rate</div>
                            </div>
                            <div className="bg-beige/30 p-6 rounded-xl">
                              <div className="text-2xl text-soft-brown font-bold">{investmentData.mortgageTerm}</div>
                              <div className="text-sm text-warm-gray">Years</div>
                            </div>
                          </div>
                        </div>
                      </TabsContent>

                      {/* Rental Yield Tab */}
                      <TabsContent value="rental" className="space-y-8">
                        <div className="text-center py-12">
                          <Banknote className="w-16 h-16 text-gold mx-auto mb-4" />
                          <h3 className="text-xl text-soft-brown mb-4">Rental Yield Analysis</h3>
                          <p className="text-warm-gray">Comprehensive rental income and yield analysis.</p>
                          <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
                            <div className="bg-beige/30 p-6 rounded-xl">
                              <div className="text-2xl text-soft-brown font-bold">{investmentData.expectedRentalYield}%</div>
                              <div className="text-sm text-warm-gray">Gross Yield</div>
                            </div>
                            <div className="bg-beige/30 p-6 rounded-xl">
                              <div className="text-2xl text-soft-brown font-bold">AED {calculateNetRentalIncome().toLocaleString(undefined, { maximumFractionDigits: 0 })}</div>
                              <div className="text-sm text-warm-gray">Net Annual Income</div>
                            </div>
                            <div className="bg-beige/30 p-6 rounded-xl">
                              <div className="text-2xl text-soft-brown font-bold">AED {(calculateNetRentalIncome() / 12).toLocaleString(undefined, { maximumFractionDigits: 0 })}</div>
                              <div className="text-sm text-warm-gray">Monthly Income</div>
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Left Column - Images and Gallery */}
          <div className="lg:col-span-2 space-y-8">
            
            {/* Main Image */}
            <div className="relative">
              <div className="aspect-[4/3] rounded-xl overflow-hidden shadow-xl">
                <ImageWithFallback
                  src={propertyImages[selectedImageIndex].src}
                  alt={`${project.name} - ${propertyImages[selectedImageIndex].title}`}
                  className="w-full h-full object-cover"
                />
                
                {/* Image Overlay Controls */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10">
                  <div className="absolute top-4 left-4 flex space-x-2">
                    <Badge className="bg-black/50 text-white border-0">
                      {selectedImageIndex + 1} / {propertyImages.length}
                    </Badge>
                    <Badge className="bg-black/50 text-white border-0 capitalize">
                      {propertyImages[selectedImageIndex].category}
                    </Badge>
                  </div>
                  
                  <div className="absolute top-4 right-4 flex space-x-2">
                    <Button
                      size="sm"
                      variant="secondary"
                      className="bg-black/50 hover:bg-black/70 text-white border-0"
                      onClick={() => setIsImageGalleryOpen(true)}
                    >
                      <Expand className="w-4 h-4 mr-1" />
                      Expand
                    </Button>
                  </div>
                  
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-3">
                    <Button
                      size="sm"
                      variant="secondary"
                      className="bg-black/50 hover:bg-black/70 text-white border-0"
                      onClick={() => setSelectedImageIndex(prev => prev > 0 ? prev - 1 : propertyImages.length - 1)}
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="secondary"
                      className="bg-black/50 hover:bg-black/70 text-white border-0"
                      onClick={() => setSelectedImageIndex(prev => prev < propertyImages.length - 1 ? prev + 1 : 0)}
                    >
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Thumbnail Gallery */}
            <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-3">
              {propertyImages.map((image, index) => (
                <button
                  key={image.id}
                  onClick={() => setSelectedImageIndex(index)}
                  className={`aspect-square rounded-lg overflow-hidden border-2 transition-all ${
                    selectedImageIndex === index 
                      ? 'border-gold shadow-lg scale-105' 
                      : 'border-beige hover:border-gold/50 hover:scale-102'
                  }`}
                >
                  <ImageWithFallback
                    src={image.src}
                    alt={image.title}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>

            {/* Content Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <div className="relative w-full">
                <TabsList className="w-full h-auto flex flex-nowrap md:grid md:grid-cols-5 bg-beige/50 rounded-xl p-2 overflow-x-auto gap-2 md:gap-0">
                  <TabsTrigger 
                    value="overview" 
                    className="rounded-lg flex-shrink-0 min-w-[100px] text-sm px-3 py-2"
                  >
                    Overview
                  </TabsTrigger>
                  <TabsTrigger 
                    value="floor-plans" 
                    className="rounded-lg flex-shrink-0 min-w-[100px] text-sm px-3 py-2"
                  >
                    <span className="hidden sm:inline">Floor Plans</span>
                    <span className="sm:hidden">Plans</span>
                  </TabsTrigger>
                  <TabsTrigger 
                    value="amenities" 
                    className="rounded-lg flex-shrink-0 min-w-[100px] text-sm px-3 py-2"
                  >
                    Amenities
                  </TabsTrigger>
                  <TabsTrigger 
                    value="location" 
                    className="rounded-lg flex-shrink-0 min-w-[100px] text-sm px-3 py-2"
                  >
                    Location
                  </TabsTrigger>
                  <TabsTrigger 
                    value="investment" 
                    className="rounded-lg flex-shrink-0 min-w-[100px] text-sm px-3 py-2"
                  >
                    <span className="hidden sm:inline">Investment</span>
                    <span className="sm:hidden">ROI</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              {/* Overview Tab */}
              <TabsContent value="overview" className="space-y-8 mt-8">
                
                {/* Property Description */}
                <Card className="border border-beige shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-soft-brown">About This Property</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <p className="text-warm-gray leading-relaxed">
                      {project.description || 
                        "Experience luxury living at its finest with this exceptional property offering. Located in one of Dubai's most prestigious neighborhoods, this development combines modern architecture with world-class amenities. Each residence is thoughtfully designed with premium finishes, spacious layouts, and stunning city skyline views."
                      }
                    </p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div>
                        <h4 className="text-soft-brown mb-4">Key Features</h4>
                        <ul className="space-y-3">
                          {[
                            'Premium finishes throughout',
                            'Floor-to-ceiling windows',
                            'Modern kitchen appliances',
                            'Smart home technology',
                            'Private balcony/terrace',
                            'Premium bathroom features'
                          ].map((feature) => (
                            <li key={feature} className="flex items-center text-warm-gray">
                              <CheckCircle2 className="w-5 h-5 mr-3 text-gold flex-shrink-0" />
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="text-soft-brown mb-4">Investment Benefits</h4>
                        <ul className="space-y-3">
                          {[
                            '0% Property Tax',
                            '100% Foreign Ownership',
                            '8-12% Rental Yields',
                            '15% Capital Growth',
                            'Golden Visa Eligibility',
                            'Prime Location'
                          ].map((benefit) => (
                            <li key={benefit} className="flex items-center text-warm-gray">
                              <Star className="w-5 h-5 mr-3 text-gold flex-shrink-0" />
                              <span>{benefit}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Payment Plans Section */}
                <Card className="border border-beige shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-soft-brown flex items-center">
                      <CreditCard className="w-5 h-5 mr-3 text-gold" />
                      Payment Plans
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      
                      {/* 60/40 Plan */}
                      <div className="relative p-4 border border-beige rounded-xl hover:border-gold/50 transition-colors">
                        <Badge className="absolute -top-2 left-4 bg-gold text-charcoal text-xs">
                          Popular
                        </Badge>
                        <div className="space-y-3">
                          <h4 className="text-soft-brown font-medium">60/40 Plan</h4>
                          <div className="grid grid-cols-2 gap-2 text-center">
                            <div className="p-2 bg-gold/10 rounded-lg">
                              <div className="text-lg text-gold font-medium">60%</div>
                              <div className="text-xs text-warm-gray">Construction</div>
                            </div>
                            <div className="p-2 bg-beige/50 rounded-lg">
                              <div className="text-lg text-soft-brown font-medium">40%</div>
                              <div className="text-xs text-warm-gray">Handover</div>
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-xs text-warm-gray">Booking Fee</div>
                            <div className="text-sm text-soft-brown font-medium">5%</div>
                          </div>
                        </div>
                      </div>

                      {/* 70/30 Plan */}
                      <div className="p-4 border border-beige rounded-xl hover:border-gold/50 transition-colors">
                        <div className="space-y-3">
                          <h4 className="text-soft-brown font-medium">70/30 Plan</h4>
                          <div className="grid grid-cols-2 gap-2 text-center">
                            <div className="p-2 bg-gold/10 rounded-lg">
                              <div className="text-lg text-gold font-medium">70%</div>
                              <div className="text-xs text-warm-gray">Construction</div>
                            </div>
                            <div className="p-2 bg-beige/50 rounded-lg">
                              <div className="text-lg text-soft-brown font-medium">30%</div>
                              <div className="text-xs text-warm-gray">Handover</div>
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-xs text-warm-gray">Booking Fee</div>
                            <div className="text-sm text-soft-brown font-medium">10%</div>
                          </div>
                        </div>
                      </div>

                      {/* 80/20 Plan */}
                      <div className="p-4 border border-beige rounded-xl hover:border-gold/50 transition-colors">
                        <div className="space-y-3">
                          <h4 className="text-soft-brown font-medium">80/20 Plan</h4>
                          <div className="grid grid-cols-2 gap-2 text-center">
                            <div className="p-2 bg-gold/10 rounded-lg">
                              <div className="text-lg text-gold font-medium">80%</div>
                              <div className="text-xs text-warm-gray">Construction</div>
                            </div>
                            <div className="p-2 bg-beige/50 rounded-lg">
                              <div className="text-lg text-soft-brown font-medium">20%</div>
                              <div className="text-xs text-warm-gray">Handover</div>
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-xs text-warm-gray">Booking Fee</div>
                            <div className="text-sm text-soft-brown font-medium">10%</div>
                          </div>
                        </div>
                      </div>

                    </div>
                    
                    {/* Payment Plan Info */}
                    <div className="mt-6 p-4 bg-beige/30 rounded-xl">
                      <div className="flex items-center justify-between">
                        <div>
                          <h5 className="text-soft-brown font-medium mb-1">Flexible Payment Options</h5>
                          <p className="text-sm text-warm-gray">Choose a payment structure that fits your investment strategy</p>
                        </div>
                        <Button 
                          variant="outline" 
                          size="sm"
                          className="border-gold/30 text-gold hover:bg-gold/10 rounded-xl"
                        >
                          <Calculator className="w-4 h-4 mr-2" />
                          Calculate
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

              </TabsContent>

              {/* Floor Plans Tab */}
              <TabsContent value="floor-plans" className="space-y-8 mt-8">
                <Card className="border border-beige shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-soft-brown">Available Floor Plans</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-8">
                    
                    {/* Floor Plan Selection */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
                      {floorPlans.map((plan, index) => (
                        <button
                          key={plan.id}
                          onClick={() => setSelectedFloorPlan(index)}
                          className={`p-4 rounded-xl border-2 text-left transition-all ${
                            selectedFloorPlan === index 
                              ? 'border-gold bg-gold/10 shadow-lg' 
                              : 'border-beige hover:border-gold/50'
                          }`}
                        >
                          <div className="space-y-3">
                            <h4 className="text-soft-brown font-medium">{plan.name}</h4>
                            <div className="space-y-2 text-sm text-warm-gray">
                              <div className="flex items-center">
                                <Bed className="w-4 h-4 mr-2 text-gold" />
                                <span>{plan.bedrooms} Bed</span>
                              </div>
                              <div className="flex items-center">
                                <Bath className="w-4 h-4 mr-2 text-gold" />
                                <span>{plan.bathrooms} Bath</span>
                              </div>
                              <div className="flex items-center">
                                <Square className="w-4 h-4 mr-2 text-gold" />
                                <span>{plan.size}</span>
                              </div>
                            </div>
                            <div className="text-gold font-medium">{plan.price}</div>
                            <Badge variant="outline" className="text-xs">
                              {plan.available} Available
                            </Badge>
                          </div>
                        </button>
                      ))}
                    </div>
                    
                    {/* Selected Floor Plan */}
                    <div className="border-2 border-gold/20 rounded-xl overflow-hidden">
                      <div className="bg-gold/10 p-6 border-b border-gold/20">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="text-soft-brown text-xl">{floorPlans[selectedFloorPlan].name}</h3>
                            <p className="text-warm-gray">{floorPlans[selectedFloorPlan].size} • {floorPlans[selectedFloorPlan].price}</p>
                          </div>
                          <Button 
                            variant="outline"
                            size="sm"
                            className="border-gold/30 text-gold hover:bg-gold/10"
                          >
                            <Download className="w-4 h-4 mr-2" />
                            Download
                          </Button>
                        </div>
                      </div>
                      <div className="aspect-[4/3] bg-gray-100">
                        <ImageWithFallback
                          src={floorPlans[selectedFloorPlan].image}
                          alt={`${floorPlans[selectedFloorPlan].name} floor plan`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Amenities Tab */}
              <TabsContent value="amenities" className="space-y-8 mt-8">
                <Card className="border border-beige shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-soft-brown">World-Class Amenities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                      {amenities.map((amenity, index) => {
                        const IconComponent = amenity.icon;
                        return (
                          <div key={index} className="flex items-center space-x-4 p-4 rounded-xl bg-beige/30 hover:bg-beige/50 transition-colors">
                            <div className="w-12 h-12 bg-gold/20 rounded-xl flex items-center justify-center flex-shrink-0">
                              <IconComponent className="w-6 h-6 text-gold" />
                            </div>
                            <div>
                              <h4 className="text-soft-brown font-medium">{amenity.name}</h4>
                              <p className="text-xs text-warm-gray">{amenity.category}</p>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Location Tab */}
              <TabsContent value="location" className="space-y-8 mt-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  
                  {/* Map */}
                  <Card className="border border-beige shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-soft-brown flex items-center justify-between">
                        <span className="flex items-center">
                          <MapPin className="w-5 h-5 mr-2 text-gold" />
                          Property Location
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(`https://maps.google.com/?q=${propertyCoordinates[0]},${propertyCoordinates[1]}`, '_blank')}
                          className="border-gold/30 text-gold hover:bg-gold/10"
                        >
                          <ExternalLink className="w-4 h-4 mr-2" />
                          Open in Maps
                        </Button>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                      <div className="aspect-[4/3] rounded-lg overflow-hidden">
                        <iframe
                          src={generatePropertyMapUrl()}
                          width="100%"
                          height="100%"
                          style={{ border: 0 }}
                          allowFullScreen
                          loading="lazy"
                          referrerPolicy="no-referrer-when-downgrade"
                          className="w-full h-full"
                        />
                      </div>
                    </CardContent>
                  </Card>
                  
                  {/* Nearby Landmarks */}
                  <Card className="border border-beige shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-soft-brown">Nearby Landmarks</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {nearbyLandmarks.map((landmark, index) => {
                        const IconComponent = landmark.icon;
                        return (
                          <div key={index} className="flex items-center justify-between p-4 bg-beige/30 rounded-xl hover:bg-beige/50 transition-colors">
                            <div className="flex items-center space-x-4">
                              <div className="w-10 h-10 bg-gold/20 rounded-lg flex items-center justify-center">
                                <IconComponent className="w-5 h-5 text-gold" />
                              </div>
                              <div>
                                <div className="text-soft-brown font-medium">{landmark.name}</div>
                                <div className="text-xs text-warm-gray">{landmark.description}</div>
                              </div>
                            </div>
                            <Badge className="bg-gold text-charcoal">
                              {landmark.distance}
                            </Badge>
                          </div>
                        );
                      })}
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Investment Tab */}
              <TabsContent value="investment" className="space-y-8 mt-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  
                  {/* Investment Calculator CTA */}
                  <Card className="border border-gold/20 shadow-lg bg-gradient-to-br from-gold/5 to-gold/10">
                    <CardContent className="p-8 text-center">
                      <div className="w-20 h-20 bg-gold/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <Calculator className="w-10 h-10 text-gold" />
                      </div>
                      <h3 className="text-soft-brown text-xl mb-4">Investment Calculator</h3>
                      <p className="text-warm-gray mb-8 leading-relaxed">
                        Calculate your potential returns, mortgage options, and see detailed market analysis for this property.
                      </p>
                      <Button 
                        onClick={() => setIsInvestmentCalculatorOpen(true)}
                        className="w-full bg-gold hover:bg-gold/90 text-charcoal font-medium py-4 rounded-xl mb-6"
                      >
                        <TrendingUp className="w-5 h-5 mr-2" />
                        Open Calculator
                      </Button>
                      
                      <div className="grid grid-cols-2 gap-4 text-center">
                        <div className="bg-white/70 rounded-xl p-4">
                          <div className="text-2xl text-soft-brown">{calculateCashOnCash().toFixed(1)}%</div>
                          <div className="text-xs text-warm-gray">Cash Return</div>
                        </div>
                        <div className="bg-white/70 rounded-xl p-4">
                          <div className="text-2xl text-soft-brown">{calculateTotalROI().toFixed(0)}%</div>
                          <div className="text-xs text-warm-gray">Total ROI</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Market Insights */}
                  <Card className="border border-beige shadow-sm">
                    <CardHeader>
                      <CardTitle className="text-soft-brown">Market Insights</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-4 bg-beige/30 rounded-xl">
                          <div className="text-2xl text-soft-brown">8.5%</div>
                          <div className="text-xs text-warm-gray">Avg. Rental Yield</div>
                        </div>
                        <div className="text-center p-4 bg-beige/30 rounded-xl">
                          <div className="text-2xl text-soft-brown">15%</div>
                          <div className="text-xs text-warm-gray">Price Appreciation</div>
                        </div>
                        <div className="text-center p-4 bg-beige/30 rounded-xl">
                          <div className="text-2xl text-soft-brown">High</div>
                          <div className="text-xs text-warm-gray">Demand Level</div>
                        </div>
                        <div className="text-center p-4 bg-beige/30 rounded-xl">
                          <div className="text-2xl text-soft-brown">A+</div>
                          <div className="text-xs text-warm-gray">Location Grade</div>
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        <h4 className="text-soft-brown">Investment Advantages</h4>
                        <ul className="space-y-3">
                          {[
                            'Prime location with high demand',
                            'Strong rental market',
                            'Excellent capital appreciation',
                            'Tax-free investment environment',
                            'Golden Visa eligibility'
                          ].map((advantage) => (
                            <li key={advantage} className="flex items-center text-warm-gray">
                              <ThumbsUp className="w-4 h-4 mr-3 text-gold flex-shrink-0" />
                              <span className="text-sm">{advantage}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Right Column - Property Info and Contact */}
          <div className="space-y-6">
            
            {/* Property Location Map */}
            <Card className="border border-beige shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-soft-brown text-lg mb-2">
                  {project.name}
                </CardTitle>
                <div className="flex items-center text-warm-gray text-sm">
                  <MapPin className="w-4 h-4 mr-2 text-gold" />
                  {project.location}
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="aspect-[16/9] relative overflow-hidden rounded-lg">
                  <iframe
                    src={generatePropertyMapUrl()}
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    className="w-full h-full"
                  />
                  
                  {/* Location Pin Overlay */}
                  <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                    <div className="w-6 h-6 bg-gold rounded-full border-2 border-white shadow-lg pulse-animation"></div>
                  </div>
                  
                  {/* Map Action Button */}
                  <div className="absolute bottom-3 right-3">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => window.open(`https://maps.google.com/?q=${propertyCoordinates[0]},${propertyCoordinates[1]}`, '_blank')}
                      className="bg-white/90 hover:bg-white text-soft-brown border border-beige shadow-sm"
                    >
                      <ExternalLink className="w-3 h-3 mr-1" />
                      View
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Property Quick Info */}
            <Card className="border border-beige shadow-sm">
              <CardHeader>
                <CardTitle className="text-soft-brown">Property Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                
                {/* Key Details */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-warm-gray text-sm">Developer</Label>
                    <div className="text-soft-brown font-medium">{project.developer}</div>
                  </div>
                  <div>
                    <Label className="text-warm-gray text-sm">Status</Label>
                    <div className="text-soft-brown font-medium">{project.status}</div>
                  </div>
                  <div>
                    <Label className="text-warm-gray text-sm">Completion</Label>
                    <div className="text-soft-brown font-medium">{project.completion}</div>
                  </div>
                  <div>
                    <Label className="text-warm-gray text-sm">Units</Label>
                    <div className="text-soft-brown font-medium">{floorPlans.length} Types</div>
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="border-t border-beige pt-6">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-lg text-soft-brown">{floorPlans.reduce((sum, plan) => sum + plan.available, 0)}</div>
                      <div className="text-xs text-warm-gray">Units Available</div>
                    </div>
                    <div>
                      <div className="text-lg text-soft-brown">8.5%</div>
                      <div className="text-xs text-warm-gray">Expected ROI</div>
                    </div>
                    <div>
                      <div className="text-lg text-soft-brown">10%</div>
                      <div className="text-xs text-warm-gray">Down Payment</div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3 pt-6 border-t border-beige">

                  
                  <div className="grid grid-cols-2 gap-3">
                    <Button 
                      variant="outline"
                      size="sm"
                      onClick={() => setIsCallbackDialogOpen(true)}
                      className="border-soft-brown/30 text-soft-brown hover:bg-soft-brown/10"
                    >
                      <PhoneCall className="w-4 h-4 mr-1" />
                      Call Me
                    </Button>
                    <Button 
                      variant="outline"
                      size="sm"
                      onClick={() => setIsViewingDialogOpen(true)}
                      className="border-soft-brown/30 text-soft-brown hover:bg-soft-brown/10"
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      View
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Form */}
            <Card className="border border-beige shadow-sm">
              <CardHeader>
                <CardTitle className="text-soft-brown">Get More Information</CardTitle>
                <p className="text-warm-gray text-sm">Our experts will get in touch within 24 hours</p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleContactSubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="fullName">Full Name *</Label>
                    <Input
                      id="fullName"
                      value={contactFormData.fullName}
                      onChange={(e) => setContactFormData(prev => ({ ...prev, fullName: e.target.value }))}
                      placeholder="Enter your name"
                      className="mt-1"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="contactEmail">Email *</Label>
                    <Input
                      id="contactEmail"
                      type="email"
                      value={contactFormData.email}
                      onChange={(e) => setContactFormData(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="Enter your email"
                      className="mt-1"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="contactPhone">Phone *</Label>
                    <Input
                      id="contactPhone"
                      type="tel"
                      value={contactFormData.phone}
                      onChange={(e) => setContactFormData(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder="+971 50 123 4567"
                      className="mt-1"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="interest">I'm interested in</Label>
                    <select
                      id="interest"
                      value={contactFormData.interest}
                      onChange={(e) => setContactFormData(prev => ({ ...prev, interest: e.target.value }))}
                      className="w-full mt-1 p-3 border border-beige rounded-lg bg-white text-soft-brown"
                    >
                      <option value="buying">Buying</option>
                      <option value="investing">Investing</option>
                      <option value="viewing">Property Viewing</option>
                      <option value="information">More Information</option>
                    </select>
                  </div>
                  
                  <div>
                    <Label htmlFor="contactMessage">Message</Label>
                    <Textarea
                      id="contactMessage"
                      value={contactFormData.message}
                      onChange={(e) => setContactFormData(prev => ({ ...prev, message: e.target.value }))}
                      placeholder="Tell us about your requirements..."
                      className="mt-1"
                      rows={3}
                    />
                  </div>
                  
                  <Button type="submit" className="w-full bg-soft-brown hover:bg-soft-brown/90 text-white">
                    <Send className="w-4 h-4 mr-2" />
                    Send Inquiry
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card className="border border-beige shadow-sm">
              <CardHeader>
                <CardTitle className="text-soft-brown text-lg">Need Help?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gold/20 rounded-lg flex items-center justify-center">
                    <Phone className="w-5 h-5 text-gold" />
                  </div>
                  <div>
                    <div className="text-soft-brown font-medium">+971 4 123 4567</div>
                    <div className="text-xs text-warm-gray">Available 24/7</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gold/20 rounded-lg flex items-center justify-center">
                    <Mail className="w-5 h-5 text-gold" />
                  </div>
                  <div>
                    <div className="text-soft-brown font-medium"><EMAIL></div>
                    <div className="text-xs text-warm-gray">Response within 2 hours</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gold/20 rounded-lg flex items-center justify-center">
                    <MapPin className="w-5 h-5 text-gold" />
                  </div>
                  <div>
                    <div className="text-soft-brown font-medium">Business Bay, Dubai</div>
                    <div className="text-xs text-warm-gray">Visit our office</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Share Dialog */}
      <Dialog open={isShareDialogOpen} onOpenChange={setIsShareDialogOpen}>
        <DialogContent className="sm:max-w-md bg-white">
          <DialogHeader>
            <DialogTitle className="text-soft-brown">Share Property</DialogTitle>
            <DialogDescription className="text-warm-gray">
              Share {project.name} with friends and colleagues
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                onClick={() => handleShare('facebook')}
                className="justify-start"
              >
                <Facebook className="w-4 h-4 mr-2" />
                Facebook
              </Button>
              <Button
                variant="outline"
                onClick={() => handleShare('twitter')}
                className="justify-start"
              >
                <Twitter className="w-4 h-4 mr-2" />
                Twitter
              </Button>
              <Button
                variant="outline"
                onClick={() => handleShare('linkedin')}
                className="justify-start"
              >
                <Linkedin className="w-4 h-4 mr-2" />
                LinkedIn
              </Button>
              <Button
                variant="outline"
                onClick={() => handleShare('whatsapp')}
                className="justify-start"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                WhatsApp
              </Button>
            </div>
            
            <div className="flex items-center space-x-2">
              <Input
                value={window.location.href}
                readOnly
                className="flex-1"
              />
              <Button
                onClick={() => handleShare('copy')}
                variant="outline"
              >
                <Copy className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Investment Calculator Dialog */}
      <Dialog open={isInvestmentCalculatorOpen} onOpenChange={setIsInvestmentCalculatorOpen}>
        <DialogContent className="sm:max-w-6xl bg-white max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-6 border-b border-beige">
            <DialogTitle className="text-3xl text-soft-brown flex items-center">
              <TrendingUp className="w-8 h-8 mr-3 text-gold" />
              Investment Calculator
            </DialogTitle>
            <DialogDescription className="text-warm-gray mt-2 text-lg">
              Comprehensive ROI analysis for {project.name}
            </DialogDescription>
          </DialogHeader>

          <div className="py-8">
            <Tabs value={calculatorTab} onValueChange={setCalculatorTab} className="w-full">
              <div className="relative w-full">
                <TabsList className="w-full h-auto flex flex-nowrap md:grid md:grid-cols-3 bg-beige/50 rounded-xl p-2 overflow-x-auto gap-2 md:gap-0 mb-8">
                  <TabsTrigger 
                    value="roi" 
                    className="flex items-center rounded-lg flex-shrink-0 min-w-[120px] text-sm px-3 py-2"
                  >
                    <PieChart className="w-4 h-4 mr-2" />
                    <span className="hidden sm:inline">ROI Analysis</span>
                    <span className="sm:hidden">ROI</span>
                  </TabsTrigger>
                  <TabsTrigger 
                    value="mortgage" 
                    className="flex items-center rounded-lg flex-shrink-0 min-w-[120px] text-sm px-3 py-2"
                  >
                    <CreditCard className="w-4 h-4 mr-2" />
                    Mortgage
                  </TabsTrigger>
                  <TabsTrigger 
                    value="rental" 
                    className="flex items-center rounded-lg flex-shrink-0 min-w-[120px] text-sm px-3 py-2"
                  >
                    <Banknote className="w-4 h-4 mr-2" />
                    <span className="hidden sm:inline">Rental Yield</span>
                    <span className="sm:hidden">Rental</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              {/* ROI Analysis Tab */}
              <TabsContent value="roi" className="space-y-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <Card className="rounded-2xl border-0 shadow-xl">
                    <CardHeader className="p-6">
                      <CardTitle className="text-soft-brown text-xl">Investment Parameters</CardTitle>
                    </CardHeader>
                    <CardContent className="p-6 pt-0 space-y-8">
                      <div>
                        <Label className="flex items-center justify-between mb-3">
                          <span>Property Price</span>
                          <span className="text-gold font-medium">AED {investmentData.propertyPrice.toLocaleString()}</span>
                        </Label>
                        <Slider
                          value={[investmentData.propertyPrice]}
                          onValueChange={(value) => updateInvestmentData('propertyPrice', value)}
                          max={10000000}
                          min={500000}
                          step={50000}
                          className="w-full"
                        />
                      </div>

                      <div>
                        <Label className="flex items-center justify-between mb-3">
                          <span>Down Payment</span>
                          <span className="text-gold font-medium">{investmentData.downPayment}%</span>
                        </Label>
                        <Slider
                          value={[investmentData.downPayment]}
                          onValueChange={(value) => updateInvestmentData('downPayment', value)}
                          max={50}
                          min={10}
                          step={5}
                          className="w-full"
                        />
                      </div>

                      <div>
                        <Label className="flex items-center justify-between mb-3">
                          <span>Expected Rental Yield</span>
                          <span className="text-gold font-medium">{investmentData.expectedRentalYield}%</span>
                        </Label>
                        <Slider
                          value={[investmentData.expectedRentalYield]}
                          onValueChange={(value) => updateInvestmentData('expectedRentalYield', value)}
                          max={15}
                          min={4}
                          step={0.5}
                          className="w-full"
                        />
                      </div>

                      <div>
                        <Label className="flex items-center justify-between mb-3">
                          <span>Capital Appreciation</span>
                          <span className="text-gold font-medium">{investmentData.capitalAppreciation}%</span>
                        </Label>
                        <Slider
                          value={[investmentData.capitalAppreciation]}
                          onValueChange={(value) => updateInvestmentData('capitalAppreciation', value)}
                          max={12}
                          min={2}
                          step={0.5}
                          className="w-full"
                        />
                      </div>

                      <div>
                        <Label className="flex items-center justify-between mb-3">
                          <span>Holding Period</span>
                          <span className="text-gold font-medium">{investmentData.holdingPeriod} years</span>
                        </Label>
                        <Slider
                          value={[investmentData.holdingPeriod]}
                          onValueChange={(value) => updateInvestmentData('holdingPeriod', value)}
                          max={20}
                          min={3}
                          step={1}
                          className="w-full"
                        />
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="rounded-2xl border-0 shadow-xl">
                    <CardHeader className="p-6">
                      <CardTitle className="text-soft-brown text-xl">Investment Returns</CardTitle>
                    </CardHeader>
                    <CardContent className="p-6 pt-0 space-y-6">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-gold/10 p-6 rounded-2xl">
                          <div className="flex items-center justify-between mb-3">
                            <span className="text-sm text-warm-gray">Cash on Cash Return</span>
                            <TrendingUp className="w-5 h-5 text-gold" />
                          </div>
                          <div className="text-3xl text-soft-brown font-bold">
                            {calculateCashOnCash().toFixed(1)}%
                          </div>
                          <div className="text-xs text-warm-gray mt-2">Annual cash flow return</div>
                        </div>

                        <div className="bg-gold/10 p-6 rounded-2xl">
                          <div className="flex items-center justify-between mb-3">
                            <span className="text-sm text-warm-gray">Total ROI</span>
                            <Target className="w-5 h-5 text-gold" />
                          </div>
                          <div className="text-3xl text-soft-brown font-bold">
                            {calculateTotalROI().toFixed(1)}%
                          </div>
                          <div className="text-xs text-warm-gray mt-2">Over {investmentData.holdingPeriod} years</div>
                        </div>

                        <div className="bg-gold/10 p-6 rounded-2xl">
                          <div className="flex items-center justify-between mb-3">
                            <span className="text-sm text-warm-gray">Monthly Cash Flow</span>
                            <DollarSign className="w-5 h-5 text-gold" />
                          </div>
                          <div className="text-2xl text-soft-brown font-bold">
                            AED {(calculateNetRentalIncome() / 12 - calculateMortgagePayment()).toLocaleString(undefined, { maximumFractionDigits: 0 })}
                          </div>
                          <div className="text-xs text-warm-gray mt-2">Net monthly income</div>
                        </div>

                        <div className="bg-gold/10 p-6 rounded-2xl">
                          <div className="flex items-center justify-between mb-3">
                            <span className="text-sm text-warm-gray">Initial Investment</span>
                            <Wallet className="w-5 h-5 text-gold" />
                          </div>
                          <div className="text-xl text-soft-brown font-bold">
                            AED {(investmentData.propertyPrice * investmentData.downPayment / 100).toLocaleString(undefined, { maximumFractionDigits: 0 })}
                          </div>
                          <div className="text-xs text-warm-gray mt-2">Down payment required</div>
                        </div>
                      </div>

                      <div className="pt-6 border-t border-beige">
                        <h4 className="text-soft-brown mb-4 font-medium">Investment Summary</h4>
                        <div className="space-y-3">
                          <div className="flex justify-between">
                            <span className="text-warm-gray">Property Value:</span>
                            <span className="text-soft-brown font-medium">AED {investmentData.propertyPrice.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-warm-gray">Down Payment:</span>
                            <span className="text-soft-brown font-medium">AED {(investmentData.propertyPrice * investmentData.downPayment / 100).toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-warm-gray">Loan Amount:</span>
                            <span className="text-soft-brown font-medium">AED {(investmentData.propertyPrice * (100 - investmentData.downPayment) / 100).toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-warm-gray">Annual Rental Income:</span>
                            <span className="text-soft-brown font-medium">AED {calculateAnnualRentalIncome().toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between border-t border-beige pt-3">
                            <span className="text-warm-gray">Future Property Value:</span>
                            <span className="text-gold font-medium">AED {(investmentData.propertyPrice * Math.pow(1 + investmentData.capitalAppreciation / 100, investmentData.holdingPeriod)).toLocaleString(undefined, { maximumFractionDigits: 0 })}</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Mortgage Tab */}
              <TabsContent value="mortgage" className="space-y-8">
                <div className="text-center py-12">
                  <Calculator className="w-16 h-16 text-gold mx-auto mb-4" />
                  <h3 className="text-xl text-soft-brown mb-4">Mortgage Calculator</h3>
                  <p className="text-warm-gray">Detailed mortgage calculations and payment breakdown.</p>
                  <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
                    <div className="bg-beige/30 p-6 rounded-xl">
                      <div className="text-2xl text-soft-brown font-bold">AED {calculateMortgagePayment().toLocaleString(undefined, { maximumFractionDigits: 0 })}</div>
                      <div className="text-sm text-warm-gray">Monthly Payment</div>
                    </div>
                    <div className="bg-beige/30 p-6 rounded-xl">
                      <div className="text-2xl text-soft-brown font-bold">{investmentData.interestRate}%</div>
                      <div className="text-sm text-warm-gray">Interest Rate</div>
                    </div>
                    <div className="bg-beige/30 p-6 rounded-xl">
                      <div className="text-2xl text-soft-brown font-bold">{investmentData.mortgageTerm}</div>
                      <div className="text-sm text-warm-gray">Years</div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Rental Yield Tab */}
              <TabsContent value="rental" className="space-y-8">
                <div className="text-center py-12">
                  <Banknote className="w-16 h-16 text-gold mx-auto mb-4" />
                  <h3 className="text-xl text-soft-brown mb-4">Rental Yield Analysis</h3>
                  <p className="text-warm-gray">Comprehensive rental income and yield analysis.</p>
                  <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
                    <div className="bg-beige/30 p-6 rounded-xl">
                      <div className="text-2xl text-soft-brown font-bold">{investmentData.expectedRentalYield}%</div>
                      <div className="text-sm text-warm-gray">Gross Yield</div>
                    </div>
                    <div className="bg-beige/30 p-6 rounded-xl">
                      <div className="text-2xl text-soft-brown font-bold">AED {calculateNetRentalIncome().toLocaleString(undefined, { maximumFractionDigits: 0 })}</div>
                      <div className="text-sm text-warm-gray">Net Annual Income</div>
                    </div>
                    <div className="bg-beige/30 p-6 rounded-xl">
                      <div className="text-2xl text-soft-brown font-bold">AED {(calculateNetRentalIncome() / 12).toLocaleString(undefined, { maximumFractionDigits: 0 })}</div>
                      <div className="text-sm text-warm-gray">Monthly Income</div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>

      {/* Image Gallery Modal */}
      <Dialog open={isImageGalleryOpen} onOpenChange={setIsImageGalleryOpen}>
        <DialogContent className="sm:max-w-6xl bg-white max-h-[90vh] overflow-hidden">
          <DialogHeader className="pb-4 border-b border-beige">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-2xl text-soft-brown">Property Gallery</DialogTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsImageGalleryOpen(false)}
                className="text-warm-gray hover:text-soft-brown"
              >
                <X className="w-6 h-6" />
              </Button>
            </div>
          </DialogHeader>
          <div className="py-4">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {propertyImages.map((image, index) => (
                <div key={image.id} className="aspect-square rounded-lg overflow-hidden group cursor-pointer">
                  <ImageWithFallback
                    src={image.src}
                    alt={image.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    onClick={() => {
                      setSelectedImageIndex(index);
                      setIsImageGalleryOpen(false);
                    }}
                  />
                </div>
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}