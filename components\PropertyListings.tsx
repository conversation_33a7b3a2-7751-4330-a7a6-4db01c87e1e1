import { useState } from 'react';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Download, MapPin, Grid3X3, List, Bath, Bed, Square, Heart, TrendingUp, Clock, CalendarDays } from 'lucide-react';
import { ImageWithFallback } from './figma/ImageWithFallback';

interface Project {
  id: number;
  name: string;
  location: string;
  price: string;
  image: string;
  completion: string;
  description?: string;
  developer: string;
  status: string;
  bedrooms?: string;
  bathrooms?: string;
  size?: string;
  roi?: string;
  coordinates?: [number, number];
}

interface PropertyListingsProps {
  onProjectSelect: (project: Project) => void;
  onLoadMore: () => void;
}

export function PropertyListings({ onProjectSelect, onLoadMore }: PropertyListingsProps) {
  const [sortBy, setSortBy] = useState('newest');

  // Mock data for nearly ready properties (completing within 12 months)
  const nearlyReadyProperties: Project[] = [
    {
      id: 1,
      name: "Burj Crown",
      location: "Downtown Dubai",
      price: "AED 2,850,000",
      image: "https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      completion: "Q2 2025",
      developer: "Emaar Properties",
      status: "Nearly Ready",
      bedrooms: "2-3 BR",
      bathrooms: "3-4",
      size: "1,200-1,800 sq ft",
      roi: "8.5%",
      description: "Luxury apartments in the heart of Downtown Dubai with stunning Burj Khalifa views. Final finishing touches in progress.",
      coordinates: [25.1972, 55.2744]
    },
    {
      id: 2,
      name: "Marina Pearls",
      location: "Dubai Marina",
      price: "AED 1,950,000",
      image: "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      completion: "Q1 2025",
      developer: "Select Group",
      status: "Nearly Ready",
      bedrooms: "1-2 BR",
      bathrooms: "2-3",
      size: "850-1,200 sq ft",
      roi: "9.2%",
      description: "Waterfront living at its finest with marina views and premium amenities. Interior works 95% complete.",
      coordinates: [25.0772, 55.1384]
    },
    {
      id: 3,
      name: "Business Central",
      location: "Business Bay",
      price: "AED 1,450,000",
      image: "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      completion: "Q3 2025",
      developer: "Damac Properties",
      status: "Nearly Ready",
      bedrooms: "1-3 BR",
      bathrooms: "2-4",
      size: "750-1,500 sq ft",
      roi: "10.1%",
      description: "Modern business district living with canal views. Final MEP installations in progress.",
      coordinates: [25.1870, 55.2631]
    },
    {
      id: 4,
      name: "Creek Horizon",
      location: "Dubai Creek Harbour",
      price: "AED 2,200,000",
      image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      completion: "Q2 2025",
      developer: "Emaar Properties",
      status: "Nearly Ready",
      bedrooms: "2-3 BR",
      bathrooms: "3-4",
      size: "1,100-1,600 sq ft",
      roi: "8.8%",
      description: "Waterfront apartments with Creek Tower views. External works completed, interiors 90% done.",
      coordinates: [25.1838, 55.3167]
    },
    {
      id: 5,
      name: "JBR Residences",
      location: "JBR",
      price: "AED 3,200,000",
      image: "https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      completion: "Q1 2025",
      developer: "Nakheel",
      status: "Nearly Ready",
      bedrooms: "3-4 BR",
      bathrooms: "4-5",
      size: "1,800-2,500 sq ft",
      roi: "7.5%",
      description: "Beachfront luxury with direct beach access. Final quality checks and handover preparations underway.",
      coordinates: [25.0657, 55.1364]
    },
    {
      id: 6,
      name: "DIFC Gateway",
      location: "DIFC",
      price: "AED 2,650,000",
      image: "https://images.unsplash.com/photo-1515263487990-61b07816b018?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      completion: "Q3 2025",
      developer: "ICD Brookfield",
      status: "Nearly Ready",
      bedrooms: "1-2 BR",
      bathrooms: "2-3",
      size: "900-1,400 sq ft",
      roi: "8.1%",
      description: "Premium financial district living with city skyline views. Final inspections and certifications in progress.",
      coordinates: [25.2131, 55.2796]
    }
  ];

  const PropertyCard = ({ property }: { property: Project }) => {
    return (
      <Card 
        className="bg-white shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] hover:shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] hover:-translate-y-0.5 transition-all duration-300 overflow-hidden group cursor-pointer rounded-xl border-0 h-full"
        onClick={() => onProjectSelect(property)}
      >
        {/* Image */}
        <div className="relative h-48 overflow-hidden">
          <ImageWithFallback
            src={property.image}
            alt={property.name}
            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
          />
          <div className="absolute top-3 left-3">
            <Badge className="bg-gold text-soft-brown px-2 py-1 text-xs">
              <Clock className="w-3 h-3 mr-1" />
              {property.completion}
            </Badge>
          </div>
          <div className="absolute top-3 right-3">
            <Button
              size="sm"
              variant="ghost"
              className="bg-white/90 hover:bg-white text-soft-brown p-1.5 rounded-full"
              onClick={(e) => {
                e.stopPropagation();
                // Handle favorite logic
              }}
            >
              <Heart className="w-3 h-3" />
            </Button>
          </div>
          <div className="absolute bottom-3 left-3">
            <Badge variant="outline" className="bg-white/90 border-white text-soft-brown text-xs">
              {property.status}
            </Badge>
          </div>
        </div>
        
        {/* Content */}
        <CardContent className="p-4">
          <div className="mb-3">
            <h3 className="text-lg text-[rgba(30,26,26,1)] mb-1 group-hover:text-gold transition-colors line-clamp-1">
              {property.name}
            </h3>
            <div className="flex items-center text-warm-gray text-sm mb-2">
              <MapPin className="w-3 h-3 mr-1" />
              {property.location}
            </div>
            <div className="text-lg text-soft-brown">
              {property.price}
            </div>
          </div>

          <div className="grid grid-cols-3 gap-2 mb-3 text-center">
            <div>
              <div className="flex items-center justify-center mb-1">
                <Bed className="w-3 h-3 text-gold mr-1" />
                <span className="text-xs text-warm-gray">Beds</span>
              </div>
              <div className="text-soft-brown text-sm">{property.bedrooms}</div>
            </div>
            <div>
              <div className="flex items-center justify-center mb-1">
                <Bath className="w-3 h-3 text-gold mr-1" />
                <span className="text-xs text-warm-gray">Baths</span>
              </div>
              <div className="text-soft-brown text-sm">{property.bathrooms}</div>
            </div>
            <div>
              <div className="flex items-center justify-center mb-1">
                <TrendingUp className="w-3 h-3 text-emerald-500 mr-1" />
                <span className="text-xs text-warm-gray">ROI</span>
              </div>
              <div className="text-emerald-500 text-sm">{property.roi}</div>
            </div>
          </div>

          <div className="text-center">
            <div className="text-xs text-[rgba(30,26,26,0.6)] mb-2">
              By {property.developer}
            </div>
            <Button 
              className="bg-gold hover:bg-gold/90 text-[rgba(255,255,255,1)] px-4 py-1 text-sm w-full"
              onClick={(e) => {
                e.stopPropagation();
                onProjectSelect(property);
              }}
            >
              View Details
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <section className="section-padding bg-[rgba(255,255,255,1)]">
      <div className="container">
        
        {/* Section Header */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-12">
          <div className="mb-6 lg:mb-0">
            <h2 className="mb-4 text-[rgba(30,26,26,1)] text-[36px]">Nearly Ready Properties</h2>
            <p className="text-[rgba(30,26,26,1)] text-xl max-w-2xl leading-relaxed">
              Move in soon with these exceptional properties completing within 12 months. 
              Secure your investment with immediate handover opportunities.
            </p>
          </div>
          
          {/* Controls */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            {/* Sort Dropdown */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-52 rounded-xl bg-white border-soft-gray/30 text-soft-brown">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="completion">Earliest Completion</SelectItem>
                <SelectItem value="price-low">Price: Low to High</SelectItem>
                <SelectItem value="price-high">Price: High to Low</SelectItem>
                <SelectItem value="newest">Newest First</SelectItem>
                <SelectItem value="roi">Best ROI</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Results Info */}
        <div className="mb-8">
          <div className="bg-white rounded-xl p-4 shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)]">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between">
              <p className="text-[rgba(30,26,26,0.5)] mb-2 sm:mb-0">
                Showing <span className="text-[rgba(30,26,26,0.5)]">{nearlyReadyProperties.length} nearly ready properties</span> • Completion within 12 months
              </p>
              <div className="text-sm text-warm-gray flex items-center">
                <CalendarDays className="w-4 h-4 mr-1" />
                Average completion: <span className="text-gold ml-1">6-8 months</span>
              </div>
            </div>
          </div>
        </div>

        {/* Properties Horizontal Scroll */}
        <div className="overflow-x-auto">
          <div className="flex gap-8 pb-4" style={{ width: 'max-content' }}>
            {nearlyReadyProperties.map((property) => (
              <div key={property.id} className="flex-shrink-0 w-80">
                <PropertyCard property={property} />
              </div>
            ))}
          </div>
        </div>

        {/* Load More */}
        <div className="text-center mt-16">
          <Button 
            variant="outline" 
            size="lg" 
            className="border-soft-brown text-soft-brown hover:bg-soft-brown hover:text-white px-8 py-3 rounded-xl transition-all duration-300"
            onClick={onLoadMore}
          >
            View All
          </Button>
        </div>

        {/* Quick Handover Benefits */}
        <div className="mt-16 bg-[rgba(255,255,255,1)] rounded-2xl p-8 shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)]">
          <div className="text-center mb-8">
            <h3 className="text-[rgba(30,26,26,1)] mb-4">Benefits of Nearly Ready Properties</h3>
            <p className="text-[rgba(30,26,26,1)] text-lg">Why choosing properties with immediate handover makes smart investment sense</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center group">
              <div className="text-3xl text-gold mb-2 group-hover:scale-110 transition-transform duration-300">
                <Clock className="w-8 h-8 mx-auto mb-2" />
                3-12
              </div>
              <div className="text-[rgba(30,26,26,0.7)]">Months to Handover</div>
            </div>
            <div className="text-center group">
              <div className="text-3xl text-gold mb-2 group-hover:scale-110 transition-transform duration-300">
                <TrendingUp className="w-8 h-8 mx-auto mb-2" />
                85%
              </div>
              <div className="text-[rgba(30,26,26,0.7)]">Payment Completed</div>
            </div>
            <div className="text-center group">
              <div className="text-3xl text-gold mb-2 group-hover:scale-110 transition-transform duration-300">
                <CalendarDays className="w-8 h-8 mx-auto mb-2" />
                95%
              </div>
              <div className="text-[rgba(30,26,26,0.7)]">Construction Complete</div>
            </div>
            <div className="text-center group">
              <div className="text-3xl text-gold mb-2 group-hover:scale-110 transition-transform duration-300">
                <Download className="w-8 h-8 mx-auto mb-2" />
                100%
              </div>
              <div className="text-[rgba(30,26,26,0.7)]">Ready for Rental</div>
            </div>
          </div>
        </div>

      </div>
    </section>
  );
}