import { useEffect, useState } from 'react';

interface SplashScreenProps {
  onComplete: () => void;
}

export function SplashScreen({ onComplete }: SplashScreenProps) {
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    // Set up timing-based triggers exactly as specified:
    // 200ms delay → logo trace → glow (1200ms) → fade up (1800ms) → exit (3000ms) → complete (4000ms)
    
    const exitTimer = setTimeout(() => {
      setIsExiting(true);
      
      // Complete transition after exit animation (1000ms exit animation)
      setTimeout(() => {
        onComplete();
      }, 1000);
    }, 3000); // Start exit at 3000ms

    // Cleanup timer on unmount
    return () => {
      clearTimeout(exitTimer);
    };
  }, [onComplete]);

  return (
    <div className={`splash-screen ${isExiting ? 'splash-screen-exit' : ''}`}>
      
      {/* Floating ambient particles */}
      <div className="splash-particles">
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="splash-particle" />
        ))}
      </div>

      {/* Pulsing ambient lights */}
      <div className="splash-ambient-light" />
      <div className="splash-ambient-light" />
      <div className="splash-ambient-light" />

      {/* Main splash content */}
      <div className="splash-content">
        
        {/* Logo container with four-stage animation */}
        <div className="splash-logo-container">
          <div className="splash-logo">
            <svg
              className="splash-logo-svg"
              viewBox="0 0 800 200"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              {/* Circular element on the left */}
              <circle
                className="splash-logo-path"
                cx="60"
                cy="80"
                r="45"
                strokeWidth="3"
                fill="none"
              />
              
              {/* Partial arc inside the circle */}
              <path
                className="splash-logo-path"
                d="M 25 60 Q 40 45 60 50 Q 80 55 95 70"
                strokeWidth="3"
                fill="none"
              />

              {/* SMART text */}
              <g className="splash-logo-path" strokeWidth="2.5" fill="none">
                {/* S */}
                <path d="M 140 50 Q 140 40 150 40 L 170 40 Q 180 40 180 50 Q 180 60 170 65 L 155 65 Q 145 65 145 75 Q 145 85 155 85 L 175 85 Q 185 85 185 95" />
                
                {/* M */}
                <path d="M 200 95 L 200 40 L 210 55 L 220 40 L 230 95 M 210 40 L 210 95 M 220 40 L 220 95" />
                
                {/* A */}
                <path d="M 250 95 L 260 40 L 270 95 M 255 75 L 265 75" />
                
                {/* R */}
                <path d="M 285 95 L 285 40 L 305 40 Q 315 40 315 50 Q 315 60 305 65 L 285 65 M 295 65 L 315 95" />
                
                {/* T */}
                <path d="M 330 40 L 360 40 M 345 40 L 345 95" />
              </g>

              {/* OFF text */}
              <g className="splash-logo-path" strokeWidth="2.5" fill="none">
                {/* O */}
                <circle cx="390" cy="67.5" r="27.5" />
                
                {/* F */}
                <path d="M 440 95 L 440 40 L 470 40 M 440 67 L 465 67" />
                
                {/* F */}
                <path d="M 485 95 L 485 40 L 515 40 M 485 67 L 510 67" />
              </g>

              {/* PLAN text */}
              <g className="splash-logo-path" strokeWidth="2.5" fill="none">
                {/* P */}
                <path d="M 545 95 L 545 40 L 565 40 Q 575 40 575 50 Q 575 60 565 65 L 545 65" />
                
                {/* L */}
                <path d="M 590 95 L 590 40 M 590 95 L 615 95" />
                
                {/* A */}
                <path d="M 630 95 L 640 40 L 650 95 M 635 75 L 645 75" />
                
                {/* N */}
                <path d="M 665 95 L 665 40 L 685 95 L 685 40" />
              </g>

              {/* Rectangular frame on the right */}
              <g className="splash-logo-path" strokeWidth="3" fill="none">
                {/* Main rectangle frame */}
                <rect x="720" y="45" width="60" height="50" />
                
                {/* Corner accent lines */}
                <path d="M 720 45 L 720 35 L 710 35" />
                <path d="M 780 45 L 780 35 L 790 35" />
                <path d="M 780 95 L 780 105 L 790 105" />
                <path d="M 720 95 L 720 105 L 710 105" />
              </g>

              {/* Connecting line from circle to main text */}
              <path
                className="splash-logo-path"
                d="M 105 80 L 135 80"
                strokeWidth="2"
                fill="none"
              />

              {/* Decorative line under main text */}
              <path
                className="splash-logo-path"
                d="M 140 110 L 690 110"
                strokeWidth="1.5"
                fill="none"
              />

              {/* INVEST SMART subtitle */}
              <g className="splash-logo-path" strokeWidth="1.5" fill="none">
                {/* INVEST */}
                <path d="M 160 135 L 160 155 M 170 135 L 170 155 M 170 145 L 185 155 L 185 135 M 190 135 L 190 155 M 190 135 L 210 135 M 190 155 L 210 155 M 215 155 L 215 145 Q 215 135 225 135 Q 235 135 235 145 L 235 155 M 240 155 Q 250 155 250 145 Q 250 135 240 140 L 250 135 M 255 155 L 255 135 L 265 155 L 275 135" />
                
                {/* SMART */}
                <path d="M 310 135 Q 310 130 320 130 L 335 130 Q 345 130 345 135 Q 345 145 335 145 L 325 145 Q 315 145 315 155 M 350 155 L 350 130 L 360 145 L 370 130 L 380 155 M 360 130 L 360 155 M 370 130 L 370 155 M 390 155 L 400 130 L 410 155 M 395 145 L 405 145 M 415 155 L 415 130 L 435 130 Q 445 130 445 140 Q 445 150 435 150 L 415 150 M 425 150 L 445 155 M 450 155 L 450 130 L 470 130 M 460 130 L 460 155" />
              </g>

              {/* Additional decorative elements */}
              <circle
                className="splash-logo-path"
                cx="750"
                cy="70"
                r="8"
                strokeWidth="2"
                fill="none"
              />
              
              {/* Small accent lines */}
              <path
                className="splash-logo-path"
                d="M 120 45 L 130 45 M 120 50 L 125 50"
                strokeWidth="1.5"
                fill="none"
              />
            </svg>
          </div>
        </div>

        {/* Brand text with luxury typography */}
        <h1 className="splash-brand-text">
          Smart Off Plan
        </h1>

        {/* Tagline */}
        <p className="splash-tagline">
          Your Gateway to Dubai's Premium Properties
        </p>

        {/* Loading experience section */}
        <div className="splash-loading-section">
          
          {/* Loading experience text */}
          <div className="splash-loading-text">
            Loading Experience
          </div>

          {/* Circular pulse animation */}
          <div className="splash-pulse-container">
            <div className="splash-pulse-circle" />
          </div>

          {/* Progress line */}
          <div className="splash-progress-container">
            <div className="splash-progress-bar" />
          </div>

        </div>

      </div>
    </div>
  );
}