"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "RouterContext", {
    enumerable: true,
    get: function() {
        return RouterContext;
    }
});
const _interop_require_default = require("@swc/helpers/_/_interop_require_default");
const _react = /*#__PURE__*/ _interop_require_default._(require("react"));
const RouterContext = _react.default.createContext(null);
if (process.env.NODE_ENV !== "production") {
    RouterContext.displayName = "RouterContext";
}

//# sourceMappingURL=router-context.shared-runtime.js.map