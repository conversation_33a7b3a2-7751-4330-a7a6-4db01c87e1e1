{"name": "smart-off-plan", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@tailwindcss/typography": "^0.5.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "dompurify": "^3.2.6", "embla-carousel-react": "^8.0.0", "input-otp": "^1.2.4", "jspdf": "^3.0.1", "lucide-react": "^0.294.0", "next": "^14.0.0", "postcss": "^8.4.0", "react": "^18.2.0", "react-day-picker": "^8.9.1", "react-dom": "^18.2.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "react-resizable-panels": "^0.0.55", "recharts": "^2.8.0", "sonner": "^1.2.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "vaul": "^0.7.9", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0"}}