import { NextRequest, NextResponse } from 'next/server'

// Transform external API data to our internal format (same as in main route)
function transformProperty(externalProperty: any) {
  return {
    id: externalProperty.id || externalProperty._id || `prop_${Date.now()}`,
    title: externalProperty.title || externalProperty.name || 'Untitled Property',
    description: externalProperty.description || externalProperty.summary || '',
    price: parseFloat(externalProperty.price) || 0,
    currency: externalProperty.currency || 'AED',
    propertyType: externalProperty.propertyType || externalProperty.type || 'apartment',
    status: externalProperty.status || 'available',
    bedrooms: parseInt(externalProperty.bedrooms) || parseInt(externalProperty.beds) || 0,
    bathrooms: parseInt(externalProperty.bathrooms) || parseInt(externalProperty.baths) || 0,
    area: parseFloat(externalProperty.area) || parseFloat(externalProperty.size) || 0,
    areaUnit: externalProperty.areaUnit || externalProperty.sizeUnit || 'sqft',
    location: {
      name: externalProperty.location?.name || externalProperty.area || 'Unknown Location',
      city: externalProperty.location?.city || externalProperty.city || 'Dubai',
      country: externalProperty.location?.country || externalProperty.country || 'UAE',
      coordinates: externalProperty.location?.coordinates || externalProperty.coordinates || undefined,
    },
    developer: externalProperty.developer ? {
      name: externalProperty.developer.name || externalProperty.developerName || 'Unknown Developer',
      logo: externalProperty.developer.logo || externalProperty.developerLogo || undefined,
    } : undefined,
    images: Array.isArray(externalProperty.images) 
      ? externalProperty.images.map((img: any) => ({
          url: typeof img === 'string' ? img : img.url || img.src,
          alt: typeof img === 'object' ? img.alt || img.caption : undefined,
          type: typeof img === 'object' ? img.type : 'gallery',
        }))
      : externalProperty.image 
        ? [{ url: externalProperty.image, type: 'main' }]
        : [],
    amenities: Array.isArray(externalProperty.amenities) 
      ? externalProperty.amenities 
      : externalProperty.amenities 
        ? externalProperty.amenities.split(',').map((a: string) => a.trim())
        : [],
    features: Array.isArray(externalProperty.features) 
      ? externalProperty.features 
      : externalProperty.features 
        ? externalProperty.features.split(',').map((f: string) => f.trim())
        : [],
    completionDate: externalProperty.completionDate || externalProperty.handoverDate || undefined,
    isFeatured: Boolean(externalProperty.isFeatured || externalProperty.featured),
    isActive: Boolean(externalProperty.isActive !== false),
    createdAt: externalProperty.createdAt || externalProperty.created_at || new Date().toISOString(),
    updatedAt: externalProperty.updatedAt || externalProperty.updated_at || new Date().toISOString(),
    // Additional fields that might be available for single property
    paymentPlan: externalProperty.paymentPlan || undefined,
    floorPlan: externalProperty.floorPlan || undefined,
    virtualTour: externalProperty.virtualTour || undefined,
    video: externalProperty.video || undefined,
    documents: externalProperty.documents || [],
    nearbyPlaces: externalProperty.nearbyPlaces || [],
    transportation: externalProperty.transportation || [],
    views: parseInt(externalProperty.views) || 0,
    slug: externalProperty.slug || undefined,
    seoTitle: externalProperty.seoTitle || undefined,
    seoDescription: externalProperty.seoDescription || undefined,
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const propertyId = params.id

    if (!propertyId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing property ID',
          message: 'Property ID is required'
        },
        { status: 400 }
      )
    }

    // Get API configuration
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL
    const apiKey = process.env.NEXT_PUBLIC_API_KEY

    if (!apiBaseUrl || !apiKey) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'API configuration missing',
          message: 'API base URL or API key not configured'
        },
        { status: 500 }
      )
    }

    // Build external API URL
    const externalApiUrl = `${apiBaseUrl}/api/properties/${propertyId}`

    // Make request to external API
    const response = await fetch(externalApiUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'User-Agent': 'SmartOffPlan/1.0',
      },
      // Add timeout
      signal: AbortSignal.timeout(10000), // 10 second timeout
    })

    if (!response.ok) {
      if (response.status === 404) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Property not found',
            message: `Property with ID ${propertyId} was not found`
          },
          { status: 404 }
        )
      }

      console.error(`External API error: ${response.status} ${response.statusText}`)
      
      return NextResponse.json(
        { 
          success: false, 
          error: 'External API error',
          message: `Failed to fetch property: ${response.status} ${response.statusText}`,
          statusCode: response.status
        },
        { status: response.status >= 500 ? 502 : response.status }
      )
    }

    const externalData = await response.json()

    // Handle different response formats from external API
    let property: any = null

    if (externalData.data) {
      // Wrapped response with data property
      property = externalData.data
    } else if (externalData.property) {
      // Alternative property name
      property = externalData.property
    } else if (externalData.id || externalData._id) {
      // Direct property object
      property = externalData
    } else {
      console.error('Unexpected API response format:', externalData)
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid API response format',
          message: 'External API returned unexpected data format'
        },
        { status: 502 }
      )
    }

    if (!property) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Property not found',
          message: `Property with ID ${propertyId} was not found`
        },
        { status: 404 }
      )
    }

    // Transform property to our internal format
    const transformedProperty = transformProperty(property)

    // Build response
    const responseData = {
      data: transformedProperty,
      success: true,
    }

    // Add cache headers for better performance
    const response_headers = new Headers()
    response_headers.set('Cache-Control', 'public, s-maxage=600, stale-while-revalidate=1200') // 10 min cache
    response_headers.set('X-Property-ID', propertyId)

    return NextResponse.json(responseData, { 
      status: 200,
      headers: response_headers
    })

  } catch (error) {
    console.error('Property detail API error:', error)

    // Handle specific error types
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Network error',
          message: 'Failed to connect to external API. Please check your internet connection.'
        },
        { status: 503 }
      )
    }

    if (error instanceof Error && error.name === 'AbortError') {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Timeout error',
          message: 'Request to external API timed out. Please try again.'
        },
        { status: 504 }
      )
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching property details.'
      },
      { status: 500 }
    )
  }
}
