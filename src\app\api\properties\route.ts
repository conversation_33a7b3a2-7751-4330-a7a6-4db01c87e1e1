import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    console.log("🚀 Properties API called");

    const { searchParams } = new URL(request.url);

    // Extract query parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");

    // Get API configuration
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
    const apiKey = process.env.NEXT_PUBLIC_API_KEY;

    console.log("🔧 API Config:", {
      apiBaseUrl,
      hasApiKey: !!apiKey,
      apiKeyLength: apiKey?.length,
    });

    if (!apiBaseUrl || !apiKey) {
      return NextResponse.json(
        {
          success: false,
          error: "API configuration missing",
          message: "API base URL or API key not configured",
        },
        { status: 500 }
      );
    }

    // Build external API URL
    const externalApiUrl = `${apiBaseUrl}/v1/properties`;
    console.log("📡 Calling external API:", externalApiUrl);

    // Make request to external API
    const response = await fetch(externalApiUrl, {
      method: "GET",
      headers: {
        "X-RapidAPI-Key": apiKey,
        "X-RapidAPI-Host": "realty-in-us.p.rapidapi.com",
        "Content-Type": "application/json",
      },
    });

    console.log("📥 External API response status:", response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ External API error:", response.status, errorText);

      return NextResponse.json(
        {
          success: false,
          error: "External API error",
          message: `Failed to fetch properties: ${response.status} ${response.statusText}`,
          details: errorText,
          apiUrl: externalApiUrl,
        },
        { status: response.status === 404 ? 404 : 502 }
      );
    }

    const externalData = await response.json();
    console.log("✅ External API response received");
    console.log("📊 Response type:", typeof externalData);

    // Return the raw response for now so we can see the structure
    return NextResponse.json({
      success: true,
      data: externalData,
      message: "Properties fetched successfully",
      apiUrl: externalApiUrl,
      requestParams: { page, limit },
    });
  } catch (error) {
    console.error("❌ Properties API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        message: "An unexpected error occurred while fetching properties.",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
