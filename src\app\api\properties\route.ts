import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'

// Types for the external API response
interface ExternalProperty {
  id: string
  title: string
  description?: string
  price: number
  currency: string
  propertyType: string
  status: string
  bedrooms: number
  bathrooms: number
  area: number
  areaUnit: string
  location: {
    name: string
    city: string
    country: string
    coordinates?: {
      latitude: number
      longitude: number
    }
  }
  developer?: {
    name: string
    logo?: string
  }
  images: Array<{
    url: string
    alt?: string
    type?: string
  }>
  amenities?: string[]
  features?: string[]
  completionDate?: string
  isFeatured?: boolean
  isActive?: boolean
  createdAt: string
  updatedAt: string
}

interface ExternalApiResponse {
  data: ExternalProperty[]
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  success: boolean
  message?: string
}

// Transform external API data to our internal format
function transformProperty(externalProperty: any): ExternalProperty {
  return {
    id: externalProperty.id || externalProperty._id || `prop_${Date.now()}`,
    title: externalProperty.title || externalProperty.name || 'Untitled Property',
    description: externalProperty.description || externalProperty.summary || '',
    price: parseFloat(externalProperty.price) || 0,
    currency: externalProperty.currency || 'AED',
    propertyType: externalProperty.propertyType || externalProperty.type || 'apartment',
    status: externalProperty.status || 'available',
    bedrooms: parseInt(externalProperty.bedrooms) || parseInt(externalProperty.beds) || 0,
    bathrooms: parseInt(externalProperty.bathrooms) || parseInt(externalProperty.baths) || 0,
    area: parseFloat(externalProperty.area) || parseFloat(externalProperty.size) || 0,
    areaUnit: externalProperty.areaUnit || externalProperty.sizeUnit || 'sqft',
    location: {
      name: externalProperty.location?.name || externalProperty.area || 'Unknown Location',
      city: externalProperty.location?.city || externalProperty.city || 'Dubai',
      country: externalProperty.location?.country || externalProperty.country || 'UAE',
      coordinates: externalProperty.location?.coordinates || externalProperty.coordinates || undefined,
    },
    developer: externalProperty.developer ? {
      name: externalProperty.developer.name || externalProperty.developerName || 'Unknown Developer',
      logo: externalProperty.developer.logo || externalProperty.developerLogo || undefined,
    } : undefined,
    images: Array.isArray(externalProperty.images) 
      ? externalProperty.images.map((img: any) => ({
          url: typeof img === 'string' ? img : img.url || img.src,
          alt: typeof img === 'object' ? img.alt || img.caption : undefined,
          type: typeof img === 'object' ? img.type : 'gallery',
        }))
      : externalProperty.image 
        ? [{ url: externalProperty.image, type: 'main' }]
        : [],
    amenities: Array.isArray(externalProperty.amenities) 
      ? externalProperty.amenities 
      : externalProperty.amenities 
        ? externalProperty.amenities.split(',').map((a: string) => a.trim())
        : [],
    features: Array.isArray(externalProperty.features) 
      ? externalProperty.features 
      : externalProperty.features 
        ? externalProperty.features.split(',').map((f: string) => f.trim())
        : [],
    completionDate: externalProperty.completionDate || externalProperty.handoverDate || undefined,
    isFeatured: Boolean(externalProperty.isFeatured || externalProperty.featured),
    isActive: Boolean(externalProperty.isActive !== false), // Default to true unless explicitly false
    createdAt: externalProperty.createdAt || externalProperty.created_at || new Date().toISOString(),
    updatedAt: externalProperty.updatedAt || externalProperty.updated_at || new Date().toISOString(),
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Extract query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '12')
    const search = searchParams.get('search') || ''
    const propertyType = searchParams.get('propertyType') || ''
    const minPrice = searchParams.get('minPrice') || ''
    const maxPrice = searchParams.get('maxPrice') || ''
    const bedrooms = searchParams.get('bedrooms') || ''
    const bathrooms = searchParams.get('bathrooms') || ''
    const location = searchParams.get('location') || ''
    const developer = searchParams.get('developer') || ''
    const featured = searchParams.get('featured') || ''
    const sort = searchParams.get('sort') || 'createdAt:desc'

    // Get API configuration
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL
    const apiKey = process.env.NEXT_PUBLIC_API_KEY

    if (!apiBaseUrl || !apiKey) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'API configuration missing',
          message: 'API base URL or API key not configured'
        },
        { status: 500 }
      )
    }

    // Build external API URL with parameters
    const externalApiUrl = new URL(`${apiBaseUrl}/api/properties`)
    
    // Add query parameters to external API call
    externalApiUrl.searchParams.set('page', page.toString())
    externalApiUrl.searchParams.set('limit', limit.toString())
    
    if (search) externalApiUrl.searchParams.set('search', search)
    if (propertyType) externalApiUrl.searchParams.set('propertyType', propertyType)
    if (minPrice) externalApiUrl.searchParams.set('minPrice', minPrice)
    if (maxPrice) externalApiUrl.searchParams.set('maxPrice', maxPrice)
    if (bedrooms) externalApiUrl.searchParams.set('bedrooms', bedrooms)
    if (bathrooms) externalApiUrl.searchParams.set('bathrooms', bathrooms)
    if (location) externalApiUrl.searchParams.set('location', location)
    if (developer) externalApiUrl.searchParams.set('developer', developer)
    if (featured) externalApiUrl.searchParams.set('featured', featured)
    if (sort) externalApiUrl.searchParams.set('sort', sort)

    // Make request to external API
    const response = await fetch(externalApiUrl.toString(), {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'User-Agent': 'SmartOffPlan/1.0',
      },
      // Add timeout
      signal: AbortSignal.timeout(10000), // 10 second timeout
    })

    if (!response.ok) {
      console.error(`External API error: ${response.status} ${response.statusText}`)
      
      return NextResponse.json(
        { 
          success: false, 
          error: 'External API error',
          message: `Failed to fetch properties: ${response.status} ${response.statusText}`,
          statusCode: response.status
        },
        { status: response.status === 404 ? 404 : 502 }
      )
    }

    const externalData = await response.json()

    // Handle different response formats from external API
    let properties: any[] = []
    let pagination = undefined

    if (Array.isArray(externalData)) {
      // Direct array response
      properties = externalData
    } else if (externalData.data && Array.isArray(externalData.data)) {
      // Wrapped response with data property
      properties = externalData.data
      pagination = externalData.pagination || externalData.meta
    } else if (externalData.properties && Array.isArray(externalData.properties)) {
      // Alternative property name
      properties = externalData.properties
      pagination = externalData.pagination || externalData.meta
    } else {
      console.error('Unexpected API response format:', externalData)
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid API response format',
          message: 'External API returned unexpected data format'
        },
        { status: 502 }
      )
    }

    // Transform properties to our internal format
    const transformedProperties = properties.map(transformProperty)

    // Build response
    const responseData: ExternalApiResponse = {
      data: transformedProperties,
      success: true,
      pagination: pagination || {
        page,
        limit,
        total: transformedProperties.length,
        totalPages: Math.ceil(transformedProperties.length / limit),
        hasNext: false,
        hasPrev: page > 1,
      }
    }

    // Add cache headers for better performance
    const response_headers = new Headers()
    response_headers.set('Cache-Control', 'public, s-maxage=300, stale-while-revalidate=600') // 5 min cache
    response_headers.set('X-Total-Count', responseData.pagination?.total.toString() || '0')
    response_headers.set('X-Page', page.toString())
    response_headers.set('X-Per-Page', limit.toString())

    return NextResponse.json(responseData, { 
      status: 200,
      headers: response_headers
    })

  } catch (error) {
    console.error('Properties API error:', error)

    // Handle specific error types
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Network error',
          message: 'Failed to connect to external API. Please check your internet connection.'
        },
        { status: 503 }
      )
    }

    if (error instanceof Error && error.name === 'AbortError') {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Timeout error',
          message: 'Request to external API timed out. Please try again.'
        },
        { status: 504 }
      )
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching properties.'
      },
      { status: 500 }
    )
  }
}
