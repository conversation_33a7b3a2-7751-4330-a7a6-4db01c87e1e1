'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { api } from '@/lib/api-client'
import { endpoints } from '@/lib/config'

export default function TestApiPage() {
  const [loading, setLoading] = useState(false)
  const [properties, setProperties] = useState<any[]>([])
  const [error, setError] = useState<string | null>(null)
  const [selectedProperty, setSelectedProperty] = useState<any>(null)

  const fetchProperties = async () => {
    setLoading(true)
    setError(null)
    
    try {
      console.log('Fetching properties from:', endpoints.properties.list)
      const response = await api.get(endpoints.properties.list, {
        params: {
          page: 1,
          limit: 10,
        }
      })
      
      console.log('API Response:', response)
      setProperties(response.data || [])
    } catch (err: any) {
      console.error('API Error:', err)
      setError(err.message || 'Failed to fetch properties')
    } finally {
      setLoading(false)
    }
  }

  const fetchPropertyDetail = async (propertyId: string) => {
    setLoading(true)
    setError(null)
    
    try {
      console.log('Fetching property detail for ID:', propertyId)
      const response = await api.get(endpoints.properties.detail(propertyId))
      
      console.log('Property Detail Response:', response)
      setSelectedProperty(response.data)
    } catch (err: any) {
      console.error('API Error:', err)
      setError(err.message || 'Failed to fetch property detail')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">API Test Page</h1>
      
      {/* API Configuration Display */}
      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-2">API Configuration</h2>
        <p><strong>Base URL:</strong> {process.env.NEXT_PUBLIC_API_BASE_URL}</p>
        <p><strong>API Key:</strong> {process.env.NEXT_PUBLIC_API_KEY ? '✅ Set' : '❌ Not Set'}</p>
        <p><strong>Properties Endpoint:</strong> {endpoints.properties.list}</p>
      </div>

      {/* Test Properties List */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-4">
          <h2 className="text-xl font-semibold">Test Properties List</h2>
          <Button 
            onClick={fetchProperties} 
            disabled={loading}
            className="bg-soft-brown hover:bg-deep-brown"
          >
            {loading ? (
              <>
                <LoadingSpinner size="sm" color="white" className="mr-2" />
                Loading...
              </>
            ) : (
              'Fetch Properties'
            )}
          </Button>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <strong>Error:</strong> {error}
          </div>
        )}

        {properties.length > 0 && (
          <div className="bg-white border rounded-lg overflow-hidden">
            <div className="px-4 py-3 bg-gray-50 border-b">
              <h3 className="font-semibold">Properties Found: {properties.length}</h3>
            </div>
            <div className="divide-y">
              {properties.slice(0, 5).map((property, index) => (
                <div key={property.id || index} className="p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium text-lg">{property.title}</h4>
                      <p className="text-gray-600">{property.location?.name}, {property.location?.city}</p>
                      <p className="text-soft-brown font-semibold">
                        {property.currency} {property.price?.toLocaleString()}
                      </p>
                      <p className="text-sm text-gray-500">
                        {property.bedrooms} bed • {property.bathrooms} bath • {property.area} {property.areaUnit}
                      </p>
                    </div>
                    <Button
                      onClick={() => fetchPropertyDetail(property.id)}
                      variant="outline"
                      size="sm"
                      disabled={loading}
                    >
                      View Details
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Property Detail */}
      {selectedProperty && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Property Detail</h2>
          <div className="bg-white border rounded-lg p-6">
            <h3 className="text-2xl font-bold mb-4">{selectedProperty.title}</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Basic Information</h4>
                <ul className="space-y-1 text-sm">
                  <li><strong>Price:</strong> {selectedProperty.currency} {selectedProperty.price?.toLocaleString()}</li>
                  <li><strong>Type:</strong> {selectedProperty.propertyType}</li>
                  <li><strong>Status:</strong> {selectedProperty.status}</li>
                  <li><strong>Bedrooms:</strong> {selectedProperty.bedrooms}</li>
                  <li><strong>Bathrooms:</strong> {selectedProperty.bathrooms}</li>
                  <li><strong>Area:</strong> {selectedProperty.area} {selectedProperty.areaUnit}</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Location</h4>
                <ul className="space-y-1 text-sm">
                  <li><strong>Area:</strong> {selectedProperty.location?.name}</li>
                  <li><strong>City:</strong> {selectedProperty.location?.city}</li>
                  <li><strong>Country:</strong> {selectedProperty.location?.country}</li>
                </ul>
                
                {selectedProperty.developer && (
                  <div className="mt-4">
                    <h4 className="font-semibold mb-2">Developer</h4>
                    <p className="text-sm">{selectedProperty.developer.name}</p>
                  </div>
                )}
              </div>
            </div>

            {selectedProperty.description && (
              <div className="mt-6">
                <h4 className="font-semibold mb-2">Description</h4>
                <p className="text-sm text-gray-600">{selectedProperty.description}</p>
              </div>
            )}

            {selectedProperty.amenities && selectedProperty.amenities.length > 0 && (
              <div className="mt-6">
                <h4 className="font-semibold mb-2">Amenities</h4>
                <div className="flex flex-wrap gap-2">
                  {selectedProperty.amenities.map((amenity: string, index: number) => (
                    <span 
                      key={index}
                      className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
                    >
                      {amenity}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {selectedProperty.images && selectedProperty.images.length > 0 && (
              <div className="mt-6">
                <h4 className="font-semibold mb-2">Images ({selectedProperty.images.length})</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {selectedProperty.images.slice(0, 4).map((image: any, index: number) => (
                    <div key={index} className="aspect-square bg-gray-200 rounded overflow-hidden">
                      <img 
                        src={image.url} 
                        alt={image.alt || `Property image ${index + 1}`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/placeholder-image.jpg'
                        }}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Raw Response Display */}
      {(properties.length > 0 || selectedProperty) && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Raw API Response</h2>
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto max-h-96">
            <pre className="text-xs">
              {JSON.stringify(selectedProperty || properties, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  )
}
