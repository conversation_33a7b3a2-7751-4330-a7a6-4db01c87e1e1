import { useState, useMemo } from "react";
import { PropertyFilters } from "./PropertyFilters";
import { PropertyListings } from "./PropertyListings";
import { FeaturedProjects } from "./FeaturedProjects";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "./ui/dialog";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { Slider } from "./ui/slider";
import { Separator } from "./ui/separator";
import { Switch } from "./ui/switch";
import {
  Home,
  ChevronRight,
  Building2,
  MapPin,
  Calendar,
  DollarSign,
  TrendingUp,
  Filter,
  Grid3X3,
  List,
  Search,
  SlidersHorizontal,
  Star,
  Bed,
  Bath,
  Square,
  ArrowUpDown,
  X,
  RotateCcw,
  Check,
  Banknote,
  Ruler,
  Clock,
  Hammer,
  Home as HomeIcon,
  Users,
  ShoppingCart,
} from "lucide-react";
import { ImageWithFallback } from "./figma/ImageWithFallback";

interface AllPropertiesPageProps {
  onProjectSelect: (project: any) => void;
  onBack: () => void;
  selectedDeveloper?: any;
}

interface FilterState {
  priceUnit: "total" | "sqft";
  minArea: number;
  maxArea: number;
  developmentStatus: string[];
  unitTypes: string[];
  bedrooms: string[];
  salesStatus: string[];
  completionDate: string;
  minPrice: number;
  maxPrice: number;
}

const initialFilters: FilterState = {
  priceUnit: "total",
  minArea: 0,
  maxArea: 5000,
  developmentStatus: [],
  unitTypes: [],
  bedrooms: [],
  salesStatus: [],
  completionDate: "all",
  minPrice: 0,
  maxPrice: ********,
};

export function AllPropertiesPage({
  onProjectSelect,
  onBack,
  selectedDeveloper,
}: AllPropertiesPageProps) {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState("featured");
  const [searchQuery, setSearchQuery] = useState("");
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [filters, setFilters] = useState<FilterState>(initialFilters);

  // Mock data for properties
  const allProperties = [
    {
      id: 1,
      name: "Marina Vista Towers",
      location: "Dubai Marina",
      developer: "Emaar Properties",
      image:
        "https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      price: "AED 1,200,000",
      pricePerSqft: 1200,
      area: 1000,
      bedrooms: "2 BR",
      bathrooms: 2,
      size: "1,000 sq ft",
      completion: "Q4 2025",
      status: "Under Construction",
      salesStatus: "On Sale",
      unitType: "Apartments",
      featured: true,
      paymentPlan: "60/40",
      roi: "8.5%",
    },
    {
      id: 2,
      name: "Downtown Heights",
      location: "Downtown Dubai",
      developer: "Dubai Properties",
      image:
        "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      price: "AED 2,500,000",
      pricePerSqft: 1500,
      area: 1667,
      bedrooms: "3 BR",
      bathrooms: 3,
      size: "1,667 sq ft",
      completion: "Q2 2026",
      status: "Presale",
      salesStatus: "Presale (EOI)",
      unitType: "Apartments",
      featured: true,
      paymentPlan: "70/30",
      roi: "9.2%",
    },
    {
      id: 3,
      name: "Palm Luxury Villas",
      location: "Palm Jumeirah",
      developer: "Nakheel",
      image:
        "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      price: "AED 8,500,000",
      pricePerSqft: 2125,
      area: 4000,
      bedrooms: "5+ BR",
      bathrooms: 6,
      size: "4,000 sq ft",
      completion: "Q1 2025",
      status: "Completed",
      salesStatus: "On Sale",
      unitType: "Villa",
      featured: false,
      paymentPlan: "Cash",
      roi: "7.8%",
    },
    {
      id: 4,
      name: "Business Bay Towers",
      location: "Business Bay",
      developer: "Damac Properties",
      image:
        "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      price: "AED 950,000",
      pricePerSqft: 1350,
      area: 704,
      bedrooms: "1 BR",
      bathrooms: 1,
      size: "704 sq ft",
      completion: "Q3 2025",
      status: "Under Construction",
      salesStatus: "Start of Sales",
      unitType: "Apartments",
      featured: false,
      paymentPlan: "80/20",
      roi: "8.8%",
    },
    {
      id: 5,
      name: "Creek Harbour Residences",
      location: "Dubai Creek Harbour",
      developer: "Emaar Properties",
      image:
        "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      price: "AED 1,800,000",
      pricePerSqft: 1286,
      area: 1400,
      bedrooms: "2 BR",
      bathrooms: 2,
      size: "1,400 sq ft",
      completion: "Q1 2027",
      status: "Presale",
      salesStatus: "Announced",
      unitType: "Apartments",
      featured: true,
      paymentPlan: "60/40",
      roi: "9.5%",
    },
    {
      id: 6,
      name: "DIFC Penthouses",
      location: "DIFC",
      developer: "Omniyat",
      image:
        "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      price: "AED 15,000,000",
      pricePerSqft: 3750,
      area: 4000,
      bedrooms: "4 BR",
      bathrooms: 5,
      size: "4,000 sq ft",
      completion: "Q2 2026",
      status: "Under Construction",
      salesStatus: "On Sale",
      unitType: "Penthouse",
      featured: true,
      paymentPlan: "50/50",
      roi: "6.5%",
    },
  ];

  // Filter options
  const developmentStatusOptions = [
    "Presale",
    "Under Construction",
    "Completed",
  ];
  const unitTypeOptions = [
    "Apartments",
    "Villa",
    "Townhouse",
    "Duplex",
    "Penthouse",
  ];
  const bedroomOptions = ["Studio", "1 BR", "2 BR", "3 BR", "4 BR", "5+ BR"];
  const salesStatusOptions = [
    "Announced",
    "Presale (EOI)",
    "Start of Sales",
    "On Sale",
    "Out of Stock",
  ];
  const completionDateOptions = [
    { value: "all", label: "All Projects" },
    { value: "12months", label: "Completing in 12 months" },
    { value: "2years", label: "Completing in 2 years" },
    { value: "3years", label: "Completing in 3 years" },
    { value: "4years", label: "Completing in 4 years" },
    { value: "5years", label: "Completing in 5+ years" },
  ];

  // Helper functions for filters
  const toggleArrayFilter = (array: string[], value: string) => {
    return array.includes(value)
      ? array.filter((item) => item !== value)
      : [...array, value];
  };

  const updateFilters = (key: keyof FilterState, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const resetFilters = () => {
    setFilters(initialFilters);
  };

  const hasActiveFilters = useMemo(() => {
    return (
      filters.priceUnit !== "total" ||
      filters.minArea !== 0 ||
      filters.maxArea !== 5000 ||
      filters.developmentStatus.length > 0 ||
      filters.unitTypes.length > 0 ||
      filters.bedrooms.length > 0 ||
      filters.salesStatus.length > 0 ||
      filters.completionDate !== "all" ||
      filters.minPrice !== 0 ||
      filters.maxPrice !== ********
    );
  }, [filters]);

  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (filters.priceUnit !== "total") count++;
    if (filters.minArea !== 0 || filters.maxArea !== 5000) count++;
    if (filters.developmentStatus.length > 0) count++;
    if (filters.unitTypes.length > 0) count++;
    if (filters.bedrooms.length > 0) count++;
    if (filters.salesStatus.length > 0) count++;
    if (filters.completionDate !== "all") count++;
    if (filters.minPrice !== 0 || filters.maxPrice !== ********) count++;
    return count;
  }, [filters]);

  // Apply filters to properties
  const filteredProperties = useMemo(() => {
    let filtered = [...allProperties];

    // Apply search query
    if (searchQuery) {
      filtered = filtered.filter(
        (property) =>
          property.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          property.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
          property.developer.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply developer filter if specified
    if (selectedDeveloper) {
      filtered = filtered.filter(
        (property) => property.developer === selectedDeveloper.name
      );
    }

    // Apply area filters
    filtered = filtered.filter(
      (property) =>
        property.area >= filters.minArea && property.area <= filters.maxArea
    );

    // Apply price filters
    const getPrice = (property: any) => {
      const price = parseInt(property.price.replace(/[^0-9]/g, ""));
      return filters.priceUnit === "sqft" ? property.pricePerSqft : price;
    };

    filtered = filtered.filter((property) => {
      const price = getPrice(property);
      return price >= filters.minPrice && price <= filters.maxPrice;
    });

    // Apply development status filter
    if (filters.developmentStatus.length > 0) {
      filtered = filtered.filter((property) =>
        filters.developmentStatus.includes(property.status)
      );
    }

    // Apply unit type filter
    if (filters.unitTypes.length > 0) {
      filtered = filtered.filter((property) =>
        filters.unitTypes.includes(property.unitType)
      );
    }

    // Apply bedroom filter
    if (filters.bedrooms.length > 0) {
      filtered = filtered.filter((property) =>
        filters.bedrooms.includes(property.bedrooms)
      );
    }

    // Apply sales status filter
    if (filters.salesStatus.length > 0) {
      filtered = filtered.filter((property) =>
        filters.salesStatus.includes(property.salesStatus)
      );
    }

    // Apply completion date filter
    if (filters.completionDate !== "all") {
      // This would need more complex logic based on actual completion dates
      // For now, just return filtered results
    }

    return filtered;
  }, [allProperties, searchQuery, selectedDeveloper, filters]);

  const sortedProperties = useMemo(() => {
    const sorted = [...filteredProperties];

    switch (sortBy) {
      case "price-low":
        return sorted.sort((a, b) => {
          const priceA = parseInt(a.price.replace(/[^0-9]/g, ""));
          const priceB = parseInt(b.price.replace(/[^0-9]/g, ""));
          return priceA - priceB;
        });
      case "price-high":
        return sorted.sort((a, b) => {
          const priceA = parseInt(a.price.replace(/[^0-9]/g, ""));
          const priceB = parseInt(b.price.replace(/[^0-9]/g, ""));
          return priceB - priceA;
        });
      case "completion":
        return sorted.sort((a, b) => a.completion.localeCompare(b.completion));
      case "location":
        return sorted.sort((a, b) => a.location.localeCompare(b.location));
      case "featured":
      default:
        return sorted.sort(
          (a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0)
        );
    }
  }, [filteredProperties, sortBy]);

  const LuxuryCheckboxGroup = ({
    options,
    values,
    onChange,
    label,
    icon: Icon,
  }: {
    options: string[];
    values: string[];
    onChange: (values: string[]) => void;
    label: string;
    icon: any;
  }) => (
    <Card className="border-beige/60 shadow-sm">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center space-x-2 text-soft-brown text-lg">
          <Icon className="w-5 h-5 text-gold" />
          <span>{label}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {options.map((option) => (
          <div key={option} className="flex items-center space-x-3 group">
            <div
              className={`w-5 h-5 rounded-md border-2 flex items-center justify-center cursor-pointer transition-all duration-200 ${
                values.includes(option)
                  ? "bg-gold border-gold text-charcoal shadow-sm"
                  : "border-soft-brown/30 hover:border-gold hover:bg-gold/5 hover:shadow-sm"
              }`}
              onClick={() => onChange(toggleArrayFilter(values, option))}
            >
              {values.includes(option) && <Check className="w-3 h-3" />}
            </div>
            <label
              className="text-sm text-warm-gray cursor-pointer flex-1 group-hover:text-soft-brown transition-colors"
              onClick={() => onChange(toggleArrayFilter(values, option))}
            >
              {option}
            </label>
          </div>
        ))}
      </CardContent>
    </Card>
  );

  const formatPrice = (value: number) => {
    if (filters.priceUnit === "sqft") {
      return `${value.toLocaleString()}`;
    } else {
      if (value >= 1000000) {
        return `${(value / 1000000).toFixed(1)}M`;
      } else if (value >= 1000) {
        return `${(value / 1000).toFixed(0)}K`;
      }
      return value.toLocaleString();
    }
  };

  return (
    <div className="min-h-screen bg-ivory">
      {/* Header */}
      <header className="bg-white border-b border-beige shadow-sm">
        <div className="container py-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onBack}
                  className="text-warm-gray hover:text-gold"
                >
                  <Home className="w-4 h-4 mr-2" />
                  Home
                </Button>
                <ChevronRight className="w-4 h-4 text-warm-gray" />
                <span className="text-soft-brown">Properties</span>
                {selectedDeveloper && (
                  <>
                    <ChevronRight className="w-4 h-4 text-warm-gray" />
                    <span className="text-gold">{selectedDeveloper.name}</span>
                  </>
                )}
              </div>
              <h1 className="text-soft-brown text-[48px] leading-[1.2] py-2">
                {selectedDeveloper
                  ? `${selectedDeveloper.name}`
                  : "All Developments"}
              </h1>
              <p className="text-warm-gray mt-2">
                {sortedProperties.length} properties found
                {hasActiveFilters && ` (${activeFilterCount} filters applied)`}
              </p>
            </div>
          </div>

          {/* Search and Controls */}
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray w-5 h-5" />
                <Input
                  placeholder="Search properties, locations, developers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-white border-beige focus:border-gold rounded-xl"
                />
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {/* Advanced Filters Button */}
              <Dialog
                open={isFilterModalOpen}
                onOpenChange={setIsFilterModalOpen}
              >
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className={`border-soft-brown/30 text-soft-brown hover:bg-soft-brown hover:text-white rounded-xl relative ${
                      hasActiveFilters
                        ? "bg-gold text-charcoal border-gold"
                        : ""
                    }`}
                  >
                    <SlidersHorizontal className="w-4 h-4 mr-2" />
                    Filters
                    {activeFilterCount > 0 && (
                      <Badge className="ml-2 bg-soft-brown text-white text-xs min-w-[20px] h-5">
                        {activeFilterCount}
                      </Badge>
                    )}
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-6xl max-h-[90vh] bg-white flex flex-col overflow-hidden">
                  {/* Fixed Header - No Scroll */}
                  <DialogHeader className="flex-shrink-0 pb-6 border-b border-beige">
                    <div className="flex items-center justify-between">
                      <div>
                        <DialogTitle className="text-2xl text-soft-brown">
                          Advanced Filters
                        </DialogTitle>
                        <DialogDescription className="text-warm-gray mt-2">
                          Refine your property search using the filters below to
                          find properties that match your specific requirements.
                        </DialogDescription>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={resetFilters}
                          disabled={!hasActiveFilters}
                          className={`border-soft-brown/30 transition-all duration-200 ${
                            hasActiveFilters
                              ? "text-gold border-gold/30 hover:bg-gold/10 hover:border-gold"
                              : "text-warm-gray border-beige hover:bg-beige/50"
                          }`}
                        >
                          <RotateCcw className="w-4 h-4 mr-2" />
                          Reset
                        </Button>
                        <Button
                          onClick={() => setIsFilterModalOpen(false)}
                          className="bg-gold hover:bg-gold/90 text-charcoal"
                        >
                          Apply Filters
                        </Button>
                      </div>
                    </div>
                  </DialogHeader>

                  {/* Scrollable Content Area */}
                  <div className="flex-1 overflow-y-auto py-8 space-y-8">
                    {/* Price Configuration Section */}
                    <Card className="border-gold/20 bg-gradient-to-r from-light-gold/10 to-beige/30">
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2 text-soft-brown text-xl">
                          <Banknote className="w-6 h-6 text-gold" />
                          <span>Price Configuration</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        {/* Price Unit Toggle */}
                        <div className="flex items-center justify-between p-4 bg-white rounded-xl border border-beige/50">
                          <div className="flex items-center space-x-3">
                            <DollarSign className="w-5 h-5 text-gold" />
                            <div>
                              <Label className="text-soft-brown font-medium">
                                Price Display Mode
                              </Label>
                              <p className="text-xs text-warm-gray mt-1">
                                Choose how prices are displayed
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <Label
                              className={`text-sm transition-colors ${
                                filters.priceUnit === "total"
                                  ? "text-soft-brown"
                                  : "text-warm-gray"
                              }`}
                            >
                              Total Price
                            </Label>
                            <Switch
                              checked={filters.priceUnit === "sqft"}
                              onCheckedChange={(checked) =>
                                updateFilters(
                                  "priceUnit",
                                  checked ? "sqft" : "total"
                                )
                              }
                              className="data-[state=checked]:bg-gold"
                            />
                            <Label
                              className={`text-sm transition-colors ${
                                filters.priceUnit === "sqft"
                                  ? "text-soft-brown"
                                  : "text-warm-gray"
                              }`}
                            >
                              Per Sq Ft
                            </Label>
                          </div>
                        </div>

                        {/* Price Range */}
                        <div className="space-y-4">
                          <div className="flex items-center space-x-2">
                            <TrendingUp className="w-5 h-5 text-gold" />
                            <Label className="text-soft-brown font-medium text-lg">
                              Price Range (
                              {filters.priceUnit === "sqft"
                                ? "AED per Sq Ft"
                                : "AED"}
                              )
                            </Label>
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label className="text-xs text-warm-gray uppercase tracking-wide">
                                Minimum
                              </Label>
                              <div className="relative">
                                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray text-sm">
                                  AED
                                </span>
                                <Input
                                  type="number"
                                  value={filters.minPrice}
                                  onChange={(e) =>
                                    updateFilters(
                                      "minPrice",
                                      parseInt(e.target.value) || 0
                                    )
                                  }
                                  className="pl-12 border-beige/50 focus:border-gold rounded-lg"
                                  placeholder="0"
                                />
                              </div>
                            </div>
                            <div className="space-y-2">
                              <Label className="text-xs text-warm-gray uppercase tracking-wide">
                                Maximum
                              </Label>
                              <div className="relative">
                                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-gray text-sm">
                                  AED
                                </span>
                                <Input
                                  type="number"
                                  value={filters.maxPrice}
                                  onChange={(e) =>
                                    updateFilters(
                                      "maxPrice",
                                      parseInt(e.target.value) || ********
                                    )
                                  }
                                  className="pl-12 border-beige/50 focus:border-gold rounded-lg"
                                  placeholder="10,000,000"
                                />
                              </div>
                            </div>
                          </div>

                          <div className="space-y-3 p-4 bg-beige/30 rounded-lg">
                            <div className="flex justify-between text-sm text-warm-gray">
                              <span>AED {formatPrice(filters.minPrice)}</span>
                              <span>AED {formatPrice(filters.maxPrice)}</span>
                            </div>
                            <Slider
                              value={[filters.minPrice, filters.maxPrice]}
                              onValueChange={([min, max]) => {
                                updateFilters("minPrice", min);
                                updateFilters("maxPrice", max);
                              }}
                              max={
                                filters.priceUnit === "sqft" ? 5000 : 20000000
                              }
                              min={0}
                              step={filters.priceUnit === "sqft" ? 50 : 50000}
                              className="w-full"
                            />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Area and Timing Section */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Area Range */}
                      <Card className="border-beige/60">
                        <CardHeader>
                          <CardTitle className="flex items-center space-x-2 text-soft-brown text-lg">
                            <Ruler className="w-5 h-5 text-gold" />
                            <span>Area Range</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-2 gap-3">
                            <div className="space-y-2">
                              <Label className="text-xs text-warm-gray uppercase tracking-wide">
                                Min Sq Ft
                              </Label>
                              <Input
                                type="number"
                                value={filters.minArea}
                                onChange={(e) =>
                                  updateFilters(
                                    "minArea",
                                    parseInt(e.target.value) || 0
                                  )
                                }
                                className="border-beige/50 focus:border-gold rounded-lg"
                                placeholder="0"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label className="text-xs text-warm-gray uppercase tracking-wide">
                                Max Sq Ft
                              </Label>
                              <Input
                                type="number"
                                value={filters.maxArea}
                                onChange={(e) =>
                                  updateFilters(
                                    "maxArea",
                                    parseInt(e.target.value) || 5000
                                  )
                                }
                                className="border-beige/50 focus:border-gold rounded-lg"
                                placeholder="5,000"
                              />
                            </div>
                          </div>

                          <div className="space-y-2 p-3 bg-beige/20 rounded-lg">
                            <div className="flex justify-between text-sm text-warm-gray">
                              <span>
                                {filters.minArea.toLocaleString()} sq ft
                              </span>
                              <span>
                                {filters.maxArea.toLocaleString()} sq ft
                              </span>
                            </div>
                            <Slider
                              value={[filters.minArea, filters.maxArea]}
                              onValueChange={([min, max]) => {
                                updateFilters("minArea", min);
                                updateFilters("maxArea", max);
                              }}
                              max={8000}
                              min={0}
                              step={50}
                              className="w-full"
                            />
                          </div>
                        </CardContent>
                      </Card>

                      {/* Project Completion */}
                      <Card className="border-beige/60">
                        <CardHeader>
                          <CardTitle className="flex items-center space-x-2 text-soft-brown text-lg">
                            <Clock className="w-5 h-5 text-gold" />
                            <span>Project Completion</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <Label className="text-xs text-warm-gray uppercase tracking-wide">
                              Completion Timeframe
                            </Label>
                            <Select
                              value={filters.completionDate}
                              onValueChange={(value) =>
                                updateFilters("completionDate", value)
                              }
                            >
                              <SelectTrigger className="w-full border-beige/50 focus:border-gold rounded-lg">
                                <SelectValue placeholder="Select completion timeframe" />
                              </SelectTrigger>
                              <SelectContent>
                                {completionDateOptions.map((option) => (
                                  <SelectItem
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Property Characteristics Section */}
                    <div>
                      <div className="flex items-center space-x-2 mb-6">
                        <HomeIcon className="w-6 h-6 text-gold" />
                        <h3 className="text-xl text-soft-brown">
                          Property Characteristics
                        </h3>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {/* Development Status */}
                        <LuxuryCheckboxGroup
                          label="Development Status"
                          icon={Hammer}
                          options={developmentStatusOptions}
                          values={filters.developmentStatus}
                          onChange={(values) =>
                            updateFilters("developmentStatus", values)
                          }
                        />

                        {/* Unit Types */}
                        <LuxuryCheckboxGroup
                          label="Unit Type"
                          icon={Building2}
                          options={unitTypeOptions}
                          values={filters.unitTypes}
                          onChange={(values) =>
                            updateFilters("unitTypes", values)
                          }
                        />

                        {/* Bedrooms */}
                        <LuxuryCheckboxGroup
                          label="Bedrooms"
                          icon={Bed}
                          options={bedroomOptions}
                          values={filters.bedrooms}
                          onChange={(values) =>
                            updateFilters("bedrooms", values)
                          }
                        />

                        {/* Sales Status */}
                        <LuxuryCheckboxGroup
                          label="Sales Status"
                          icon={ShoppingCart}
                          options={salesStatusOptions}
                          values={filters.salesStatus}
                          onChange={(values) =>
                            updateFilters("salesStatus", values)
                          }
                        />
                      </div>
                    </div>

                    {/* Summary Section */}
                    {hasActiveFilters && (
                      <Card className="border-gold/30 bg-gradient-to-r from-gold/5 to-light-gold/10">
                        <CardContent className="pt-6">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="text-lg text-soft-brown mb-2">
                                Filter Summary
                              </h4>
                              <p className="text-warm-gray">
                                {activeFilterCount} filters applied •{" "}
                                {sortedProperties.length} properties match your
                                criteria
                              </p>
                            </div>
                            <Button
                              variant="outline"
                              onClick={resetFilters}
                              className="border-gold text-gold hover:bg-gold hover:text-charcoal"
                            >
                              <RotateCcw className="w-4 h-4 mr-2" />
                              Reset All
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </DialogContent>
              </Dialog>

              {/* Sort Dropdown */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-40 border-soft-brown/30 rounded-xl">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="featured">Featured</SelectItem>
                  <SelectItem value="price-low">Price: Low to High</SelectItem>
                  <SelectItem value="price-high">Price: High to Low</SelectItem>
                  <SelectItem value="completion">Completion Date</SelectItem>
                  <SelectItem value="location">Location</SelectItem>
                </SelectContent>
              </Select>

              {/* View Mode Toggle */}
              <div className="flex items-center border border-soft-brown/30 rounded-xl overflow-hidden">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className={`rounded-none ${
                    viewMode === "grid"
                      ? "bg-soft-brown text-white"
                      : "text-soft-brown hover:bg-soft-brown/10"
                  }`}
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className={`rounded-none ${
                    viewMode === "list"
                      ? "bg-soft-brown text-white"
                      : "text-soft-brown hover:bg-soft-brown/10"
                  }`}
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Active Filters Display */}
          {hasActiveFilters && (
            <div className="mt-4 flex flex-wrap items-center gap-2">
              <span className="text-sm text-warm-gray mr-2">
                Active filters:
              </span>
              {filters.priceUnit !== "total" && (
                <Badge
                  variant="outline"
                  className="bg-gold/10 text-gold border-gold/30"
                >
                  Price per Sq Ft
                  <X
                    className="w-3 h-3 ml-1 cursor-pointer"
                    onClick={() => updateFilters("priceUnit", "total")}
                  />
                </Badge>
              )}
              {(filters.minArea !== 0 || filters.maxArea !== 5000) && (
                <Badge
                  variant="outline"
                  className="bg-gold/10 text-gold border-gold/30"
                >
                  Area: {filters.minArea}-{filters.maxArea} sq ft
                  <X
                    className="w-3 h-3 ml-1 cursor-pointer"
                    onClick={() => {
                      updateFilters("minArea", 0);
                      updateFilters("maxArea", 5000);
                    }}
                  />
                </Badge>
              )}
              {filters.developmentStatus.map((status) => (
                <Badge
                  key={status}
                  variant="outline"
                  className="bg-gold/10 text-gold border-gold/30"
                >
                  {status}
                  <X
                    className="w-3 h-3 ml-1 cursor-pointer"
                    onClick={() =>
                      updateFilters(
                        "developmentStatus",
                        filters.developmentStatus.filter((s) => s !== status)
                      )
                    }
                  />
                </Badge>
              ))}
              {filters.unitTypes.map((type) => (
                <Badge
                  key={type}
                  variant="outline"
                  className="bg-gold/10 text-gold border-gold/30"
                >
                  {type}
                  <X
                    className="w-3 h-3 ml-1 cursor-pointer"
                    onClick={() =>
                      updateFilters(
                        "unitTypes",
                        filters.unitTypes.filter((t) => t !== type)
                      )
                    }
                  />
                </Badge>
              ))}
              {filters.bedrooms.map((bedroom) => (
                <Badge
                  key={bedroom}
                  variant="outline"
                  className="bg-gold/10 text-gold border-gold/30"
                >
                  {bedroom}
                  <X
                    className="w-3 h-3 ml-1 cursor-pointer"
                    onClick={() =>
                      updateFilters(
                        "bedrooms",
                        filters.bedrooms.filter((b) => b !== bedroom)
                      )
                    }
                  />
                </Badge>
              ))}
              {filters.salesStatus.map((status) => (
                <Badge
                  key={status}
                  variant="outline"
                  className="bg-gold/10 text-gold border-gold/30"
                >
                  {status}
                  <X
                    className="w-3 h-3 ml-1 cursor-pointer"
                    onClick={() =>
                      updateFilters(
                        "salesStatus",
                        filters.salesStatus.filter((s) => s !== status)
                      )
                    }
                  />
                </Badge>
              ))}
              {filters.completionDate !== "all" && (
                <Badge
                  variant="outline"
                  className="bg-gold/10 text-gold border-gold/30"
                >
                  {
                    completionDateOptions.find(
                      (opt) => opt.value === filters.completionDate
                    )?.label
                  }
                  <X
                    className="w-3 h-3 ml-1 cursor-pointer"
                    onClick={() => updateFilters("completionDate", "all")}
                  />
                </Badge>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={resetFilters}
                className="text-gold hover:bg-gold/10 text-xs"
              >
                Clear all
              </Button>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className="container py-8">
        {sortedProperties.length === 0 ? (
          <div className="text-center py-16">
            <Building2 className="w-16 h-16 text-warm-gray mx-auto mb-4" />
            <h3 className="text-xl text-soft-brown mb-2">
              No properties found
            </h3>
            <p className="text-warm-gray mb-6">
              Try adjusting your filters or search criteria to find more
              properties.
            </p>
            <Button
              onClick={resetFilters}
              className="bg-gold hover:bg-gold/90 text-charcoal"
            >
              Reset Filters
            </Button>
          </div>
        ) : (
          <>
            {/* Grid View */}
            {viewMode === "grid" && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {sortedProperties.map((property) => (
                  <Card
                    key={property.id}
                    className="group cursor-pointer border border-beige hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden rounded-xl"
                    onClick={() => onProjectSelect(property)}
                  >
                    <div className="relative aspect-[4/3] overflow-hidden">
                      <ImageWithFallback
                        src={property.image}
                        alt={property.name}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      {property.featured && (
                        <Badge className="absolute top-4 left-4 bg-gold text-charcoal">
                          Featured
                        </Badge>
                      )}
                      <Badge className="absolute top-4 right-4 bg-black/70 text-white border-0">
                        {property.status}
                      </Badge>
                    </div>
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-xl text-soft-brown group-hover:text-gold transition-colors">
                            {property.name}
                          </h3>
                          <div className="flex items-center text-warm-gray mt-1">
                            <MapPin className="w-4 h-4 mr-1" />
                            <span className="text-sm">{property.location}</span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="text-2xl text-gold">
                            {filters.priceUnit === "sqft"
                              ? `AED ${property.pricePerSqft.toLocaleString()}/sq ft`
                              : property.price}
                          </div>
                          <Badge
                            variant="outline"
                            className="text-xs border-gold/30 text-gold"
                          >
                            ROI {property.roi}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-3 gap-4 text-center">
                          <div className="space-y-1">
                            <Bed className="w-4 h-4 mx-auto text-gold" />
                            <div className="text-xs text-warm-gray">
                              {property.bedrooms}
                            </div>
                          </div>
                          <div className="space-y-1">
                            <Bath className="w-4 h-4 mx-auto text-gold" />
                            <div className="text-xs text-warm-gray">
                              {property.bathrooms} Bath
                            </div>
                          </div>
                          <div className="space-y-1">
                            <Square className="w-4 h-4 mx-auto text-gold" />
                            <div className="text-xs text-warm-gray">
                              {property.size}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center justify-between pt-4 border-t border-beige">
                          <div className="text-sm text-warm-gray">
                            <Calendar className="w-4 h-4 inline mr-1" />
                            {property.completion}
                          </div>
                          <div className="text-sm text-warm-gray">
                            {property.paymentPlan}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* List View */}
            {viewMode === "list" && (
              <div className="space-y-6">
                {sortedProperties.map((property) => (
                  <Card
                    key={property.id}
                    className="group cursor-pointer border border-beige hover:shadow-lg transition-all duration-300 overflow-hidden rounded-xl"
                    onClick={() => onProjectSelect(property)}
                  >
                    <CardContent className="p-0">
                      <div className="flex flex-col md:flex-row">
                        <div className="relative w-full md:w-80 aspect-[4/3] md:aspect-auto md:h-48 overflow-hidden">
                          <ImageWithFallback
                            src={property.image}
                            alt={property.name}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                          {property.featured && (
                            <Badge className="absolute top-4 left-4 bg-gold text-charcoal">
                              Featured
                            </Badge>
                          )}
                        </div>
                        <div className="flex-1 p-6">
                          <div className="flex justify-between items-start mb-4">
                            <div>
                              <h3 className="text-xl text-soft-brown group-hover:text-gold transition-colors mb-2">
                                {property.name}
                              </h3>
                              <div className="flex items-center text-warm-gray mb-2">
                                <MapPin className="w-4 h-4 mr-1" />
                                <span>{property.location}</span>
                                <span className="mx-2">•</span>
                                <span>{property.developer}</span>
                              </div>
                              <div className="flex items-center space-x-4 text-sm text-warm-gray">
                                <div className="flex items-center">
                                  <Bed className="w-4 h-4 mr-1 text-gold" />
                                  {property.bedrooms}
                                </div>
                                <div className="flex items-center">
                                  <Bath className="w-4 h-4 mr-1 text-gold" />
                                  {property.bathrooms} Bath
                                </div>
                                <div className="flex items-center">
                                  <Square className="w-4 h-4 mr-1 text-gold" />
                                  {property.size}
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-2xl text-gold mb-2">
                                {filters.priceUnit === "sqft"
                                  ? `AED ${property.pricePerSqft.toLocaleString()}/sq ft`
                                  : property.price}
                              </div>
                              <Badge
                                variant="outline"
                                className="border-gold/30 text-gold"
                              >
                                ROI {property.roi}
                              </Badge>
                            </div>
                          </div>
                          <div className="flex items-center justify-between pt-4 border-t border-beige">
                            <div className="flex items-center space-x-6 text-sm text-warm-gray">
                              <div className="flex items-center">
                                <Calendar className="w-4 h-4 mr-1" />
                                {property.completion}
                              </div>
                              <Badge className="bg-beige text-soft-brown">
                                {property.status}
                              </Badge>
                              <span>{property.paymentPlan}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </>
        )}
      </main>
    </div>
  );
}
