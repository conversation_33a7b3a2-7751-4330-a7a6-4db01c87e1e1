"use client";

import React, { Component, ErrorInfo, ReactNode } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, Bug } from "lucide-react";
import { logError } from "@/lib/error-handler";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  level?: "page" | "section" | "component";
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error
    logError(error, errorInfo);

    // Update state with error info
    this.setState({
      errorInfo,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Send to error monitoring service in production
    if (process.env.NODE_ENV === "production") {
      // Example: Sentry.captureException(error, { extra: errorInfo })
      console.error("Error Boundary caught an error:", error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = "/";
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI based on level
      return this.renderErrorUI();
    }

    return this.props.children;
  }

  private renderErrorUI() {
    const { level = "component", showDetails = false } = this.props;
    const { error, errorInfo, errorId } = this.state;

    // Component-level error (minimal UI)
    if (level === "component") {
      return (
        <div className="flex items-center justify-center p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="text-center">
            <AlertTriangle className="w-8 h-8 text-red-500 mx-auto mb-2" />
            <p className="text-sm text-red-700 mb-2">Something went wrong</p>
            <Button
              onClick={this.handleRetry}
              size="sm"
              variant="outline"
              className="text-red-700 border-red-300 hover:bg-red-100"
            >
              <RefreshCw className="w-4 h-4 mr-1" />
              Try Again
            </Button>
          </div>
        </div>
      );
    }

    // Section-level error (medium UI)
    if (level === "section") {
      return (
        <div className="flex items-center justify-center min-h-[200px] bg-red-50 border border-red-200 rounded-lg">
          <div className="text-center max-w-md px-6">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-800 mb-2">
              Oops! Something went wrong
            </h3>
            <p className="text-red-600 mb-4">
              We encountered an error while loading this section. Please try
              again.
            </p>
            <div className="flex gap-2 justify-center">
              <Button
                onClick={this.handleRetry}
                size="sm"
                variant="outline"
                className="text-red-700 border-red-300 hover:bg-red-100"
              >
                <RefreshCw className="w-4 h-4 mr-1" />
                Try Again
              </Button>
              <Button
                onClick={this.handleReload}
                size="sm"
                variant="outline"
                className="text-red-700 border-red-300 hover:bg-red-100"
              >
                <RefreshCw className="w-4 h-4 mr-1" />
                Reload Page
              </Button>
            </div>
          </div>
        </div>
      );
    }

    // Page-level error (full UI)
    return (
      <div className="min-h-screen bg-ivory flex items-center justify-center px-4">
        <div className="text-center max-w-lg">
          <div className="mb-8">
            <AlertTriangle className="w-20 h-20 text-red-500 mx-auto mb-6" />
            <h1 className="text-3xl font-bold text-soft-brown mb-4">
              Something went wrong
            </h1>
            <p className="text-warm-gray text-lg mb-6">
              We're sorry, but something unexpected happened. Our team has been
              notified and is working to fix the issue.
            </p>

            {showDetails && error && (
              <details className="text-left bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <summary className="cursor-pointer text-red-700 font-medium mb-2">
                  <Bug className="w-4 h-4 inline mr-1" />
                  Error Details
                </summary>
                <div className="text-sm text-red-600 space-y-2">
                  <div>
                    <strong>Error ID:</strong> {errorId}
                  </div>
                  <div>
                    <strong>Message:</strong> {error.message}
                  </div>
                  {process.env.NODE_ENV === "development" && (
                    <>
                      <div>
                        <strong>Stack:</strong>
                        <pre className="mt-1 text-xs bg-red-100 p-2 rounded overflow-auto">
                          {error.stack}
                        </pre>
                      </div>
                      {errorInfo && (
                        <div>
                          <strong>Component Stack:</strong>
                          <pre className="mt-1 text-xs bg-red-100 p-2 rounded overflow-auto">
                            {errorInfo.componentStack}
                          </pre>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </details>
            )}
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={this.handleRetry}
              className="bg-soft-brown hover:bg-deep-brown text-white"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
            <Button
              onClick={this.handleReload}
              variant="outline"
              className="border-soft-brown text-soft-brown hover:bg-soft-brown hover:text-white"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Reload Page
            </Button>
            <Button
              onClick={this.handleGoHome}
              variant="outline"
              className="border-soft-brown text-soft-brown hover:bg-soft-brown hover:text-white"
            >
              <Home className="w-4 h-4 mr-2" />
              Go Home
            </Button>
          </div>

          <div className="mt-8 text-sm text-warm-gray">
            <p>
              If this problem persists, please contact our support team at{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-soft-brown hover:underline"
              >
                <EMAIL>
              </a>
            </p>
            {errorId && (
              <p className="mt-2">
                Reference ID:{" "}
                <code className="bg-gray-100 px-1 rounded">{errorId}</code>
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }
}

// Specialized Error Boundaries
export function PageErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <ErrorBoundary
      level="page"
      showDetails={process.env.NODE_ENV === "development"}
    >
      {children}
    </ErrorBoundary>
  );
}

export function SectionErrorBoundary({ children }: { children: ReactNode }) {
  return <ErrorBoundary level="section">{children}</ErrorBoundary>;
}

export function ComponentErrorBoundary({ children }: { children: ReactNode }) {
  return <ErrorBoundary level="component">{children}</ErrorBoundary>;
}

// HOC for wrapping components with error boundaries
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  level: "page" | "section" | "component" = "component"
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary level={level}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${
    Component.displayName || Component.name
  })`;

  return WrappedComponent;
}
