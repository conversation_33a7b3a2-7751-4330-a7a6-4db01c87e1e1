import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ContactForm } from '../ContactForm'

// Mock the user store
const mockSubmitContactForm = jest.fn()
jest.mock('@/store/useUserStore', () => ({
  useUserStore: () => ({
    submitContactForm: mockSubmitContactForm,
    loading: false,
  }),
}))

// Mock error handler
jest.mock('@/lib/error-handler', () => ({
  showSuccess: jest.fn(),
  showError: jest.fn(),
}))

// Mock security sanitization
jest.mock('@/lib/security', () => ({
  sanitize: {
    text: (input: string) => input,
    email: (input: string) => input.toLowerCase(),
    phone: (input: string) => input,
  },
}))

describe('ContactForm', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render all form fields', () => {
    render(<ContactForm />)

    expect(screen.getByLabelText(/full name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/phone number/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/subject/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/message/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /send message/i })).toBeInTheDocument()
  })

  it('should validate required fields', async () => {
    const user = userEvent.setup()
    render(<ContactForm />)

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/name must be at least 2 characters/i)).toBeInTheDocument()
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument()
      expect(screen.getByText(/please enter a valid phone number/i)).toBeInTheDocument()
      expect(screen.getByText(/message must be at least 10 characters/i)).toBeInTheDocument()
    })

    expect(mockSubmitContactForm).not.toHaveBeenCalled()
  })

  it('should validate email format', async () => {
    const user = userEvent.setup()
    render(<ContactForm />)

    const emailInput = screen.getByLabelText(/email address/i)
    await user.type(emailInput, 'invalid-email')

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument()
    })
  })

  it('should validate phone number format', async () => {
    const user = userEvent.setup()
    render(<ContactForm />)

    const phoneInput = screen.getByLabelText(/phone number/i)
    await user.type(phoneInput, 'invalid-phone')

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid phone number/i)).toBeInTheDocument()
    })
  })

  it('should validate message length', async () => {
    const user = userEvent.setup()
    render(<ContactForm />)

    const messageInput = screen.getByLabelText(/message/i)
    await user.type(messageInput, 'Short')

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/message must be at least 10 characters/i)).toBeInTheDocument()
    })
  })

  it('should submit form with valid data', async () => {
    const user = userEvent.setup()
    mockSubmitContactForm.mockResolvedValue(undefined)

    render(<ContactForm />)

    // Fill in valid form data
    await user.type(screen.getByLabelText(/full name/i), 'John Doe')
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/phone number/i), '+971501234567')
    await user.type(screen.getByLabelText(/subject/i), 'Test Subject')
    await user.type(screen.getByLabelText(/message/i), 'This is a test message that is long enough.')

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockSubmitContactForm).toHaveBeenCalledWith({
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+971501234567',
        subject: 'Test Subject',
        message: 'This is a test message that is long enough.',
        propertyId: '',
        source: 'website',
      })
    })
  })

  it('should show character count for message', async () => {
    const user = userEvent.setup()
    render(<ContactForm />)

    const messageInput = screen.getByLabelText(/message/i)
    await user.type(messageInput, 'Hello')

    expect(screen.getByText('5/1000 characters')).toBeInTheDocument()
  })

  it('should call onSuccess callback after successful submission', async () => {
    const user = userEvent.setup()
    const onSuccess = jest.fn()
    mockSubmitContactForm.mockResolvedValue(undefined)

    render(<ContactForm onSuccess={onSuccess} />)

    // Fill in valid form data
    await user.type(screen.getByLabelText(/full name/i), 'John Doe')
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/phone number/i), '+971501234567')
    await user.type(screen.getByLabelText(/message/i), 'This is a test message that is long enough.')

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(onSuccess).toHaveBeenCalled()
    })
  })

  it('should include propertyId when provided', async () => {
    const user = userEvent.setup()
    mockSubmitContactForm.mockResolvedValue(undefined)

    render(<ContactForm propertyId="property-123" />)

    // Fill in valid form data
    await user.type(screen.getByLabelText(/full name/i), 'John Doe')
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/phone number/i), '+971501234567')
    await user.type(screen.getByLabelText(/message/i), 'This is a test message that is long enough.')

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockSubmitContactForm).toHaveBeenCalledWith(
        expect.objectContaining({
          propertyId: 'property-123',
        })
      )
    })
  })

  it('should disable form during submission', async () => {
    const user = userEvent.setup()
    
    // Mock loading state
    jest.mocked(require('@/store/useUserStore').useUserStore).mockReturnValue({
      submitContactForm: mockSubmitContactForm,
      loading: true,
    })

    render(<ContactForm />)

    expect(screen.getByLabelText(/full name/i)).toBeDisabled()
    expect(screen.getByLabelText(/email address/i)).toBeDisabled()
    expect(screen.getByLabelText(/phone number/i)).toBeDisabled()
    expect(screen.getByLabelText(/message/i)).toBeDisabled()
    expect(screen.getByRole('button', { name: /sending message/i })).toBeDisabled()
  })
})
