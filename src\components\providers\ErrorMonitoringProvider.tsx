'use client'

import { useEffect } from 'react'
import { errorMonitor, setUser, addContext } from '@/lib/error-monitoring'

interface ErrorMonitoringProviderProps {
  children: React.ReactNode
}

export function ErrorMonitoringProvider({ children }: ErrorMonitoringProviderProps) {
  useEffect(() => {
    // Initialize error monitoring
    if (typeof window !== 'undefined') {
      // Add global context
      addContext('app_version', process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0')
      addContext('environment', process.env.NODE_ENV)
      addContext('build_time', process.env.NEXT_PUBLIC_BUILD_TIME || new Date().toISOString())
      
      // Add browser information
      addContext('browser', {
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
      })
      
      // Add screen information
      addContext('screen', {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth,
        pixelDepth: screen.pixelDepth,
      })
      
      // Add viewport information
      addContext('viewport', {
        width: window.innerWidth,
        height: window.innerHeight,
        devicePixelRatio: window.devicePixelRatio,
      })
      
      // Monitor route changes
      const handleRouteChange = () => {
        addContext('current_route', window.location.pathname)
        addContext('current_url', window.location.href)
      }
      
      // Initial route
      handleRouteChange()
      
      // Listen for route changes (for client-side navigation)
      window.addEventListener('popstate', handleRouteChange)
      
      // Monitor performance
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming
            addContext('page_load_performance', {
              loadTime: navEntry.loadEventEnd - navEntry.fetchStart,
              domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.fetchStart,
              firstByte: navEntry.responseStart - navEntry.fetchStart,
            })
          }
        }
      })
      
      try {
        observer.observe({ entryTypes: ['navigation'] })
      } catch (e) {
        // Performance Observer not supported
      }
      
      // Monitor memory usage (if available)
      if ('memory' in performance) {
        const memoryInfo = (performance as any).memory
        addContext('memory', {
          usedJSHeapSize: memoryInfo.usedJSHeapSize,
          totalJSHeapSize: memoryInfo.totalJSHeapSize,
          jsHeapSizeLimit: memoryInfo.jsHeapSizeLimit,
        })
      }
      
      // Monitor connection (if available)
      if ('connection' in navigator) {
        const connection = (navigator as any).connection
        addContext('connection', {
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
          saveData: connection.saveData,
        })
      }
      
      // Cleanup function
      return () => {
        window.removeEventListener('popstate', handleRouteChange)
        observer.disconnect()
      }
    }
  }, [])

  // Set user when authentication state changes
  useEffect(() => {
    // This would typically come from your auth context/store
    // For now, we'll check localStorage for user info
    const checkUser = () => {
      try {
        const userStr = localStorage.getItem('user')
        if (userStr) {
          const user = JSON.parse(userStr)
          if (user.id) {
            setUser(user.id)
            addContext('user', {
              id: user.id,
              email: user.email,
              role: user.role,
            })
          }
        }
      } catch (e) {
        // Ignore parsing errors
      }
    }

    checkUser()

    // Listen for storage changes (user login/logout)
    window.addEventListener('storage', checkUser)
    
    return () => {
      window.removeEventListener('storage', checkUser)
    }
  }, [])

  return <>{children}</>
}
