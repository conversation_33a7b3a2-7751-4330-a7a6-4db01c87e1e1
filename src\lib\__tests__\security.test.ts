import { 
  sanitize, 
  rateLimit, 
  tokenManager, 
  csrf, 
  inputValidation 
} from '../security'

// Mock localStorage for tests
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock sessionStorage for tests
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
})

describe('Sanitization Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('sanitize.text', () => {
    it('should remove dangerous characters', () => {
      const input = '<script>alert("xss")</script>Hello World'
      const result = sanitize.text(input)
      expect(result).toBe('scriptalert("xss")/scriptHello World')
    })

    it('should remove javascript protocols', () => {
      const input = 'javascript:alert("xss")'
      const result = sanitize.text(input)
      expect(result).toBe('alert("xss")')
    })

    it('should trim whitespace', () => {
      const input = '  Hello World  '
      const result = sanitize.text(input)
      expect(result).toBe('Hello World')
    })
  })

  describe('sanitize.email', () => {
    it('should normalize email addresses', () => {
      const input = '<EMAIL>'
      const result = sanitize.email(input)
      expect(result).toBe('<EMAIL>')
    })

    it('should remove invalid characters', () => {
      const input = 'john<script>@example.com'
      const result = sanitize.email(input)
      expect(result).toBe('<EMAIL>')
    })
  })

  describe('sanitize.phone', () => {
    it('should keep only valid phone characters', () => {
      const input = '+971 (50) 123-4567 ext.123'
      const result = sanitize.phone(input)
      expect(result).toBe('+971 (50) 123-4567')
    })

    it('should remove invalid characters', () => {
      const input = '+971abc123def456'
      const result = sanitize.phone(input)
      expect(result).toBe('+971123456')
    })
  })

  describe('sanitize.url', () => {
    it('should validate and return valid URLs', () => {
      const input = 'https://example.com/path'
      const result = sanitize.url(input)
      expect(result).toBe('https://example.com/path')
    })

    it('should reject invalid protocols', () => {
      const input = 'javascript:alert("xss")'
      const result = sanitize.url(input)
      expect(result).toBe('')
    })

    it('should reject malformed URLs', () => {
      const input = 'not-a-url'
      const result = sanitize.url(input)
      expect(result).toBe('')
    })
  })

  describe('sanitize.filename', () => {
    it('should remove dangerous characters from filenames', () => {
      const input = '../../../etc/passwd'
      const result = sanitize.filename(input)
      expect(result).toBe('etcpasswd')
    })

    it('should limit filename length', () => {
      const input = 'a'.repeat(300)
      const result = sanitize.filename(input)
      expect(result.length).toBeLessThanOrEqual(255)
    })
  })
})

describe('Rate Limiting', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear()
    localStorageMock.setItem.mockClear()
    rateLimit.clear('test')
  })

  it('should allow requests within limit', () => {
    localStorageMock.getItem.mockReturnValue(null)
    
    const result = rateLimit.isAllowed('test', 5, 60000)
    expect(result).toBe(true)
  })

  it('should block requests exceeding limit', () => {
    const now = Date.now()
    const requests = Array(5).fill(now)
    localStorageMock.getItem.mockReturnValue(JSON.stringify(requests))
    
    const result = rateLimit.isAllowed('test', 5, 60000)
    expect(result).toBe(false)
  })

  it('should reset after time window', () => {
    const oldTime = Date.now() - 120000 // 2 minutes ago
    const requests = Array(5).fill(oldTime)
    localStorageMock.getItem.mockReturnValue(JSON.stringify(requests))
    
    const result = rateLimit.isAllowed('test', 5, 60000)
    expect(result).toBe(true)
  })
})

describe('CSRF Protection', () => {
  beforeEach(() => {
    sessionStorageMock.getItem.mockClear()
    sessionStorageMock.setItem.mockClear()
  })

  it('should generate CSRF tokens', () => {
    const token = csrf.generateToken()
    expect(token).toBeDefined()
    expect(token.length).toBeGreaterThan(10)
  })

  it('should store and retrieve CSRF tokens', () => {
    const token = 'test-csrf-token'
    csrf.storeToken(token)
    
    expect(sessionStorageMock.setItem).toHaveBeenCalledWith('csrf_token', token)
    
    sessionStorageMock.getItem.mockReturnValue(token)
    const retrieved = csrf.getToken()
    expect(retrieved).toBe(token)
  })

  it('should validate CSRF tokens', () => {
    const token = 'test-csrf-token'
    sessionStorageMock.getItem.mockReturnValue(token)
    
    const isValid = csrf.validateToken(token)
    expect(isValid).toBe(true)
    
    const isInvalid = csrf.validateToken('wrong-token')
    expect(isInvalid).toBe(false)
  })
})

describe('Input Validation', () => {
  describe('hasInjectionPattern', () => {
    it('should detect script injection', () => {
      const malicious = '<script>alert("xss")</script>'
      expect(inputValidation.hasInjectionPattern(malicious)).toBe(true)
    })

    it('should detect javascript protocol', () => {
      const malicious = 'javascript:alert("xss")'
      expect(inputValidation.hasInjectionPattern(malicious)).toBe(true)
    })

    it('should detect event handlers', () => {
      const malicious = 'onload=alert("xss")'
      expect(inputValidation.hasInjectionPattern(malicious)).toBe(true)
    })

    it('should allow safe content', () => {
      const safe = 'Hello, this is safe content!'
      expect(inputValidation.hasInjectionPattern(safe)).toBe(false)
    })
  })

  describe('hasSQLInjection', () => {
    it('should detect SQL injection patterns', () => {
      const malicious = "'; DROP TABLE users; --"
      expect(inputValidation.hasSQLInjection(malicious)).toBe(true)
    })

    it('should detect UNION attacks', () => {
      const malicious = 'UNION SELECT * FROM users'
      expect(inputValidation.hasSQLInjection(malicious)).toBe(true)
    })

    it('should detect boolean-based attacks', () => {
      const malicious = "' OR 1=1 --"
      expect(inputValidation.hasSQLInjection(malicious)).toBe(true)
    })

    it('should allow safe content', () => {
      const safe = 'This is a normal search query'
      expect(inputValidation.hasSQLInjection(safe)).toBe(false)
    })
  })

  describe('validateFile', () => {
    it('should validate allowed file types', () => {
      const file = new File(['content'], 'test.jpg', { type: 'image/jpeg' })
      const result = inputValidation.validateFile(file)
      expect(result.valid).toBe(true)
    })

    it('should reject files that are too large', () => {
      const largeContent = new Array(6 * 1024 * 1024).fill('a').join('') // 6MB
      const file = new File([largeContent], 'large.jpg', { type: 'image/jpeg' })
      const result = inputValidation.validateFile(file)
      expect(result.valid).toBe(false)
      expect(result.error).toContain('size')
    })

    it('should reject disallowed file types', () => {
      const file = new File(['content'], 'test.exe', { type: 'application/x-executable' })
      const result = inputValidation.validateFile(file)
      expect(result.valid).toBe(false)
      expect(result.error).toContain('type')
    })

    it('should reject files with dangerous extensions', () => {
      const file = new File(['content'], 'test.php', { type: 'image/jpeg' })
      const result = inputValidation.validateFile(file)
      expect(result.valid).toBe(false)
      expect(result.error).toContain('extension')
    })
  })
})
