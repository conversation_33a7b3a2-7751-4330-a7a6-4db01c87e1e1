import { 
  contactFormSchema, 
  registerSchema, 
  loginSchema,
  validateField,
  getFieldErrors,
  customValidators
} from '../validation'
import { z } from 'zod'

describe('Validation Schemas', () => {
  describe('contactFormSchema', () => {
    it('should validate a valid contact form', () => {
      const validData = {
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+971501234567',
        message: 'This is a test message that is long enough to pass validation.',
      }

      const result = contactFormSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('should reject invalid email', () => {
      const invalidData = {
        name: '<PERSON>',
        email: 'invalid-email',
        phone: '+971501234567',
        message: 'This is a test message.',
      }

      const result = contactFormSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].path).toContain('email')
      }
    })

    it('should reject short message', () => {
      const invalidData = {
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+971501234567',
        message: 'Short',
      }

      const result = contactFormSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].path).toContain('message')
      }
    })

    it('should reject invalid name with numbers', () => {
      const invalidData = {
        name: 'John123',
        email: '<EMAIL>',
        phone: '+971501234567',
        message: 'This is a valid message.',
      }

      const result = contactFormSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })
  })

  describe('registerSchema', () => {
    it('should validate a valid registration form', () => {
      const validData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+971501234567',
        password: 'StrongPass123!',
        confirmPassword: 'StrongPass123!',
        agreeToTerms: true,
      }

      const result = registerSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('should reject mismatched passwords', () => {
      const invalidData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'StrongPass123!',
        confirmPassword: 'DifferentPass123!',
        agreeToTerms: true,
      }

      const result = registerSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].path).toContain('confirmPassword')
      }
    })

    it('should reject weak password', () => {
      const invalidData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'weak',
        confirmPassword: 'weak',
        agreeToTerms: true,
      }

      const result = registerSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })

    it('should reject when terms not agreed', () => {
      const invalidData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'StrongPass123!',
        confirmPassword: 'StrongPass123!',
        agreeToTerms: false,
      }

      const result = registerSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })
  })

  describe('loginSchema', () => {
    it('should validate a valid login form', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'password123',
      }

      const result = loginSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('should reject invalid email', () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'password123',
      }

      const result = loginSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })

    it('should reject empty password', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: '',
      }

      const result = loginSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })
  })
})

describe('Validation Helpers', () => {
  describe('validateField', () => {
    const testSchema = z.string().min(5, 'Must be at least 5 characters')

    it('should return success for valid input', () => {
      const result = validateField(testSchema, 'valid input')
      expect(result.success).toBe(true)
      expect(result.data).toBe('valid input')
    })

    it('should return error for invalid input', () => {
      const result = validateField(testSchema, 'hi')
      expect(result.success).toBe(false)
      expect(result.error).toBe('Must be at least 5 characters')
    })
  })

  describe('getFieldErrors', () => {
    it('should extract field errors from ZodError', () => {
      const schema = z.object({
        name: z.string().min(2),
        email: z.string().email(),
      })

      const result = schema.safeParse({ name: 'a', email: 'invalid' })
      if (!result.success) {
        const fieldErrors = getFieldErrors(result.error)
        expect(fieldErrors).toHaveProperty('name')
        expect(fieldErrors).toHaveProperty('email')
      }
    })
  })
})

describe('Custom Validators', () => {
  describe('uaePhone', () => {
    it('should validate UAE phone numbers', () => {
      expect(customValidators.uaePhone('+971501234567')).toBe(true)
      expect(customValidators.uaePhone('971501234567')).toBe(true)
      expect(customValidators.uaePhone('0501234567')).toBe(true)
      expect(customValidators.uaePhone('501234567')).toBe(true)
    })

    it('should reject invalid UAE phone numbers', () => {
      expect(customValidators.uaePhone('123456')).toBe(false)
      expect(customValidators.uaePhone('+1234567890')).toBe(false)
      expect(customValidators.uaePhone('invalid')).toBe(false)
    })
  })

  describe('strongPassword', () => {
    it('should validate strong passwords', () => {
      expect(customValidators.strongPassword('StrongPass123!')).toBe(true)
      expect(customValidators.strongPassword('MyP@ssw0rd')).toBe(true)
    })

    it('should reject weak passwords', () => {
      expect(customValidators.strongPassword('password')).toBe(false)
      expect(customValidators.strongPassword('PASSWORD')).toBe(false)
      expect(customValidators.strongPassword('12345678')).toBe(false)
      expect(customValidators.strongPassword('Pass123')).toBe(false) // too short
    })
  })

  describe('validUrl', () => {
    it('should validate URLs', () => {
      expect(customValidators.validUrl('https://example.com')).toBe(true)
      expect(customValidators.validUrl('http://example.com')).toBe(true)
      expect(customValidators.validUrl('https://sub.example.com/path')).toBe(true)
    })

    it('should reject invalid URLs', () => {
      expect(customValidators.validUrl('not-a-url')).toBe(false)
      expect(customValidators.validUrl('ftp://example.com')).toBe(false)
      expect(customValidators.validUrl('')).toBe(false)
    })
  })
})
