import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from "axios";
import { config } from "./config";

// Types for API responses
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: any;
}

// Create axios instance
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: config.app.url, // Use our app URL for internal API calls
    timeout: config.api.timeout,
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
  });

  // Request interceptor
  client.interceptors.request.use(
    (config) => {
      // Add auth token if available
      const token =
        typeof window !== "undefined"
          ? localStorage.getItem("auth_token")
          : null;
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // Add request timestamp for debugging
      if (process.env.NODE_ENV === "development") {
        console.log(
          `🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`,
          {
            params: config.params,
            data: config.data,
          }
        );
      }

      return config;
    },
    (error) => {
      console.error("❌ Request Error:", error);
      return Promise.reject(error);
    }
  );

  // Response interceptor
  client.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      // Log successful responses in development
      if (process.env.NODE_ENV === "development") {
        console.log(
          `✅ API Response: ${response.config.method?.toUpperCase()} ${
            response.config.url
          }`,
          {
            status: response.status,
            data: response.data,
          }
        );
      }

      return response;
    },
    (error: AxiosError<ApiError>) => {
      // Handle different error types
      if (error.response) {
        // Server responded with error status
        const { status, data } = error.response;

        console.error(
          `❌ API Error ${status}:`,
          data?.message || error.message
        );

        // Handle specific status codes
        switch (status) {
          case 401:
            // Unauthorized - clear auth and redirect to login
            if (typeof window !== "undefined") {
              localStorage.removeItem("auth_token");
              window.location.href = "/login";
            }
            break;
          case 403:
            // Forbidden
            console.error("Access forbidden");
            break;
          case 404:
            // Not found
            console.error("Resource not found");
            break;
          case 422:
            // Validation error
            console.error("Validation error:", data?.details);
            break;
          case 500:
            // Server error
            console.error("Internal server error");
            break;
        }

        return Promise.reject({
          message: data?.message || "An error occurred",
          code: data?.code,
          status,
          details: data?.details,
        } as ApiError);
      } else if (error.request) {
        // Network error
        console.error("❌ Network Error:", error.message);
        return Promise.reject({
          message: "Network error. Please check your connection.",
          code: "NETWORK_ERROR",
        } as ApiError);
      } else {
        // Other error
        console.error("❌ Error:", error.message);
        return Promise.reject({
          message: error.message || "An unexpected error occurred",
          code: "UNKNOWN_ERROR",
        } as ApiError);
      }
    }
  );

  return client;
};

// Create the API client instance
export const apiClient = createApiClient();

// Helper functions for common HTTP methods
export const api = {
  // GET request
  get: async <T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    const response = await apiClient.get<ApiResponse<T>>(url, config);
    return response.data;
  },

  // POST request
  post: async <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    const response = await apiClient.post<ApiResponse<T>>(url, data, config);
    return response.data;
  },

  // PUT request
  put: async <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    const response = await apiClient.put<ApiResponse<T>>(url, data, config);
    return response.data;
  },

  // PATCH request
  patch: async <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    const response = await apiClient.patch<ApiResponse<T>>(url, data, config);
    return response.data;
  },

  // DELETE request
  delete: async <T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    const response = await apiClient.delete<ApiResponse<T>>(url, config);
    return response.data;
  },
};

// Utility functions
export const apiUtils = {
  // Build query string from object
  buildQueryString: (params: Record<string, any>): string => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        searchParams.append(key, String(value));
      }
    });
    return searchParams.toString();
  },

  // Handle file upload
  uploadFile: async (file: File, endpoint: string): Promise<ApiResponse> => {
    const formData = new FormData();
    formData.append("file", file);

    return api.post(endpoint, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  // Download file
  downloadFile: async (url: string, filename?: string): Promise<void> => {
    const response = await apiClient.get(url, {
      responseType: "blob",
    });

    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = downloadUrl;
    link.download = filename || "download";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  },
};

export default api;
