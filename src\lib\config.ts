// Environment configuration and constants
export const config = {
  // API Configuration
  api: {
    baseUrl:
      process.env.NEXT_PUBLIC_API_BASE_URL || "https://api.smartoffplan.ae",
    version: process.env.NEXT_PUBLIC_API_VERSION || "v1",
    timeout: 30000, // 30 seconds
  },

  // App Configuration
  app: {
    name: "Smart Off Plan",
    url: process.env.NEXTAUTH_URL || "http://localhost:3000",
    environment: process.env.NEXT_PUBLIC_APP_ENV || "development",
  },

  // External Services
  services: {
    googleMaps: {
      apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || "",
    },
    analytics: {
      googleAnalyticsId: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID || "",
    },
    cloudinary: {
      cloudName: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || "",
    },
  },

  // Feature Flags
  features: {
    analytics: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === "true",
    chat: process.env.NEXT_PUBLIC_ENABLE_CHAT === "true",
    notifications: process.env.NEXT_PUBLIC_ENABLE_NOTIFICATIONS === "true",
  },

  // Development
  isDevelopment: process.env.NODE_ENV === "development",
  isProduction: process.env.NODE_ENV === "production",
  isTest: process.env.NODE_ENV === "test",
};

// API Endpoints
export const endpoints = {
  // Properties
  properties: {
    list: "/api/properties",
    detail: (id: string) => `/api/properties/${id}`,
    search: "/api/properties",
    featured: "/api/properties?featured=true",
  },

  // Developers
  developers: {
    list: "/developers",
    detail: (id: string) => `/developers/${id}`,
    properties: (id: string) => `/developers/${id}/properties`,
  },

  // Areas
  areas: {
    list: "/areas",
    detail: (id: string) => `/areas/${id}`,
    properties: (id: string) => `/areas/${id}/properties`,
  },

  // Contact
  contact: {
    submit: "/contact",
    newsletter: "/newsletter/subscribe",
  },

  // User
  user: {
    profile: "/user/profile",
    favorites: "/user/favorites",
    inquiries: "/user/inquiries",
  },

  // Authentication
  auth: {
    login: "/auth/login",
    register: "/auth/register",
    logout: "/auth/logout",
    refresh: "/auth/refresh",
  },
};

// Validation
export const validation = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  password: {
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  },
};

// Constants
export const constants = {
  // Pagination
  defaultPageSize: 12,
  maxPageSize: 50,

  // File Upload
  maxFileSize: 5 * 1024 * 1024, // 5MB
  allowedImageTypes: ["image/jpeg", "image/png", "image/webp"],
  allowedDocumentTypes: ["application/pdf", "application/msword"],

  // Cache
  cacheKeys: {
    properties: "properties",
    developers: "developers",
    areas: "areas",
    user: "user",
  },

  // Timeouts
  debounceDelay: 300,
  toastDuration: 5000,
};
