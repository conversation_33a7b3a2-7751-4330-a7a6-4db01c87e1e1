import toast from "react-hot-toast";
import { captureError } from "./error-monitoring";
import { ApiError } from "./api-client";

// Error types
export enum ErrorType {
  NETWORK = "NETWORK",
  VALIDATION = "VALIDATION",
  AUTHENTICATION = "AUTHENTICATION",
  AUTHORIZATION = "AUTHORIZATION",
  NOT_FOUND = "NOT_FOUND",
  SERVER = "SERVER",
  UNKNOWN = "UNKNOWN",
}

export interface AppError {
  type: ErrorType;
  message: string;
  code?: string;
  details?: any;
  timestamp: Date;
}

// Error classification
export const classifyError = (error: ApiError | Error | any): AppError => {
  const timestamp = new Date();

  // Handle API errors
  if (error && typeof error === "object" && "status" in error) {
    const apiError = error as ApiError;

    switch (apiError.status) {
      case 400:
        return {
          type: ErrorType.VALIDATION,
          message: apiError.message || "Invalid request data",
          code: apiError.code,
          details: apiError.details,
          timestamp,
        };
      case 401:
        return {
          type: ErrorType.AUTHENTICATION,
          message: "Please log in to continue",
          code: apiError.code,
          timestamp,
        };
      case 403:
        return {
          type: ErrorType.AUTHORIZATION,
          message: "You do not have permission to perform this action",
          code: apiError.code,
          timestamp,
        };
      case 404:
        return {
          type: ErrorType.NOT_FOUND,
          message: "The requested resource was not found",
          code: apiError.code,
          timestamp,
        };
      case 422:
        return {
          type: ErrorType.VALIDATION,
          message: apiError.message || "Validation failed",
          code: apiError.code,
          details: apiError.details,
          timestamp,
        };
      case 500:
      case 502:
      case 503:
      case 504:
        return {
          type: ErrorType.SERVER,
          message: "Server error. Please try again later.",
          code: apiError.code,
          timestamp,
        };
      default:
        return {
          type: ErrorType.UNKNOWN,
          message: apiError.message || "An unexpected error occurred",
          code: apiError.code,
          timestamp,
        };
    }
  }

  // Handle network errors
  if (error && error.code === "NETWORK_ERROR") {
    return {
      type: ErrorType.NETWORK,
      message: "Network error. Please check your connection.",
      code: "NETWORK_ERROR",
      timestamp,
    };
  }

  // Handle generic errors
  if (error instanceof Error) {
    return {
      type: ErrorType.UNKNOWN,
      message: error.message || "An unexpected error occurred",
      timestamp,
    };
  }

  // Fallback
  return {
    type: ErrorType.UNKNOWN,
    message: "An unexpected error occurred",
    timestamp,
  };
};

// Error display functions
export const showError = (
  error: ApiError | Error | any,
  customMessage?: string
) => {
  const appError = classifyError(error);
  const message = customMessage || appError.message;

  // Log error in development
  if (process.env.NODE_ENV === "development") {
    console.error("🚨 Error:", appError);
  }

  // Send to error monitoring system
  captureError(error, { customMessage, appError }, "error");

  // Show toast notification
  switch (appError.type) {
    case ErrorType.VALIDATION:
      toast.error(message, {
        duration: 6000,
        icon: "⚠️",
      });
      break;
    case ErrorType.AUTHENTICATION:
      toast.error(message, {
        duration: 8000,
        icon: "🔐",
      });
      break;
    case ErrorType.AUTHORIZATION:
      toast.error(message, {
        duration: 6000,
        icon: "🚫",
      });
      break;
    case ErrorType.NOT_FOUND:
      toast.error(message, {
        duration: 5000,
        icon: "🔍",
      });
      break;
    case ErrorType.NETWORK:
      toast.error(message, {
        duration: 8000,
        icon: "📡",
      });
      break;
    case ErrorType.SERVER:
      toast.error(message, {
        duration: 8000,
        icon: "🔧",
      });
      break;
    default:
      toast.error(message, {
        duration: 5000,
        icon: "❌",
      });
  }

  return appError;
};

// Success notifications
export const showSuccess = (message: string, duration = 4000) => {
  toast.success(message, {
    duration,
    icon: "✅",
  });
};

// Info notifications
export const showInfo = (message: string, duration = 4000) => {
  toast(message, {
    duration,
    icon: "ℹ️",
  });
};

// Loading notifications
export const showLoading = (message: string = "Loading...") => {
  return toast.loading(message, {
    icon: "⏳",
  });
};

// Dismiss specific toast
export const dismissToast = (toastId: string) => {
  toast.dismiss(toastId);
};

// Dismiss all toasts
export const dismissAllToasts = () => {
  toast.dismiss();
};

// Error boundary helper
export const logError = (error: Error, errorInfo?: any) => {
  console.error("🚨 Error Boundary:", error, errorInfo);

  // Send to error monitoring system
  captureError(error, errorInfo, "error");
};

// Validation error helpers
export const formatValidationErrors = (
  errors: Record<string, string[]>
): string => {
  const messages = Object.entries(errors).map(([field, fieldErrors]) => {
    const fieldName = field.charAt(0).toUpperCase() + field.slice(1);
    return `${fieldName}: ${fieldErrors.join(", ")}`;
  });

  return messages.join("\n");
};

// Retry helper
export const withRetry = async <T>(
  fn: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> => {
  let lastError: any;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;

      if (i === maxRetries) {
        throw error;
      }

      // Wait before retrying
      await new Promise((resolve) =>
        setTimeout(resolve, delay * Math.pow(2, i))
      );
    }
  }

  throw lastError;
};
