// Error monitoring and tracking system

interface ErrorEvent {
  id: string
  timestamp: string
  message: string
  stack?: string
  url: string
  userAgent: string
  userId?: string
  sessionId: string
  level: 'error' | 'warning' | 'info'
  context?: Record<string, any>
  tags?: string[]
  fingerprint?: string
}

interface PerformanceMetric {
  id: string
  timestamp: string
  name: string
  value: number
  unit: string
  context?: Record<string, any>
}

interface UserAction {
  id: string
  timestamp: string
  action: string
  element?: string
  page: string
  userId?: string
  sessionId: string
  context?: Record<string, any>
}

class ErrorMonitor {
  private sessionId: string
  private userId?: string
  private isEnabled: boolean
  private apiEndpoint: string
  private maxRetries: number = 3
  private retryDelay: number = 1000
  private queue: ErrorEvent[] = []
  private isOnline: boolean = true

  constructor() {
    this.sessionId = this.generateSessionId()
    this.isEnabled = process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_ENABLE_ERROR_MONITORING === 'true'
    this.apiEndpoint = process.env.NEXT_PUBLIC_ERROR_MONITORING_ENDPOINT || '/api/errors'
    
    if (typeof window !== 'undefined') {
      this.setupGlobalErrorHandlers()
      this.setupNetworkMonitoring()
      this.setupPerformanceMonitoring()
      this.startQueueProcessor()
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private setupGlobalErrorHandlers(): void {
    // Handle JavaScript errors
    window.addEventListener('error', (event) => {
      this.captureError(event.error || new Error(event.message), {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        type: 'javascript',
      })
    })

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError(new Error(event.reason), {
        type: 'unhandled_promise_rejection',
        reason: event.reason,
      })
    })

    // Handle React errors (if using React Error Boundary)
    const originalConsoleError = console.error
    console.error = (...args) => {
      if (args[0] && typeof args[0] === 'string' && args[0].includes('React')) {
        this.captureError(new Error(args.join(' ')), {
          type: 'react_error',
          args,
        })
      }
      originalConsoleError.apply(console, args)
    }
  }

  private setupNetworkMonitoring(): void {
    // Monitor network status
    window.addEventListener('online', () => {
      this.isOnline = true
      this.processQueue()
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
    })

    // Monitor fetch requests
    const originalFetch = window.fetch
    window.fetch = async (...args) => {
      const startTime = performance.now()
      
      try {
        const response = await originalFetch(...args)
        
        const endTime = performance.now()
        const duration = endTime - startTime

        // Log slow requests
        if (duration > 5000) {
          this.capturePerformanceMetric('slow_request', duration, 'ms', {
            url: args[0],
            status: response.status,
          })
        }

        // Log failed requests
        if (!response.ok) {
          this.captureError(new Error(`HTTP ${response.status}: ${response.statusText}`), {
            type: 'http_error',
            url: args[0],
            status: response.status,
            statusText: response.statusText,
          })
        }

        return response
      } catch (error) {
        this.captureError(error as Error, {
          type: 'network_error',
          url: args[0],
        })
        throw error
      }
    }
  }

  private setupPerformanceMonitoring(): void {
    // Monitor page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        
        if (navigation) {
          this.capturePerformanceMetric('page_load_time', navigation.loadEventEnd - navigation.fetchStart, 'ms')
          this.capturePerformanceMetric('dom_content_loaded', navigation.domContentLoadedEventEnd - navigation.fetchStart, 'ms')
          this.capturePerformanceMetric('first_byte', navigation.responseStart - navigation.fetchStart, 'ms')
        }

        // Monitor Core Web Vitals
        this.monitorCoreWebVitals()
      }, 0)
    })
  }

  private monitorCoreWebVitals(): void {
    // This would typically use the web-vitals library
    // For now, we'll implement basic monitoring
    
    // Monitor Largest Contentful Paint (LCP)
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      
      if (lastEntry) {
        this.capturePerformanceMetric('lcp', lastEntry.startTime, 'ms')
      }
    })

    try {
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
    } catch (e) {
      // LCP not supported
    }

    // Monitor Cumulative Layout Shift (CLS)
    let clsValue = 0
    const clsObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value
        }
      }
    })

    try {
      clsObserver.observe({ entryTypes: ['layout-shift'] })
      
      // Report CLS on page unload
      window.addEventListener('beforeunload', () => {
        this.capturePerformanceMetric('cls', clsValue, 'score')
      })
    } catch (e) {
      // CLS not supported
    }
  }

  private startQueueProcessor(): void {
    setInterval(() => {
      if (this.isOnline && this.queue.length > 0) {
        this.processQueue()
      }
    }, 5000) // Process queue every 5 seconds
  }

  private async processQueue(): Promise<void> {
    if (this.queue.length === 0) return

    const batch = this.queue.splice(0, 10) // Process up to 10 errors at a time
    
    try {
      await this.sendErrorBatch(batch)
    } catch (error) {
      // Put errors back in queue for retry
      this.queue.unshift(...batch)
      console.warn('Failed to send error batch:', error)
    }
  }

  private async sendErrorBatch(errors: ErrorEvent[]): Promise<void> {
    if (!this.isEnabled) return

    const payload = {
      errors,
      sessionId: this.sessionId,
      userId: this.userId,
      timestamp: new Date().toISOString(),
    }

    const response = await fetch(this.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    })

    if (!response.ok) {
      throw new Error(`Failed to send errors: ${response.status}`)
    }
  }

  public captureError(error: Error, context?: Record<string, any>, level: 'error' | 'warning' | 'info' = 'error'): void {
    if (!this.isEnabled) {
      console.error('Error captured:', error, context)
      return
    }

    const errorEvent: ErrorEvent = {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      message: error.message,
      stack: error.stack,
      url: window.location.href,
      userAgent: navigator.userAgent,
      userId: this.userId,
      sessionId: this.sessionId,
      level,
      context,
      fingerprint: this.generateFingerprint(error),
    }

    this.queue.push(errorEvent)

    // Send immediately for critical errors
    if (level === 'error' && this.isOnline) {
      this.sendErrorBatch([errorEvent]).catch(() => {
        // Error will remain in queue for retry
      })
    }
  }

  public capturePerformanceMetric(name: string, value: number, unit: string, context?: Record<string, any>): void {
    if (!this.isEnabled) return

    const metric: PerformanceMetric = {
      id: `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      name,
      value,
      unit,
      context,
    }

    // Send performance metrics to a separate endpoint
    fetch('/api/metrics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(metric),
    }).catch(() => {
      // Ignore metric sending failures
    })
  }

  public captureUserAction(action: string, element?: string, context?: Record<string, any>): void {
    if (!this.isEnabled) return

    const userAction: UserAction = {
      id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      action,
      element,
      page: window.location.pathname,
      userId: this.userId,
      sessionId: this.sessionId,
      context,
    }

    // Send user actions to analytics endpoint
    fetch('/api/analytics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userAction),
    }).catch(() => {
      // Ignore analytics sending failures
    })
  }

  public setUser(userId: string): void {
    this.userId = userId
  }

  public addContext(key: string, value: any): void {
    // Add global context that will be included with all errors
    if (typeof window !== 'undefined') {
      (window as any).__errorMonitorContext = {
        ...(window as any).__errorMonitorContext,
        [key]: value,
      }
    }
  }

  private generateFingerprint(error: Error): string {
    // Generate a fingerprint for grouping similar errors
    const message = error.message || 'Unknown error'
    const stack = error.stack || ''
    const firstStackLine = stack.split('\n')[1] || ''
    
    return btoa(`${message}:${firstStackLine}`).substr(0, 16)
  }

  public getSessionId(): string {
    return this.sessionId
  }

  public isMonitoringEnabled(): boolean {
    return this.isEnabled
  }
}

// Create global instance
export const errorMonitor = new ErrorMonitor()

// Convenience functions
export const captureError = (error: Error, context?: Record<string, any>, level?: 'error' | 'warning' | 'info') => {
  errorMonitor.captureError(error, context, level)
}

export const capturePerformanceMetric = (name: string, value: number, unit: string, context?: Record<string, any>) => {
  errorMonitor.capturePerformanceMetric(name, value, unit, context)
}

export const captureUserAction = (action: string, element?: string, context?: Record<string, any>) => {
  errorMonitor.captureUserAction(action, element, context)
}

export const setUser = (userId: string) => {
  errorMonitor.setUser(userId)
}

export const addContext = (key: string, value: any) => {
  errorMonitor.addContext(key, value)
}
