import DOMPurify from 'dompurify'

// Input sanitization
export const sanitize = {
  // Sanitize HTML content
  html: (input: string): string => {
    if (typeof window === 'undefined') {
      // Server-side: basic sanitization
      return input
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
    }
    
    // Client-side: use DOMPurify
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'a'],
      ALLOWED_ATTR: ['href', 'target'],
      ALLOW_DATA_ATTR: false,
    })
  },

  // Sanitize plain text
  text: (input: string): string => {
    return input
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/data:/gi, '') // Remove data: protocol
      .trim()
  },

  // Sanitize email
  email: (input: string): string => {
    return input
      .toLowerCase()
      .replace(/[^a-z0-9@._-]/g, '')
      .trim()
  },

  // Sanitize phone number
  phone: (input: string): string => {
    return input
      .replace(/[^0-9+\-\s()]/g, '')
      .trim()
  },

  // Sanitize URL
  url: (input: string): string => {
    try {
      const url = new URL(input)
      // Only allow http and https protocols
      if (!['http:', 'https:'].includes(url.protocol)) {
        return ''
      }
      return url.toString()
    } catch {
      return ''
    }
  },

  // Sanitize filename
  filename: (input: string): string => {
    return input
      .replace(/[^a-zA-Z0-9._-]/g, '')
      .replace(/\.{2,}/g, '.') // Remove multiple dots
      .substring(0, 255) // Limit length
  },
}

// Content Security Policy helpers
export const csp = {
  // Generate nonce for inline scripts
  generateNonce: (): string => {
    if (typeof window !== 'undefined' && window.crypto) {
      const array = new Uint8Array(16)
      window.crypto.getRandomValues(array)
      return btoa(String.fromCharCode(...array))
    }
    // Fallback for server-side
    return Math.random().toString(36).substring(2, 15)
  },

  // CSP header value
  getHeaderValue: (nonce?: string): string => {
    const directives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-eval' https://www.google.com https://www.gstatic.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https: blob:",
      "connect-src 'self' https://api.smartoffplan.ae",
      "frame-src 'self' https://www.google.com",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
    ]

    if (nonce) {
      directives[1] += ` 'nonce-${nonce}'`
    }

    return directives.join('; ')
  },
}

// Rate limiting helpers
export const rateLimit = {
  // Simple client-side rate limiting
  isAllowed: (key: string, maxRequests: number, windowMs: number): boolean => {
    if (typeof window === 'undefined') return true

    const now = Date.now()
    const windowStart = now - windowMs
    const storageKey = `rate_limit_${key}`
    
    try {
      const stored = localStorage.getItem(storageKey)
      const requests = stored ? JSON.parse(stored) : []
      
      // Filter out old requests
      const recentRequests = requests.filter((timestamp: number) => timestamp > windowStart)
      
      if (recentRequests.length >= maxRequests) {
        return false
      }
      
      // Add current request
      recentRequests.push(now)
      localStorage.setItem(storageKey, JSON.stringify(recentRequests))
      
      return true
    } catch {
      return true // Allow on error
    }
  },

  // Clear rate limit data
  clear: (key: string): void => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(`rate_limit_${key}`)
    }
  },
}

// Token management
export const tokenManager = {
  // Store token securely
  store: (token: string): void => {
    if (typeof window !== 'undefined') {
      // Use httpOnly cookie in production
      if (process.env.NODE_ENV === 'production') {
        // This would be handled by the server
        document.cookie = `auth_token=${token}; Secure; HttpOnly; SameSite=Strict; Max-Age=86400`
      } else {
        localStorage.setItem('auth_token', token)
      }
    }
  },

  // Get token
  get: (): string | null => {
    if (typeof window !== 'undefined') {
      if (process.env.NODE_ENV === 'production') {
        // Extract from httpOnly cookie (would need server-side help)
        return null
      } else {
        return localStorage.getItem('auth_token')
      }
    }
    return null
  },

  // Remove token
  remove: (): void => {
    if (typeof window !== 'undefined') {
      if (process.env.NODE_ENV === 'production') {
        document.cookie = 'auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
      } else {
        localStorage.removeItem('auth_token')
      }
    }
  },

  // Check if token is expired
  isExpired: (token: string): boolean => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return payload.exp * 1000 < Date.now()
    } catch {
      return true
    }
  },
}

// CSRF protection
export const csrf = {
  // Generate CSRF token
  generateToken: (): string => {
    if (typeof window !== 'undefined' && window.crypto) {
      const array = new Uint8Array(32)
      window.crypto.getRandomValues(array)
      return btoa(String.fromCharCode(...array))
    }
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
  },

  // Store CSRF token
  storeToken: (token: string): void => {
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('csrf_token', token)
    }
  },

  // Get CSRF token
  getToken: (): string | null => {
    if (typeof window !== 'undefined') {
      return sessionStorage.getItem('csrf_token')
    }
    return null
  },

  // Validate CSRF token
  validateToken: (token: string): boolean => {
    const storedToken = csrf.getToken()
    return storedToken === token
  },
}

// Security headers for API requests
export const securityHeaders = {
  // Get common security headers
  getHeaders: (): Record<string, string> => {
    const headers: Record<string, string> = {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
    }

    // Add CSRF token if available
    const csrfToken = csrf.getToken()
    if (csrfToken) {
      headers['X-CSRF-Token'] = csrfToken
    }

    return headers
  },
}

// Input validation helpers
export const inputValidation = {
  // Check for common injection patterns
  hasInjectionPattern: (input: string): boolean => {
    const patterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /data:/i,
      /vbscript:/i,
      /<iframe/i,
      /<object/i,
      /<embed/i,
      /expression\s*\(/i,
      /url\s*\(/i,
    ]

    return patterns.some(pattern => pattern.test(input))
  },

  // Check for SQL injection patterns
  hasSQLInjection: (input: string): boolean => {
    const patterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
      /('|(\\')|(;)|(\\))/,
      /(--)/,
      /(\|\|)/,
    ]

    return patterns.some(pattern => pattern.test(input))
  },

  // Validate file upload
  validateFile: (file: File): { valid: boolean; error?: string } => {
    const maxSize = 5 * 1024 * 1024 // 5MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'application/pdf']
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.pdf']

    if (file.size > maxSize) {
      return { valid: false, error: 'File size exceeds 5MB limit' }
    }

    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'File type not allowed' }
    }

    const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
    if (!allowedExtensions.includes(extension)) {
      return { valid: false, error: 'File extension not allowed' }
    }

    return { valid: true }
  },
}
