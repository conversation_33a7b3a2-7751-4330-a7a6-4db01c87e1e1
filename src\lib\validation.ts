import { z } from "zod";

// Common validation patterns
const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
const passwordRegex =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/;

// Base schemas
export const emailSchema = z
  .string()
  .email("Please enter a valid email address");
export const phoneSchema = z
  .string()
  .regex(phoneRegex, "Please enter a valid phone number");
export const passwordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters")
  .regex(
    passwordRegex,
    "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
  );

// Contact form validation
export const contactFormSchema = z.object({
  name: z
    .string()
    .min(2, "Name must be at least 2 characters")
    .max(50, "Name must be less than 50 characters")
    .regex(/^[a-zA-Z\s]+$/, "Name can only contain letters and spaces"),
  email: emailSchema,
  phone: phoneSchema,
  subject: z
    .string()
    .min(5, "Subject must be at least 5 characters")
    .max(100, "Subject must be less than 100 characters")
    .optional(),
  message: z
    .string()
    .min(10, "Message must be at least 10 characters")
    .max(1000, "Message must be less than 1000 characters"),
  propertyId: z.string().optional(),
  source: z.string().optional(),
});

export type ContactFormData = z.infer<typeof contactFormSchema>;

// User registration validation
export const registerSchema = z
  .object({
    firstName: z
      .string()
      .min(2, "First name must be at least 2 characters")
      .max(30, "First name must be less than 30 characters")
      .regex(/^[a-zA-Z]+$/, "First name can only contain letters"),
    lastName: z
      .string()
      .min(2, "Last name must be at least 2 characters")
      .max(30, "Last name must be less than 30 characters")
      .regex(/^[a-zA-Z]+$/, "Last name can only contain letters"),
    email: emailSchema,
    phone: phoneSchema.optional(),
    password: passwordSchema,
    confirmPassword: z.string(),
    agreeToTerms: z.boolean().refine((val) => val === true, {
      message: "You must agree to the terms and conditions",
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export type RegisterFormData = z.infer<typeof registerSchema>;

// Login validation
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, "Password is required"),
  rememberMe: z.boolean().optional(),
});

export type LoginFormData = z.infer<typeof loginSchema>;

// Profile update validation
export const profileUpdateSchema = z.object({
  firstName: z
    .string()
    .min(2, "First name must be at least 2 characters")
    .max(30, "First name must be less than 30 characters"),
  lastName: z
    .string()
    .min(2, "Last name must be at least 2 characters")
    .max(30, "Last name must be less than 30 characters"),
  phone: phoneSchema.optional(),
  avatar: z.string().url().optional(),
});

export type ProfileUpdateData = z.infer<typeof profileUpdateSchema>;

// Search filters validation
export const searchFiltersSchema = z.object({
  propertyTypes: z.array(
    z.enum([
      "apartment",
      "villa",
      "townhouse",
      "penthouse",
      "studio",
      "duplex",
      "commercial",
      "plot",
    ])
  ),
  priceRange: z
    .object({
      min: z.number().min(0),
      max: z.number().min(0),
      currency: z.string(),
    })
    .refine((data) => data.min <= data.max, {
      message: "Minimum price cannot be greater than maximum price",
    }),
  bedrooms: z.array(z.number().min(0).max(10)),
  bathrooms: z.array(z.number().min(0).max(10)),
  areaRange: z
    .object({
      min: z.number().min(0),
      max: z.number().min(0),
      unit: z.enum(["sqft", "sqm"]),
    })
    .refine((data) => data.min <= data.max, {
      message: "Minimum area cannot be greater than maximum area",
    }),
  locations: z.array(z.string()),
  developers: z.array(z.string()),
  amenities: z.array(z.string()),
  features: z.array(z.string()),
  completionDate: z
    .object({
      from: z.string(),
      to: z.string(),
    })
    .optional(),
});

export type SearchFiltersData = z.infer<typeof searchFiltersSchema>;

// Newsletter subscription validation
export const newsletterSchema = z.object({
  email: emailSchema,
  preferences: z
    .object({
      properties: z.boolean().default(true),
      market: z.boolean().default(true),
      news: z.boolean().default(false),
    })
    .optional(),
});

export type NewsletterData = z.infer<typeof newsletterSchema>;

// Property inquiry validation
export const propertyInquirySchema = z.object({
  propertyId: z.string().min(1, "Property ID is required"),
  type: z.enum(["general", "viewing", "pricing", "callback"]),
  name: z.string().min(2, "Name is required"),
  email: emailSchema,
  phone: phoneSchema,
  message: z.string().min(10, "Message must be at least 10 characters"),
  preferredContactTime: z.enum(["morning", "afternoon", "evening"]).optional(),
  preferredContactMethod: z.enum(["email", "phone", "whatsapp"]).optional(),
});

export type PropertyInquiryData = z.infer<typeof propertyInquirySchema>;

// File upload validation
export const fileUploadSchema = z.object({
  file: z
    .instanceof(File)
    .refine(
      (file) => file.size <= 5 * 1024 * 1024,
      "File size must be less than 5MB"
    )
    .refine(
      (file) =>
        ["image/jpeg", "image/png", "image/webp", "application/pdf"].includes(
          file.type
        ),
      "File must be an image (JPEG, PNG, WebP) or PDF"
    ),
  category: z.enum(["avatar", "document", "property_image"]).optional(),
});

export type FileUploadData = z.infer<typeof fileUploadSchema>;

// Validation helper functions
export const validateField = <T>(
  schema: z.ZodSchema<T>,
  value: unknown
): { success: boolean; error?: string; data?: T } => {
  try {
    const data = schema.parse(value);
    return { success: true, data };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors[0]?.message || "Validation failed",
      };
    }
    return { success: false, error: "Validation failed" };
  }
};

export const getFieldErrors = (error: z.ZodError): Record<string, string> => {
  const fieldErrors: Record<string, string> = {};

  error.errors.forEach((err) => {
    const path = err.path.join(".");
    if (path) {
      fieldErrors[path] = err.message;
    }
  });

  return fieldErrors;
};

// Custom validation rules
export const customValidators = {
  // UAE phone number validation
  uaePhone: (value: string) => {
    const uaePhoneRegex = /^(\+971|00971|971)?[0-9]{8,9}$/;
    return uaePhoneRegex.test(value.replace(/\s/g, ""));
  },

  // Emirates ID validation
  emiratesId: (value: string) => {
    const emiratesIdRegex = /^784-[0-9]{4}-[0-9]{7}-[0-9]$/;
    return emiratesIdRegex.test(value);
  },

  // Strong password validation
  strongPassword: (value: string) => {
    const hasUpperCase = /[A-Z]/.test(value);
    const hasLowerCase = /[a-z]/.test(value);
    const hasNumbers = /\d/.test(value);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
    const isLongEnough = value.length >= 8;

    return (
      hasUpperCase &&
      hasLowerCase &&
      hasNumbers &&
      hasSpecialChar &&
      isLongEnough
    );
  },

  // URL validation
  validUrl: (value: string) => {
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  },
};
