import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { Property, SearchFilters, SortOption } from "@/types";
import { api } from "@/lib/api-client";
import { endpoints } from "@/lib/config";
import { showError } from "@/lib/error-handler";

interface PropertyState {
  // Data
  properties: Property[];
  featuredProperties: Property[];
  selectedProperty: Property | null;

  // UI State
  loading: boolean;
  error: string | null;

  // Filters and Search
  filters: SearchFilters;
  searchQuery: string;
  sortOption: SortOption;

  // Pagination
  currentPage: number;
  totalPages: number;
  totalCount: number;

  // Actions
  fetchProperties: (page?: number) => Promise<void>;
  fetchFeaturedProperties: () => Promise<void>;
  fetchPropertyById: (id: string) => Promise<Property | null>;
  searchProperties: (
    query: string,
    filters?: Partial<SearchFilters>
  ) => Promise<void>;
  setFilters: (filters: Partial<SearchFilters>) => void;
  setSortOption: (sort: SortOption) => void;
  setSelectedProperty: (property: Property | null) => void;
  clearFilters: () => void;
  reset: () => void;
}

const defaultFilters: SearchFilters = {
  propertyTypes: [],
  priceRange: { min: 0, max: 10000000, currency: "AED" },
  bedrooms: [],
  bathrooms: [],
  areaRange: { min: 0, max: 10000, unit: "sqft" },
  locations: [],
  developers: [],
  amenities: [],
  features: [],
};

const defaultSortOption: SortOption = {
  field: "createdAt",
  order: "desc",
  label: "Newest First",
};

export const usePropertyStore = create<PropertyState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        properties: [],
        featuredProperties: [],
        selectedProperty: null,
        loading: false,
        error: null,
        filters: defaultFilters,
        searchQuery: "",
        sortOption: defaultSortOption,
        currentPage: 1,
        totalPages: 0,
        totalCount: 0,

        // Actions
        fetchProperties: async (page = 1) => {
          set({ loading: true, error: null });

          try {
            const { filters, sortOption, searchQuery } = get();

            const params = {
              page,
              limit: 12,
              sort: `${sortOption.field}:${sortOption.order}`,
              ...(searchQuery && { search: searchQuery }),
              ...filters,
            };

            const response = await api.get<Property[]>(
              endpoints.properties.list,
              { params }
            );

            set({
              properties: response.data,
              currentPage: response.pagination?.page || 1,
              totalPages: response.pagination?.totalPages || 0,
              totalCount: response.pagination?.total || 0,
              loading: false,
            });
          } catch (error) {
            const errorMessage = "Failed to fetch properties";
            set({ error: errorMessage, loading: false });
            showError(error, errorMessage);
          }
        },

        fetchFeaturedProperties: async () => {
          try {
            const response = await api.get<Property[]>(
              endpoints.properties.featured
            );
            set({ featuredProperties: response.data });
          } catch (error) {
            showError(error, "Failed to fetch featured properties");
          }
        },

        fetchPropertyById: async (id: string) => {
          set({ loading: true, error: null });

          try {
            const response = await api.get<Property>(
              endpoints.properties.detail(id)
            );
            const property = response.data;

            set({ selectedProperty: property, loading: false });
            return property;
          } catch (error) {
            const errorMessage = "Failed to fetch property details";
            set({ error: errorMessage, loading: false });
            showError(error, errorMessage);
            return null;
          }
        },

        searchProperties: async (
          query: string,
          newFilters?: Partial<SearchFilters>
        ) => {
          set({
            searchQuery: query,
            ...(newFilters && { filters: { ...get().filters, ...newFilters } }),
            currentPage: 1,
          });

          await get().fetchProperties(1);
        },

        setFilters: (newFilters: Partial<SearchFilters>) => {
          set({
            filters: { ...get().filters, ...newFilters },
            currentPage: 1,
          });
        },

        setSortOption: (sort: SortOption) => {
          set({ sortOption: sort, currentPage: 1 });
        },

        setSelectedProperty: (property: Property | null) => {
          set({ selectedProperty: property });
        },

        clearFilters: () => {
          set({
            filters: defaultFilters,
            searchQuery: "",
            currentPage: 1,
          });
        },

        reset: () => {
          set({
            properties: [],
            selectedProperty: null,
            loading: false,
            error: null,
            filters: defaultFilters,
            searchQuery: "",
            sortOption: defaultSortOption,
            currentPage: 1,
            totalPages: 0,
            totalCount: 0,
          });
        },
      }),
      {
        name: "property-store",
        partialize: (state) => ({
          filters: state.filters,
          sortOption: state.sortOption,
        }),
      }
    ),
    { name: "PropertyStore" }
  )
);
